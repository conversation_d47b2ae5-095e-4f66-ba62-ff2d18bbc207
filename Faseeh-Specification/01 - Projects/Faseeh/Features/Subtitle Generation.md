#LOGIC 

---

A solution to the lack of video content with accurate subtitles in the source/target language. by using the latest AI models to generate high-quality subtitles for the imported media.
- <span style="color:rgb(146, 208, 80)">**Online Option *(Paid)* :**</span>
	- We might provide a <span style="color:rgb(0, 176, 240)">paid service from the backend</span> to generate subtitles for the imported media.
	- or we can provide an <span style="color:rgb(0, 176, 240)">integration with an existing paid service</span>, so that the user can use it directly from the app by providing an api key for example.
	- Provide integration with <span style="color:rgb(0, 176, 240)">online subtitle databases</span> such as opensubtitles.org, subs.com, etc. 
- <span style="color:rgb(146, 208, 80)">**Offline Option *(Free)* :**</span>
	- We can provide an offline option to <span style="color:rgb(0, 176, 240)">download and setup the models locally</span> on the user's machine.
	- the user can also <span style="color:rgb(0, 176, 240)">import subtitle files manually</span>.