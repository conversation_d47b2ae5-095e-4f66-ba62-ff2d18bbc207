 #LOGIC 

---
A tool to help users improve their pronouciation by analyzing their recordings and reporting the results to them.
- Should support as many languages and accents as possible.
- Should be as accurate as possible by using well known models and algorithms.
- We can change the approach based on the language and the accent.
- The pronounciation is analyzed against either the original media, a reference audio or just the text's phonetics.
- Feedback may vary based on the used approach.
---
![[image-4.png|518x382]]
---
![|286x338](https://lh7-rt.googleusercontent.com/docsz/AD_4nXd_tG4LPfYWmIZTS3l-tD378xBVF60I-xeJd0yTUyDsz1e1xT3hmGVGMwTGuCAdKVjmmBCiGp7SBlOEYrjjem60NE8GEwHwuWMjLByhfJRhkcA2he63XOwIb9aaYgDkSXO_9dOxIA?key=tehuznIwukVhMZFFNY6uP0GJ)
![[image-6.png|333x646]]