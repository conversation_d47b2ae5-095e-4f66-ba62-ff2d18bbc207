#UI

---
Integrated dictionary that allows users to hover over any word in the imported media to get its definition.
- The dictionary sources should be <span style="color:rgb(0, 176, 240)">replaceable</span> to cover different languages, dialects and the preferences of the user.
- Dictionaries sources can be either <span style="color:rgb(146, 208, 80)">online</span> or <span style="color:rgb(255, 0, 0)">offline</span>.
- <span style="color:rgb(0, 176, 240)">Editable</span>, so that user can add his own notes, definitions, etc. which will enhance the <span style="color:rgb(255, 192, 0)">contextualization</span> of the word.
- The layout of the dictionary should be <span style="color:rgb(0, 176, 240)">customizable</span> <span style="color:rgb(0, 176, 240)"></span><span style="color:rgb(0, 176, 240)"></span>by the user, to fit their needs. for example:
	- `definition only.`
	- `definition and the image.`
	- `definition and the translation.`
	- `definition and the pronunciation.`
	- `definition and the example sentence.`
	- `definition and the synonyms.`
	- `definition and the antonyms.`