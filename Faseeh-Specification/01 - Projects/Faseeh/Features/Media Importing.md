#LOGIC 

---
User can import media from different sources and formats such as local videofiles, youtube videos, streamable videos, podcasts, articles, web pages, epubs, pdf (if possible), etc.
- <span style="color:rgb(146, 208, 80)">**Manual import :**</span>
	- user can import any type of media from their <span style="color:rgb(0, 176, 240)">local machine, or clipboard</span>.`(local files, urls, copied text, ...)`
	- this option serves better for content that is <span style="color:rgb(0, 176, 240)">not available online or copyrighted content</span>.
- <span style="color:rgb(146, 208, 80)">**Automatic import :**</span>
	- browser <span style="color:rgb(0, 176, 240)">**extension**</span> that allows users to import media from the web with a <span style="color:rgb(0, 176, 240)">**single click**</span>.
	- <span style="color:rgb(0, 176, 240)">**Convenient**</span> for most of the content available online<sub> *(Checks later for DRM protected content).*</sub>
	- Requires a way to <span style="color:rgb(0, 176, 240)">extract the main content from the page</span> the user is browsing/saving, for example in youtube this would mean the video url and its metadata, in a blog this would mean the article content and its metadata.

---
![[image-7.png]]