#UI

---
This is a mechanism that helps user raise his <span style="color:rgb(146, 208, 80)">familiarity with a language unit</span> <span style="font-style:italic; color:rgb(184, 184, 184)">(word, sentence, idiom, etc)</span>. while having a better <span style="color:rgb(146, 208, 80)">visual feedback on the progress</span>.
- Whenever a user imports a media, the system will automatically generate a familiarity list for each word in the media.
- There will be <span style="color:rgb(0, 176, 240)">different levels of familiarity</span>.
- Items <span style="color:rgb(0, 176, 240)">encountered</span> for the <span style="color:rgb(0, 176, 240)">first time</span> will be maked as <span style="color:rgb(0, 176, 240)">"new"</span>. and the rest will be marked as another level based on the number of times they have been <span style="color:rgb(0, 176, 240)">encountered</span> or <span style="color:rgb(0, 176, 240)">practiced</span>.
- Once the user <span style="color:rgb(0, 176, 240)">finishes consuming the media</span>, all language units will get a <span style="color:rgb(0, 176, 240)">boost</span> in their familiarity level.
-  User can <span style="color:rgb(0, 176, 240)">manually mark langugae units as familiar</span> and the level of familiarity.
- Words are <span style="color:rgb(0, 176, 240)">highlighted</span> in the media based on their familiarity level.
---
![[image-2.png]]
