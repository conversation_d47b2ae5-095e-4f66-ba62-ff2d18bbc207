#LOGIC 

---
Estimate the difficulty of the media on two levels :
- <span style="font-weight:bold; color:rgb(0, 176, 240)">General Level :</span> using known algorithms and heuristics to give a general idea of the difficulty of the text like :
	- <span style="font-weight:bold; color:rgb(146, 208, 80)">Flesch-Kincaid :</span> Readability level based on the number of syllables, words and sentences.
	- <span style="font-weight:bold; color:rgb(146, 208, 80)">Gunning Fog Index :</span> Readability level based on the number of complex words and sentences.
	- <span style="font-weight:bold; color:rgb(146, 208, 80)">Lexical Density :</span> Ratio of content words to function words.
	- etc.
- <span style="font-weight:bold; color:rgb(0, 176, 240)">User-Specific Level : </span> estimate the difficulty of the of the media based on personal data like : 
	- <span style="font-weight:bold; color:rgb(146, 208, 80)">Familiarity with language units :</span> ratio of known words to unknown words. <sub>[[Familiarity System]]</sub>
	- 