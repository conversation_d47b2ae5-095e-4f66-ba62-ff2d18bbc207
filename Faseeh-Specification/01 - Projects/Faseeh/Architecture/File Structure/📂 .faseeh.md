---
Type: Folder
---
# `.faseeh` Directory Overview

> [!Note] Description
> The root directory containing **all** user-specific data, configuration, plugins, and cached information for a particular Faseeh library or instance. It makes the user's entire Faseeh environment self-contained and portable.

## Key Notes

> [!Summary]+ Content:
> - **Core Database:**
>     - [[💾 metadata.sqlite]]: The main SQLite database file containing application metadata.
> - **Subdirectories:** Contains dedicated folders for different categories of data:
>     - [[📂 library]]: All imported user content files, organized by item.
>     - [[🤖 models]]: Downloaded AI/ML models.
>     - [[📂 plugins]]: Installed community plugins.
>     - [[📂 config]]: Core application and plugin configuration files.
> ---
> - **Management:** The existence and overall structure are managed implicitly by the application setup and the [[Storage Service]]. Specific subdirectories and files within are managed by their respective services (Storage Service, Plugin Manager, Model Managers, etc.).
> ---
> - **Location:** Typically located in a user-defined location (selected when creating a "Vault" or library) or a default application data directory. The exact path is crucial and managed internally.
> ---
> - **Backup:** Users wishing to back up their entire Faseeh library should back up this complete directory.

> [!Tip] Core Concept:
> This directory represents the user's "Vault" in Faseeh terminology, holding everything needed for their language learning activities within the app, separate from the application's installed program files.