---
Type: File
---

# `document.json` File Overview

> [!Note] Description
> Stores the **[[Content Adapter#^205d65|FaseehContentDocument]]** object in JSON format. This file represents the main structured content extracted and processed by a [[Content Adapter]] from sources like articles, books, or annotated images. It enables rich display and interaction within the application without needing to re-parse the original source frequently.
## Key Notes

> [!Summary]+ Content:
> - A JSON object adhering to the [[Content Adapter#^205d65|FaseehContentDocument]] interface, containing document metadata, an array of `contentBlocks` (TextBlock, ImageBlock, etc.), and descriptions of assets stored in the corresponding `assets/` subfolder.
> ---
> - **Generated By:** [[Content Adapter]] logic during the `adapt` process.
> ---
> - **Managed By:** Saved and managed by the [[Storage Service]] (Main Process), typically requested by the [[Content Adapter Registry]] after adaptation. Its path is stored in the corresponding `MediaObject` database record. Read by Renderer components (e.g., player/reader UI) via the Storage API to render the content.
> ---
> - **Optionality:** This file only exists for `MediaObjects` where a `Content Adapter` generates structured content (e.g., not typically for simple video/audio files).

> [!Tip] Usage:
> This is the primary source for rendering complex, interactive textual or mixed media content within Faseeh's reader views.