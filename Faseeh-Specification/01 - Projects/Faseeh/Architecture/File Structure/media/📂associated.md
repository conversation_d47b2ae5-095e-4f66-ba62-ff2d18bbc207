---
Type: Folder
---
# `associated` Directory Overview

> [!Note] Description
> Contains supplementary files related to the parent `MediaObject` that are not the primary source file and are not assets embedded within a structured `document.json`. Common examples include subtitles, chapter markers, separate metadata files, or alternative audio tracks.

## Key Notes

> [!Summary]+ Content:
> - Various file types, such as:
>     - Subtitle files (`.srt`, `.vtt`, potentially `.json` if stored structured).
>     - Chapter marker files (`.json`, `.xml`, `.csv`).
>     - Metadata files (`.json`, `.xml`).
>     - Alternative audio/video tracks (`.mp3`, `.m4a`, etc.).
> - Filenames should ideally be descriptive, potentially including type and language codes (e.g., `subtitle_en.srt`, `chapters.json`).
> ---
> - **Generated By:** Provided by [[Content Adapter]] logic within the `ContentAdapterResult.associatedFiles` array.
> ---
> - **Managed By:** Files are saved here by the [[Storage Service]] (Main Process), requested by the [[Content Adapter Registry]] after adaptation. Information about these files (type, language, path) might be stored directly in the `MediaObject` record or a related database table. Read by relevant Renderer components (e.g., Player for subtitles) via the [[Storage API]].
> ---
> - **Optionality:** This folder only exists if the Content Adapter provided supplementary associated files.
