---
Type: Folder
---
# `assets` Directory Overview

> [!Note] Description
> Contains extracted binary asset files (typically images, potentially small audio/video clips) that are directly referenced within the `document.json` ([[Content Adapter#^205d65|FaseehContentDocument]]) file located in the parent directory. These are assets *extracted* or generated during the content adaptation process.
## Key Notes

> [!Summary]+ Content:
> - Binary files (e.g., `.jpg`, `.png`, `.webp`, `.gif`).
> - Filenames typically correspond to the `assetId` keys used within the `document.json`'s `assets` map and `contentBlocks` (e.g., `image1.jpg`, `figureA.png`).
> ---
> - **Generated By:** Extracted or created by [[Content Adapter]] logic during the `adapt` process. The adapter includes the binary data in the `ContentAdapterResult.documentAssets` map.
> ---
> - **Managed By:** Files are saved here by the [[Storage Service]] (Main Process), typically requested by the [[Content Adapter Registry]] after adaptation based on the `documentAssets` map. Paths (relative or absolute) are implicitly managed or explicitly referenced via the `document.json`. Read by Renderer components (UI) via the Storage API when rendering the associated `document.json`.
> ---
> - **Optionality:** This folder only exists if the corresponding `document.json` references embedded assets.

> [!Tip] Distinction:
> This holds assets *extracted from* or *part of* the structured content representation, distinct from the original `source.[ext]` file and supplementary `associated/` files.