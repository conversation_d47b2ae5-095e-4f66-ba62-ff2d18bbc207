{"edges": [{"color": "2", "fromEnd": "arrow", "fromNode": "bf3516ca34dc977d", "fromSide": "top", "id": "af1b94c8fa360e1d", "label": "<PERSON><PERSON>", "styleAttributes": {"path": "dotted"}, "toNode": "e34d7339ae417b52", "toSide": "bottom"}, {"fromEnd": "arrow", "fromNode": "bf3516ca34dc977d", "fromSide": "bottom", "id": "8ed225a08535accd", "styleAttributes": {}, "toNode": "fc60d1a05f8d38cc", "toSide": "top"}, {"fromNode": "5ffcd8634e38ed0b", "fromSide": "right", "id": "e0659e7e9b9bb9af", "label": "Expose", "styleAttributes": {}, "toNode": "e34d7339ae417b52", "toSide": "left"}, {"fromNode": "e34d7339ae417b52", "fromSide": "top", "id": "b7363fb153cf7aaa", "styleAttributes": {"pathfindingMethod": "square"}, "toNode": "432422e1d0529c55", "toSide": "bottom"}, {"fromNode": "332aa080cce2eed6", "fromSide": "left", "id": "1f8f169fd97c6fd9", "label": "faseeh://URI Payload", "styleAttributes": {}, "toNode": "9665c30f235f1a68", "toSide": "right"}, {"fromNode": "1c505257ff0aee8a", "fromSide": "right", "id": "3a0e01c77f2fc729", "styleAttributes": {}, "toNode": "4154be6dc442e3a5", "toSide": "left"}, {"color": "1", "fromNode": "4154be6dc442e3a5", "fromSide": "right", "id": "4bb09bed8bd00e8e", "label": "Notify", "styleAttributes": {"path": "short-dashed", "pathfindingMethod": "square"}, "toNode": "5db27de382906b00", "toSide": "bottom"}, {"fromNode": "e34d7339ae417b52", "fromSide": "top", "id": "00fc0134cd7760a6", "styleAttributes": {"pathfindingMethod": null}, "toNode": "8a7ed12ee4a40e01", "toSide": "bottom"}, {"color": "5", "fromNode": "1e3d102e40ad4ec7", "fromSide": "left", "id": "aab9b1cc962f0b94", "styleAttributes": {"pathfindingMethod": "square"}, "toNode": "11839f22a7b0a478", "toSide": "right"}, {"color": "5", "fromNode": "5db27de382906b00", "fromSide": "left", "id": "d86b13d1a6b5218f", "label": "Manage", "styleAttributes": {"pathfindingMethod": "direct"}, "toNode": "41603c940322feed", "toSide": "right"}, {"color": "3", "fromNode": "11839f22a7b0a478", "fromSide": "left", "id": "7feb0b5f449a000c", "styleAttributes": {"path": "dotted", "pathfindingMethod": "square"}, "toNode": "41603c940322feed", "toSide": "top"}, {"color": "3", "fromNode": "11839f22a7b0a478", "fromSide": "left", "id": "c8296418169e97fe", "styleAttributes": {"path": "dotted", "pathfindingMethod": "square"}, "toNode": "8eb092032c9d8ebb", "toSide": "bottom"}, {"color": "5", "fromNode": "701e3c869bd56b72", "fromSide": "bottom", "id": "bdfb1761f1a5edbb", "styleAttributes": {"pathfindingMethod": "direct"}, "toNode": "8eb092032c9d8ebb", "toSide": "top"}, {"color": "4", "fromNode": "41603c940322feed", "fromSide": "top", "id": "8a4b660f8b0ca52d", "styleAttributes": {"path": "dotted", "pathfindingMethod": "direct"}, "toNode": "8eb092032c9d8ebb", "toSide": "bottom"}, {"color": "5", "fromNode": "2ab80a55f2db71ac", "fromSide": "bottom", "id": "9e388df6ec208adb", "styleAttributes": {"pathfindingMethod": "direct"}, "toNode": "d886d148c44dcecb", "toSide": "top"}, {"color": "3", "fromNode": "11839f22a7b0a478", "fromSide": "left", "id": "0ddee8d51986cac8", "styleAttributes": {"path": "dotted", "pathfindingMethod": "square"}, "toNode": "d886d148c44dcecb", "toSide": "bottom"}, {"color": "4", "fromNode": "41603c940322feed", "fromSide": "top", "id": "6c4249a44026b10f", "label": "Use", "styleAttributes": {"path": "dotted", "pathfindingMethod": "direct"}, "toNode": "d886d148c44dcecb", "toSide": "bottom"}, {"color": "4", "fromNode": "41603c940322feed", "fromSide": "left", "id": "c4cfea8f126662af", "label": "Use", "styleAttributes": {"path": "dotted", "pathfindingMethod": "square"}, "toNode": "c54fcfb0d29c7033", "toSide": "right"}, {"color": "1", "fromNode": "9665c30f235f1a68", "fromSide": "left", "id": "031f774ead315401", "label": "Notify", "styleAttributes": {"path": "short-dashed"}, "toNode": "e2db9918931c37fb", "toSide": "right"}, {"color": "1", "fromNode": "e2db9918931c37fb", "fromSide": "left", "id": "534f1986eb84cda1", "styleAttributes": {"path": "short-dashed", "pathfindingMethod": "square"}, "toNode": "5db27de382906b00", "toSide": "bottom"}, {"color": "2", "fromNode": "1e3d102e40ad4ec7", "fromSide": "top", "id": "87bfbe302907d07f", "label": "create", "styleAttributes": {"path": "long-dashed", "pathfindingMethod": "direct"}, "toNode": "396a4c25cab384ef", "toSide": "bottom"}, {"color": "5", "fromNode": "39bb655a16fcdc24", "fromSide": "bottom", "id": "0da89ace8d8ca975", "styleAttributes": {"pathfindingMethod": "direct"}, "toNode": "25adcbde25664ddb", "toSide": "top"}, {"color": "3", "fromNode": "11839f22a7b0a478", "fromSide": "left", "id": "466c1b7db5f2aa1a", "label": "Register", "styleAttributes": {"path": "dotted", "pathfindingMethod": "square"}, "toNode": "25adcbde25664ddb", "toSide": "bottom"}, {"color": "4", "fromNode": "41603c940322feed", "fromSide": "top", "id": "f344466caf5102f8", "label": "Use", "styleAttributes": {"path": "dotted", "pathfindingMethod": "direct"}, "toNode": "25adcbde25664ddb", "toSide": "bottom"}, {"color": "6", "fromNode": "396a4c25cab384ef", "fromSide": "left", "id": "0675e1fb37b9b705", "label": "Passed To", "styleAttributes": {}, "toNode": "11839f22a7b0a478", "toSide": "top"}], "nodes": [{"color": "#005fdb", "height": 1225, "id": "5b65443058bb82c8", "label": "Client", "styleAttributes": {}, "type": "group", "width": 2460, "x": -540, "y": -1044}, {"color": "#ffae00", "height": 1124, "id": "f9add3dd533084d9", "label": "Renderer Process", "styleAttributes": {}, "type": "group", "width": 1200, "x": -480, "y": -988}, {"color": "4", "height": 1124, "id": "7c5ab06fc9b59852", "label": "Main Process", "styleAttributes": {}, "type": "group", "width": 1097, "x": 743, "y": -988}, {"color": "#ffffff", "height": 584, "id": "8a7ed12ee4a40e01", "label": "📂.faseeh/", "styleAttributes": {}, "type": "group", "width": 981, "x": 639, "y": -1676}, {"color": "1", "height": 138, "id": "c6f12ba5ba4199ea", "label": "Server", "styleAttributes": {}, "type": "group", "width": 2460, "x": -540, "y": 232}, {"color": "3", "height": 542, "id": "b6019a90a892fe42", "label": "Backlog", "styleAttributes": {}, "type": "group", "width": 300, "x": -882, "y": -1044}, {"color": "#6069af", "height": 334, "id": "67658b27f9c6a202", "label": "🧩Plugins/plugin-id", "styleAttributes": {}, "type": "group", "width": 359, "x": 961, "y": -1534}, {"color": "#6069af", "height": 412, "id": "67f844e8b920fd96", "label": "📂 Library", "styleAttributes": {}, "type": "group", "width": 270, "x": 669, "y": -1534}, {"color": "#ff94e2", "height": 126, "id": "aca8f4be38f5e593", "label": "Event System", "styleAttributes": {}, "type": "group", "width": 870, "x": -450, "y": -11}, {"color": "#6069af", "height": 261, "id": "edf545036cccc5e4", "label": "📂config/", "styleAttributes": {}, "type": "group", "width": 249, "x": 1343, "y": -1534}, {"height": 60, "id": "f0fb59ab3d757cf7", "styleAttributes": {"textAlign": "center"}, "text": "[[📂 config|📌 Overview]]", "type": "text", "width": 213, "x": 1361, "y": -1513}, {"height": 60, "id": "fdf9b7e2ac7a66b5", "styleAttributes": {"textAlign": "center"}, "text": "[[⚙️settings.json]]", "type": "text", "width": 213, "x": 1361, "y": -1436}, {"height": 60, "id": "45a58855d0994a67", "styleAttributes": {"textAlign": "center"}, "text": "[[⚙️ enabled-plugins.json]]", "type": "text", "width": 213, "x": 1361, "y": -1357}, {"height": 60, "id": "5d631c31e80adb2d", "styleAttributes": {"textAlign": "center"}, "text": "[[📂assets|📂assets/]]", "type": "text", "width": 229, "x": 690, "y": -1278}, {"height": 60, "id": "11f010fdbf0a71d7", "styleAttributes": {"textAlign": "center"}, "text": "[[📄 Document.json]]", "type": "text", "width": 229, "x": 690, "y": -1357}, {"color": "#6069af", "height": 66, "id": "e90a147db200c8ff", "styleAttributes": {"textAlign": "center"}, "text": "[[🤖 models|🤖 models/]]", "type": "text", "width": 359, "x": 961, "y": -1188}, {"height": 60, "id": "97c2d0edf1f3305c", "styleAttributes": {"textAlign": "center"}, "text": "[[📂associated|📂associated/]]", "type": "text", "width": 229, "x": 690, "y": -1198}, {"height": 60, "id": "2a6f17d5ed1ac077", "styleAttributes": {"textAlign": "center"}, "text": "[[📄 data.json]]", "type": "text", "width": 307, "x": 983, "y": -1278}, {"height": 60, "id": "0a32caa5ddeba3ad", "styleAttributes": {"textAlign": "center"}, "text": "[[📄 manifest.json]]", "type": "text", "width": 307, "x": 983, "y": -1357}, {"height": 60, "id": "42a7613309410873", "styleAttributes": {"textAlign": "center"}, "text": "[[📂 plugins|📌 Overview]]", "type": "text", "width": 307, "x": 983, "y": -1513}, {"height": 60, "id": "ec4b0443409f40e7", "styleAttributes": {"textAlign": "center"}, "text": "[[📄 main.js]]", "type": "text", "width": 307, "x": 983, "y": -1434}, {"height": 60, "id": "81a98eb29968210c", "styleAttributes": {}, "text": "", "type": "text", "width": 260, "x": -272, "y": -1432}, {"height": 60, "id": "7e687699fc722d9e", "styleAttributes": {"textAlign": "center"}, "text": "[[📄 source.ext|📄 source.⟬ext⟭]]", "type": "text", "width": 229, "x": 690, "y": -1436}, {"color": "#6069af", "height": 65, "id": "f8a87aa17bb1a54c", "styleAttributes": {"textAlign": "center"}, "text": "[[📂 .faseeh|📌 Overview]]", "type": "text", "width": 939, "x": 662, "y": -1653}, {"height": 60, "id": "76e112eb6225c22c", "styleAttributes": {"textAlign": "center"}, "text": "[[📂 media|📌 Overview]]", "type": "text", "width": 229, "x": 691, "y": -1513}, {"color": "#6069af", "height": 138, "id": "432422e1d0529c55", "styleAttributes": {"textAlign": "center"}, "text": "**[[💾 metadata.sqlite]]**", "type": "text", "width": 249, "x": 1343, "y": -1260}, {"color": "6", "height": 60, "id": "bf3516ca34dc977d", "styleAttributes": {"textAlign": "center"}, "text": "**Core  Service**\n[[Sync Service]]", "type": "text", "width": 260, "x": 1001, "y": 15}, {"color": "2", "height": 78, "id": "da4cb0e07a73c78a", "styleAttributes": {"textAlign": "center"}, "text": "**Core  Utility**\n[[Listener Tracker]]", "type": "text", "width": 260, "x": 131, "y": 15}, {"color": "#ffffff", "height": 78, "id": "db6d65cadb3776c5", "styleAttributes": {"textAlign": "center"}, "text": "[[Event System|📌 Overview]]", "type": "text", "width": 260, "x": -424, "y": 15}, {"color": "6", "height": 60, "id": "508cf4a04654e8fd", "styleAttributes": {"textAlign": "center"}, "text": "Subtitle Generation", "type": "text", "width": 260, "x": 420, "y": 277}, {"color": "6", "height": 60, "id": "fc60d1a05f8d38cc", "styleAttributes": {"textAlign": "center"}, "text": "**Core**\n[[Sync Service]]", "type": "text", "width": 260, "x": 1001, "y": 277}, {"color": "6", "height": 60, "id": "5cae46ef93724f83", "styleAttributes": {"textAlign": "center"}, "text": "OCR Detector", "type": "text", "width": 260, "x": 705, "y": 277}, {"color": "2", "height": 78, "id": "9b0da856ef24d878", "styleAttributes": {"textAlign": "center"}, "text": "**Core  Utility**\n[[Event Emitter Wrapper]]", "type": "text", "width": 260, "x": -149, "y": 15}, {"color": "3", "height": 60, "id": "8eb092032c9d8ebb", "styleAttributes": {"textAlign": "center"}, "text": "**Abstract Class**\n[[OCR Engine]]", "type": "text", "width": 166, "x": -122, "y": -729}, {"color": "2", "height": 66, "id": "396a4c25cab384ef", "styleAttributes": {"textAlign": "center"}, "text": "**Core API Objec**\n[[FaseehApp]]", "type": "text", "width": 159, "x": 468, "y": -669}, {"color": "6", "height": 60, "id": "e34d7339ae417b52", "styleAttributes": {"textAlign": "center"}, "text": "**Core  Service**\n[[Storage Service]]", "type": "text", "width": 210, "x": 1026, "y": -592}, {"color": "6", "height": 85, "id": "9665c30f235f1a68", "styleAttributes": {"textAlign": "center"}, "text": "**Core  Service**\n[[WebClip Import Service]]", "type": "text", "width": 260, "x": 1550, "y": -183}, {"color": "3", "height": 60, "id": "11839f22a7b0a478", "styleAttributes": {"textAlign": "center"}, "text": "\n[[Base Plugin|Plugin]]", "type": "text", "width": 129, "x": 285, "y": -502}, {"color": "#2432ff", "height": 190, "id": "e2db9918931c37fb", "styleAttributes": {"textAlign": "center"}, "text": "**IPC**\n[[Event Translator]]", "type": "text", "width": 160, "x": 652, "y": -236}, {"color": "6", "height": 60, "id": "5db27de382906b00", "styleAttributes": {"textAlign": "center"}, "text": "**Core Service**\n[[Content Adapter Registry]]", "type": "text", "width": 205, "x": 195, "y": -341}, {"color": "3", "height": 60, "id": "d886d148c44dcecb", "styleAttributes": {"textAlign": "center"}, "text": "**Abstract Function**\n[[Text Tokenizer]]", "type": "text", "width": 166, "x": -343, "y": -729}, {"color": "6", "height": 70, "id": "1e3d102e40ad4ec7", "styleAttributes": {"textAlign": "center"}, "text": "**Core Service**\n[[Plugin Manager]]", "type": "text", "width": 159, "x": 468, "y": -507}, {"color": "3", "height": 60, "id": "25adcbde25664ddb", "styleAttributes": {"textAlign": "center"}, "text": "**Abstract Class**\n[[Subtitle Engine]]", "type": "text", "width": 156, "x": 102, "y": -729}, {"color": "#2432ff", "height": 190, "id": "5ffcd8634e38ed0b", "styleAttributes": {"textAlign": "center"}, "text": "**IPC/Global Object**\n[[Storage API]]", "type": "text", "width": 160, "x": 652, "y": -657}, {"color": "3", "height": 60, "id": "41603c940322feed", "styleAttributes": {"textAlign": "center"}, "text": "**Abstract Class**\n[[Content Adapter]]", "type": "text", "width": 188, "x": -133, "y": -341}, {"color": "5", "height": 60, "id": "1c505257ff0aee8a", "styleAttributes": {"textAlign": "center"}, "text": "**File**", "type": "text", "width": 120, "x": -698, "y": -167}, {"color": "6", "height": 60, "id": "4730d27a85423154", "styleAttributes": {"textAlign": "center"}, "text": "Auth Service", "type": "text", "width": 260, "x": 1290, "y": -10}, {"height": 60, "id": "beeb365e8e2526a6", "styleAttributes": {}, "text": "", "type": "text", "width": 260, "x": 920, "y": -640}, {"color": "6", "height": 78, "id": "4154be6dc442e3a5", "styleAttributes": {"textAlign": "center"}, "text": "**Core  Service**\n[[Manual Import Service]]", "type": "text", "width": 260, "x": -389, "y": -176}, {"color": "6", "height": 60, "id": "701e3c869bd56b72", "styleAttributes": {"textAlign": "center"}, "text": "**Core Service**\n[[OCR Registry Service]]", "type": "text", "width": 208, "x": -143, "y": -830}, {"color": "6", "height": 60, "id": "39bb655a16fcdc24", "styleAttributes": {"textAlign": "center"}, "text": "**Core Service**\n[[Subtitle Engine Registry]]", "type": "text", "width": 210, "x": 75, "y": -830}, {"color": "6", "height": 60, "id": "2ab80a55f2db71ac", "styleAttributes": {"textAlign": "center"}, "text": "**Core Service**\n[[Text Tokenizer Registery]]", "type": "text", "width": 223, "x": -372, "y": -830}, {"color": "6", "height": 60, "id": "c54fcfb0d29c7033", "styleAttributes": {"textAlign": "center"}, "text": "**Core Service**\n[[Language Detector]]", "type": "text", "width": 195, "x": -434, "y": -341}, {"color": "6", "height": 60, "id": "798a1c976f497193", "styleAttributes": {"textAlign": "center"}, "text": "Settings Service", "type": "text", "width": 260, "x": -862, "y": -1014}, {"color": "5", "height": 60, "id": "332aa080cce2eed6", "styleAttributes": {"textAlign": "center"}, "text": "Extension", "type": "text", "width": 120, "x": 2234, "y": -170}, {"color": "#c0fe9f", "height": 92, "id": "821c59cec5c05514", "styleAttributes": {"textAlign": "center"}, "text": "**Main Entry Point/Main Lifcycle**\n📄 [[main.ts]]", "type": "text", "width": 1025, "x": 777, "y": -954}, {"color": "#ffd285", "height": 92, "id": "a0bb7124f24a8f6a", "styleAttributes": {"textAlign": "center"}, "text": "**Renderer Entry Point/ Renderer Lifcycle**\n[[📄 renderer.ts|📌 Overview]]", "type": "text", "width": 1140, "x": -450, "y": -954}]}