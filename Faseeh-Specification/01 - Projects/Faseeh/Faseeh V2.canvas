{"edges": [{"fromNode": "b6a95d02a5fb4a90", "fromSide": "right", "id": "4fe2e4e4216d74c2", "styleAttributes": {}, "toNode": "1701eacd2981d8a6", "toSide": "left"}, {"fromNode": "b6a95d02a5fb4a90", "fromSide": "bottom", "id": "116d5892085cf794", "styleAttributes": {"pathfindingMethod": null}, "toNode": "6be23294cf4a541f", "toSide": "top"}, {"fromNode": "4c825855b06cbd83", "fromSide": "top", "id": "d656748e4817b397", "label": "use", "styleAttributes": {"path": "short-dashed", "pathfindingMethod": "square"}, "toNode": "76d25ae9966465bc", "toSide": "bottom"}, {"color": "3", "fromNode": "7c109e0cc3bbf01b", "fromSide": "bottom", "id": "0254631feafc658b", "label": "Might Use", "styleAttributes": {"path": "short-dashed", "pathfindingMethod": null}, "toNode": "80815854332adf97", "toSide": "bottom"}, {"fromNode": "76d25ae9966465bc", "fromSide": "bottom", "id": "2f0ff715fb34645d", "label": "requires", "styleAttributes": {"path": "short-dashed", "pathfindingMethod": null}, "toNode": "55a4478ba3cb6b30", "toSide": "top"}, {"color": "5", "fromNode": "80815854332adf97", "fromSide": "bottom", "id": "1d4883f30095b5b9", "label": "Use", "styleAttributes": {"path": "short-dashed"}, "toNode": "cba3f9eedf26c915", "toSide": "bottom"}, {"fromNode": "80815854332adf97", "fromSide": "right", "id": "ed07f2f7c46afa20", "label": "use", "styleAttributes": {"path": "short-dashed", "pathfindingMethod": null}, "toNode": "76d25ae9966465bc", "toSide": "right"}, {"fromNode": "b6a95d02a5fb4a90", "fromSide": "left", "id": "6c216dd374b988b4", "styleAttributes": {}, "toNode": "2f9e261ac34a239c", "toSide": "right"}, {"color": "2", "fromNode": "0ff764854905f3d2", "fromSide": "bottom", "id": "776e45faaaed7542", "styleAttributes": {}, "toNode": "e8ddb0b98a05624d", "toSide": "top"}, {"color": "2", "fromNode": "e8ddb0b98a05624d", "fromSide": "bottom", "id": "a8c884f6d638cb91", "styleAttributes": {"pathfindingMethod": "square"}, "toNode": "6a3b468f6b51fff9", "toSide": "top"}, {"color": "2", "fromNode": "e8ddb0b98a05624d", "fromSide": "bottom", "id": "2a949764b0126615", "styleAttributes": {"pathfindingMethod": "square"}, "toNode": "12d7dc45a3325599", "toSide": "top"}, {"color": "2", "fromNode": "e8ddb0b98a05624d", "fromSide": "bottom", "id": "9444251753213ed4", "styleAttributes": {}, "toNode": "b1a430432ded3012", "toSide": "top"}, {"fromNode": "6a3b468f6b51fff9", "fromSide": "bottom", "id": "ebb06a047be215be", "styleAttributes": {"pathfindingMethod": "square"}, "toNode": "40a7d8f09bf2cb16", "toSide": "top"}, {"fromNode": "b6a95d02a5fb4a90", "fromSide": "top", "id": "559fbf8b0a443a14", "styleAttributes": {}, "toNode": "69a684d84b6f3223", "toSide": "bottom"}], "nodes": [{"color": "1", "height": 2220, "id": "6be23294cf4a541f", "label": "Features", "styleAttributes": {"border": null}, "type": "group", "width": 2000, "x": -960, "y": -720}, {"color": "4", "height": 1080, "id": "2f9e261ac34a239c", "label": "UI Structue", "styleAttributes": {}, "type": "group", "width": 1480, "x": -2560, "y": -1240}, {"file": "01 - Projects/Faseeh/UI/UI Structure.md", "height": 620, "id": "7379e4d7b28ea41a", "styleAttributes": {}, "type": "file", "width": 320, "x": -3260, "y": -1400}, {"color": "2", "height": 60, "id": "0ff764854905f3d2", "styleAttributes": {"textAlign": "center"}, "text": "<PERSON>", "type": "text", "width": 260, "x": -1900, "y": -1190}, {"color": "2", "height": 60, "id": "e8ddb0b98a05624d", "styleAttributes": {"textAlign": "center"}, "text": "Authentication", "type": "text", "width": 260, "x": -1900, "y": -1080}, {"color": "2", "height": 60, "id": "6a3b468f6b51fff9", "styleAttributes": {"textAlign": "center"}, "text": "Dashboard", "type": "text", "width": 260, "x": -2280, "y": -940}, {"height": 60, "id": "40a7d8f09bf2cb16", "styleAttributes": {"shape": null, "textAlign": "center"}, "text": "**Library**\n<sup>`(Imported Media, Collections, ...)`</sup>", "type": "text", "width": 260, "x": -2440, "y": -800}, {"color": "#0062ff", "file": "01 - Projects/Faseeh/Architecture/Architecture.canvas", "height": 1538, "id": "69a684d84b6f3223", "styleAttributes": {}, "type": "file", "width": 2000, "x": -960, "y": -2639}, {"color": "2", "height": 60, "id": "b1a430432ded3012", "styleAttributes": {"textAlign": "center"}, "text": "Discovery", "type": "text", "width": 260, "x": -1900, "y": -940}, {"color": "2", "height": 60, "id": "12d7dc45a3325599", "styleAttributes": {"textAlign": "center"}, "text": "Settings", "type": "text", "width": 260, "x": -1500, "y": -940}, {"color": "#4785ff", "height": 60, "id": "b6a95d02a5fb4a90", "styleAttributes": {"border": null, "textAlign": "center"}, "text": "## **FASEEH** : Language Learning Toolkit", "type": "text", "width": 480, "x": -200, "y": -960}, {"color": "3", "height": 225, "id": "1701eacd2981d8a6", "styleAttributes": {}, "text": "### <span style=\"color:rgb(255, 192, 0)\">**Goals**</span>\n---\n- Overcome the problem of boring language learning materials.\n- Centralize the all the language learning tools in one app with small to no setup.\n- Provide a convenient solution for the user by its variety of tools.\n- Provide a free alternative to existing paid solutions.", "type": "text", "width": 540, "x": 376, "y": -1042}, {"color": "6", "file": "01 - Projects/Faseeh/Features/Subtitle Generation.md", "height": 600, "id": "073b691124ba503a", "styleAttributes": {}, "type": "file", "width": 560, "x": -320, "y": -660}, {"color": "6", "file": "01 - Projects/Faseeh/Features/Media Importing.md", "height": 600, "id": "22df955b3fbae804", "styleAttributes": {}, "type": "file", "width": 560, "x": -920, "y": -660}, {"color": "6", "file": "01 - Projects/Faseeh/Features/Advanced Media Player-Reader.md", "height": 600, "id": "76d25ae9966465bc", "styleAttributes": {}, "type": "file", "width": 560, "x": 280, "y": -660}, {"color": "6", "file": "01 - Projects/Faseeh/Features/Language Units Parser.md", "height": 600, "id": "55a4478ba3cb6b30", "styleAttributes": {}, "type": "file", "width": 560, "x": -320, "y": 60}, {"color": "6", "file": "01 - Projects/Faseeh/Features/Data Organization.md", "height": 560, "id": "a922d8cccb8824b4", "styleAttributes": {}, "type": "file", "width": 560, "x": 1240, "y": 80}, {"color": "6", "file": "01 - Projects/Faseeh/Features/Pronunciation Checker.md", "height": 600, "id": "51f6c1824b2ab78e", "styleAttributes": {}, "type": "file", "width": 560, "x": -920, "y": 60}, {"color": "6", "file": "01 - Projects/Faseeh/Features/Mouse-over Dictionary.md", "height": 600, "id": "4c825855b06cbd83", "styleAttributes": {}, "type": "file", "width": 560, "x": 280, "y": 60}, {"color": "6", "file": "01 - Projects/Faseeh/Features/Spaced Repetition System.md", "height": 560, "id": "cba3f9eedf26c915", "styleAttributes": {}, "type": "file", "width": 560, "x": -920, "y": 780}, {"color": "6", "file": "01 - Projects/Faseeh/Features/Familiarity System.md", "height": 560, "id": "80815854332adf97", "styleAttributes": {}, "type": "file", "width": 560, "x": 280, "y": 780}, {"color": "6", "file": "01 - Projects/Faseeh/Features/Difficulty Estimation.md", "height": 560, "id": "7c109e0cc3bbf01b", "styleAttributes": {}, "type": "file", "width": 560, "x": -320, "y": 780}]}