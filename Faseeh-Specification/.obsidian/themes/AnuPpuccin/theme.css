@charset "UTF-8";
/* @settings
name: AnuPpuccin
id: anuppuccin-theme-settings
settings:

# Colors

  -
    id: anp-colors-section-header
    title: Colors
    description: Theme Accent and Flavors, Color Overrides, Extended Color Schemes
    type: heading
    level: 1
    collapsed: true
  -
    id: anuppuccin-theme-light
    title: Light Theme Flavor
    description: Select your preferred light mode flavor
    type: class-select
    allowEmpty: false
    default: ctp-latte
    options:
      - 
        label: Latte
        value: ctp-latte
      - 
        label: Rosé Pine
        value: ctp-rosepine-light
  -
    id: anuppuccin-theme-dark
    title: Dark Theme Flavor
    description: Select your preferred dark mode flavor
    type: class-select
    allowEmpty: false
    default: ctp-mocha
    options:
      - 
        label: Frappe
        value: ctp-frappe
      - 
        label: Macchiato
        value: ctp-macchiato
      - 
        label: Mocha
        value: ctp-mocha
      - 
        label: Mocha Old
        value: ctp-mocha-old
  -
    id: anuppuccin-light-theme-accents
    title: Light Theme Accent
    description: Select your preferred light theme accent (Defaults to dark theme accent if left empty)
    type: class-select
    allowEmpty: true
    default: none
    options:
      - 
        label: Rosewater
        value: ctp-accent-light-rosewater
      - 
        label: Flamingo
        value: ctp-accent-light-flamingo
      - 
        label: Pink
        value: ctp-accent-light-pink
      - 
        label: Mauve
        value: ctp-accent-light-mauve
      - 
        label: Red
        value: ctp-accent-light-red
      - 
        label: Maroon
        value: ctp-accent-light-maroon
      - 
        label: Peach
        value: ctp-accent-light-peach
      - 
        label: Yellow
        value: ctp-accent-light-yellow
      - 
        label: Green
        value: ctp-accent-light-green
      - 
        label: Teal
        value: ctp-accent-light-teal
      - 
        label: Sky
        value: ctp-accent-light-sky
      - 
        label: Sapphire
        value: ctp-accent-light-sapphire
      - 
        label: Blue
        value: ctp-accent-light-blue
      - 
        label: Lavender
        value: ctp-accent-light-lavender
  -
    id: anuppuccin-theme-accents
    title: Dark Theme Accent
    description: Select your preferred accent
    type: class-select
    allowEmpty: false
    default: ctp-accent-rosewater
    options:
      - 
        label: Rosewater
        value: ctp-accent-rosewater
      - 
        label: Flamingo
        value: ctp-accent-flamingo
      - 
        label: Pink
        value: ctp-accent-pink
      - 
        label: Mauve
        value: ctp-accent-mauve
      - 
        label: Red
        value: ctp-accent-red
      - 
        label: Maroon
        value: ctp-accent-maroon
      - 
        label: Peach
        value: ctp-accent-peach
      - 
        label: Yellow
        value: ctp-accent-yellow
      - 
        label: Green
        value: ctp-accent-green
      - 
        label: Teal
        value: ctp-accent-teal
      - 
        label: Sky
        value: ctp-accent-sky
      - 
        label: Sapphire
        value: ctp-accent-sapphire
      - 
        label: Blue
        value: ctp-accent-blue
      - 
        label: Lavender
        value: ctp-accent-lavender
  -
    id: anuppuccin-accent-toggle
    title: Force Custom Accents
    description: Forces colorscheme-specific custom accents to be utilised instead of default obsidian accent
    type: class-toggle
    default: true
  -
    id: anuppuccin-url-extended-colorschemes
    title: Extended Color Schemes Snippet
    description: "Download the snippet by clicking on [this link](https://github.com/AnubisNekhet/AnuPpuccin/blob/main/snippets/extended-colorschemes.css)."
    type: info-text
    markdown: true

# Colors :: Color Overrides

  -
    id: anp-color-overrides-header
    title: Color Overrides
    description: 
    type: heading
    level: 2
    collapsed: true
  -
    id: ctp-custom-rosewater
    title: Rosewater
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-flamingo
    title: Flamingo
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-pink
    title: Pink
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-mauve
    title: Mauve
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-red
    title: Red
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-maroon
    title: Maroon
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-peach
    title: Peach
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-yellow
    title: Yellow
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-green
    title: Green
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-teal
    title: Teal
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-sky
    title: Sky
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-sapphire
    title: Sapphire
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-blue
    title: Blue
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-lavender
    title: Lavender
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-text
    title: Text
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-subtext1
    title: Subtext 1
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-subtext0
    title: Subtext0
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-overlay2
    title: Overlay 2
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-overlay1
    title: Overlay 1
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-overlay0
    title: Overlay 0
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-surface2
    title: Surface 2
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-surface1
    title: Surface 1
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-surface0
    title: Surface 0
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-base
    title: Base
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-mantle
    title: Mantle
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'
  -
    id: ctp-custom-crust
    title: Crust
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'rgb-values'

# File Editor

  -
    id: anp-editor-header
    title: File Editor & Markdown Elements
    description: Callouts, Checkboxes, Codeblocks, Lists, Tables, Tags, etc.
    type: heading
    level: 1
    collapsed: true
  -
    id: anp-active-line
    title: Active line highlight
    type: class-select
    allowEmpty: false
    default: anp-no-highlight
    options:
      -
        label: None
        value: anp-no-highlight
      -
        label: Highlight
        value: anp-current-line
      -
        label: Highlight + Border
        value: anp-current-line-border
      -
        label: Border Only
        value: anp-current-line-border-only

# File Editor & Markdown Elements :: Callouts
  -
    id: anp-callout-header
    title: Callouts
    description: Callout styles, border radius, custom colors
    type: heading
    level: 2
    collapsed: true
  -
    id: anp-callout-select
    title: Callout Style
    type: class-select
    default: none
    allowEmpty: false
    options:
      - 
        label: Default
        value: none
      - 
        label: Sleek
        value: anp-callout-sleek
      - 
        label: Block
        value: anp-callout-block
      - 
        label: Vanilla Normal
        value: anp-callout-vanilla-normal
      - 
        label: Vanilla Plus
        value: anp-callout-vanilla-plus
  -
    id: anp-callout-color-toggle
    title: Enable Custom Callout Colors
    type: class-toggle
  -
    id: callout-radius
    title: Callout Radius
    type: variable-number
    default: 4
    format: px
  -
    id: callout-title-padding
    title: Callout Title Padding
    type: variable-text
    default: 8px
  -
    id: callout-title-opacity
    title: Callout Title Background Opacity
    description: Applies to Sleek, Vanilla and Vanilla Plus callouts
    type: variable-number-slider
    default: 0.1
    min: 0
    max: 1
    step: 0.05
  -
    id: callout-content-padding
    title: Callout Content Padding
    type: variable-text
    default: 8px
  -
    id: anp-callout-fold-position
    title: Callout Fold Position
    type: variable-select
    default: '0'
    options:
      - 
        label: Left
        value: 0
      - 
        label: Right
        value: 1

# File Editor & Markdown Elements :: Checkboxes

  -
    id: anp-custom-checkboxes-header
    title: Checkboxes
    description: Custom checkboxes and speech bubbles
    type: heading
    level: 2
    collapsed: true
  -
    id: anp-custom-checkboxes
    title: Enable Custom Checkboxes
    type: class-toggle
  -
    id: anp-speech-bubble
    title: Enable Speech Bubbles
    type: class-toggle

# File Editor & Markdown Elements :: Codeblocks

  -
    id: anp-codeblock-header
    title: Codeblocks
    description: Codeblock wrapping, line numbering
    type: heading
    level: 2
    collapsed: true
  -
    id: anp-codeblock-numbers
    title: Enable Codeblock Numbering
    type: class-toggle
  -
    id: anp-codeblock-wrap-edit
    title: Codeblock Line Wrap (Edit Mode)
    description: Does not work for codeblocks with syntax highlighting
    type: class-select
    default: none
    options:
      -
        label: Wrap
        value: none
      -
        label: No Wrap
        value: anp-codeblock-edit-nowrap
  -
    id: anp-codeblock-wrap-preview
    title: Codeblock Line Wrap (Preview Mode)
    description: Does not work for codeblocks with syntax highlighting
    type: class-select
    default: none
    options:
      -
        label: Wrap
        value: none
      -
        label: No Wrap
        value: anp-codeblock-preview-nowrap
  -
    id: anp-codeblock-wrap-hl-preview
    title: Syntax Highlighted Codeblock Line Wrap (Preview Mode)
    type: class-select
    default: none
    options:
      -
        label: Wrap
        value: none
      -
        label: No Wrap
        value: anp-codeblock-preview-hl-nowrap
  -
    id: anp-code-bg-color
    title: Codeblock Background Color
    type: variable-themed-color
    allowEmpty: true
    format: hex
    opacity: false
    default-light: '#'
    default-dark: '#'
  -
    id: anp-code-text-color
    title: Codeblock Text Color
    type: variable-themed-color
    description: Applies to codeblocks without syntax highlighting
    allowEmpty: true
    format: hex
    opacity: false
    default-light: '#'
    default-dark: '#'

# File Editor & Markdown Elements :: Embeds
  -
    id: anp-embed-header
    title: Embeds
    description: Maximum embed height
    type: heading
    level: 2
    collapsed: true
  -
    id: embed-max-height
    title: Maximum embed height in px
    type: variable-number
    default: 200

# File Editor & Markdown Elements :: Lists

  -
    id: anp-list-header
    title: Lists
    description: Styling, indent, spacing
    type: heading
    level: 2
    collapsed: true
  -
    id: anp-list-toggle
    title: Enable List Styling
    type: class-toggle
  -
    id: list-indent
    title: List Indent
    type: variable-number
    default: 2
    format: em
  -
    id: list-spacing
    title: List Spacing
    type: variable-number
    default: 0.075
    format: em
  -
    id: list-marker-color
    title: Unordered list bullet color
    type: variable-themed-color
    default-light: '#'
    default-dark: '#'
    format: 'hex'
  -
    id: list-numbered-style
    title: Ordered list style
    type: variable-select
    default: decimal
    options:
      -
        label: "Decimal"
        value: decimal
      -
        label: "Decimal with leading zeroes"
        value: decimal-leading-zero
      -
        label: "Lowercase alphabetical"
        value: lower-alpha
      -
        label: "Uppercase alphabetical"
        value: upper-alpha
      -
        label: "Lowercase Roman Numerals"
        value: lower-roman
      -
        label: "Uppercase Roman Numerals"
        value: upper-roman
      -
        label: "Lowercase Latin"
        value: lower-latin
      -
        label: "Uppercase Latin"
        value: upper-latin
      -
        label: "Lowercase Greek"
        value: lower-greek
      -
        label: "Uppercase Greek"
        value: upper-greek
      -
        label: "Hiragana"
        value: hiragana
      -
        label: "Hiragana Iroha"
        value: hiragana-iroha
      -
        label: "Katakana"
        value: armenian
      -
        label: "Katakana Iroha"
        value: katakana-iroha
      -
        label: "Armenian"
        value: armenian
      -
        label: "CJK Ideographic"
        value: cjk-ideographic
      -
        label: "Hebrew"
        value: hebrew

# File Editor & Markdown Elements :: Tables

  -
    id: anp-table-header
    title: Tables
    description: Table width, alignment, table element highlight and alignment, border width
    type: heading
    level: 2
    collapsed: true
  -
    id: anp-table-toggle
    title: Enable Table Styling
    type: class-toggle
  -
    id: anp-table-width
    title: Enable Custom Table Width
    description: Use anp-table-width for yaml syntax
    type: class-toggle
  -
    id: anp-table-auto
    title: Center Tables
    description: Use anp-table-auto for yaml syntax
    type: class-toggle
  -
    id: anp-table-th-highlight
    title: <th> Highlight
    type: class-toggle
  -
    id: anp-td-highlight
    title: <td> Highlight
    type: class-select
    default: anp-td-none
    allowEmpty: false
    options:
      -
        label: None
        value: none
      -
        label: Alternate Rows
        value: anp-table-row-alt
      -
        label: Alternate Columns
        value: anp-table-col-alt
      -
        label: Checkered
        value: anp-table-checkered
      -
        label: Full
        value: anp-table-full
  -
    id: anp-table-highlight-opacity
    title: Highlight Opacity
    default: 0.5
    type: variable-number-slider
    min: 0
    max: 1
    step: 0.1
  -
    id: anp-table-align-th
    title: <th> Text Align
    type: variable-select
    default: center
    options:
      -
        label: Left
        value: left
      -
        label: Center
        value: center
      -
        label: Right
        value: right
  -
    id: anp-table-align-td
    title: <td> Text Align
    type: variable-select
    default: center
    options:
      -
        label: Left
        value: left
      -
        label: Center
        value: center
      -
        label: Right
        value: right
  -
    id: anp-table-thickness
    title: Custom Table Border Width
    type: variable-number
    default: 2
    format: px
  -
    id: anp-table-width-pct
    title: Custom Table Width
    type: variable-number
    default: 100
    format: "%"

# File Editor & Markdown Elements :: Tags

  -
    id: anp-tags-header
    title: Tags
    description: Border Width and Border Radius of Tags
    type: heading
    level: 2
    collapsed: true
  -
    id: tag-border-width
    title: Tag Border Width
    type: variable-number
    default: 0
    format: px
  -
    id: tag-radius
    title: Tag Radius
    type: variable-number-slider
    default: 2
    max: 2
    min: 0
    step: 0.1
    format: em

# File Preview

  -
    id: anp-preview-header
    title: File Preview
    description: File Preview Margins and Word-Wrap Width
    type: heading
    level: 1
    collapsed: true
  -
    id: anp-toggle-preview
    title: Enable Custom Preview Margins
    type: class-toggle
  -
    id: file-margins
    title: File Margins
    type: variable-number
    default: 32
    format: px
  -
    id: anp-preview-width-pct
    title: Preview Width
    type: variable-number
    default: 80
    format: "%"
  -
    id: file-line-width
    title: Maximum Preview Width
    type: variable-number
    default: 700
    format: px

# Integrations

  -
    id: anp-plugin-header
    title: Integrations
    description: Kanban, MAKE.md, Minimal Cards Snippet, etc.
    type: heading
    level: 1
    collapsed: true

# Integrations :: Kanban

  -
    id: anp-plugin-kanban
    title: Kanban
    description: 
    type: heading
    level: 2
    collapsed: true

# Integrations :: Kanban :: Card Settings

  -
    id: anp-plugin-card-settings
    title: Card Settings
    description: 
    type: heading
    level: 3
    collapsed: true
  -
    id: anp-kanban-hide-card-menus
    title: Hide the Card Menu Button
    type: class-toggle
  -
    id: anp-kanban-hide-archive-btn
    title: Hide the Archive Card Button
    type: class-toggle
  -
    id: anp-kanban-hide-card-border
    title: Disable Card Borders
    type: class-toggle
  -
    id: anp-kanban-card-opacity
    title: Card Opacity
    type: variable-number-slider
    default: 1
    min: 0
    max: 1
    step: 0.05
  -
    id: anp-kanban-card-radius
    title: Card Radius
    type: variable-number-slider
    default: 6
    format: px
    min: 0
    max: 10
    step: 1
  -
    id: anp-kanban-card-spacing
    title: Card Spacing
    type: variable-number-slider
    default: 8
    format: px
    min: -1
    max: 8
    step: 1

# Integrations :: Kanban :: Lane Settings

  -
    id: anp-plugin-lane-settings
    title: Lane Settings
    description: 
    type: heading
    level: 3
    collapsed: true
  -
    id: anp-kanban-lanes
    title: Enable Full Height Lanes
    description: Toggles maximum height for Kanban lanes
    type: class-toggle
  -
    id: anp-kanban-hide-lane-border
    title: Disable Lane Borders
    type: class-toggle
  -
    id: anp-kanban-lane-opacity
    title: Lane Opacity
    type: variable-number-slider
    default: 1
    min: 0
    max: 1
    step: 0.05
  -
    id: anp-kanban-lane-radius
    title: Lane Radius
    type: variable-number-slider
    default: 6
    format: px
    min: 0
    max: 10
    step: 1
  -
    id: anp-kanban-lane-spacing
    title: Lane Spacing
    type: variable-number-slider
    default: 10
    format: px
    min: -1
    max: 20
    step: 1

# Integrations :: MAKE.md

  -
    id: anp-plugin-makemd
    title: MAKE.md
    description: 
    type: heading
    level: 2
    collapsed: true
  -
    id: anp-inline-title-vis
    title: Inline Title Visibility
    description: Make.md requires inline titles to be enabled to show folder titles, hence this toggle exists to disable it on notes elsewhere.
    type: variable-select
    default: block
    options:
      -
        label: Visible
        value: block
      -
        label: Invisible
        value: none

# Integrations :: Minimal Cards

  -
    id: anp-snippet-minimal-cards
    title: Minimal Cards
    type: heading
    level: 2
    collapsed: true
  -
    id: anp-snippet-minimal-cards-disc
    title: Usage
    description: "Minimal Cards is a feature of [Minimal](https://github.com/kepano/obsidian-minimal). AnuPpuccin does not come compiled with the snippet, however you can use a compiled version with this theme."
    type: info-text
    markdown: true
  -
    id: anuppuccin-url-minimal-cards-snippet
    title: Minimal Cards Snippet
    description: "Download the snippet from [kepano](https://github.com/kepano/)'s github via [this link](https://github.com/kepano/obsidian-minimal/blob/master/src/scss/features/cards.scss)."
    type: info-text
    markdown: true
  -
    id: cards-min-width
    title: Card minimum width
    type: variable-text
    default: 180px
  -
    id: cards-max-width
    title: Card maximum width
    description: Default fills the available width, accepts valid CSS units
    type: variable-text
    default: 1fr
  -
    id: cards-mobile-width
    title: Card minimum width on mobile
    type: variable-text
    default: 120px
  -
    id: cards-padding
    title: Card padding
    type: variable-text
    default: 1.2em
  -
    id: cards-image-height
    title: Card maximum image height
    type: variable-text
    default: 400px
  -
    id: cards-border-width
    title: Card border width
    type: variable-text
    default: 1px

# Miscellaneous

  -
    id: anp-misc-element-header
    title: Miscellaneous
    description: Color Palette Transition, Custom Metadata Button
    type: heading
    level: 1
    collapsed: true
  -
    id: anp-color-transition-toggle
    title: Enable Color Transition (WIP)
    description: "Adds a transition when switching palettes, looks really neat :)"
    type: class-toggle
  -
    id: anp-button-metadata-toggle
    title: Enable Custom Metadata Button
    type: class-toggle
  -
    id: anp-print
    title: Enable Print Styling
    type: class-toggle

# Show/Hide UI Elements

  -
    id: anp-show-hide-elements-header
    title: Show/Hide UI Elements
    description: Toggle Visibility of UI Elements
    type: heading
    level: 1
    collapsed: true
  -
    id: anp-autohide-titlebar
    title: Autohide Title Bar
    type: class-toggle
  -
    id: anp-cursor
    title: Pointer cursor
    type: variable-select
    default: pointer
    options:
      -
        label: Pointer
        value: pointer
      -
        label: Normal
        value: initial
  -
    id: anp-toggle-metadata
    title: Hide Metadata
    type: class-toggle
  -
    id: anp-toggle-scrollbars
    title: Hide Scrollbars
    type: class-toggle
  -
    id: anp-hide-status-bar
    title: Hide Status Bar
    type: class-toggle
  -
    id: anp-tooltip-toggle
    title: Hide Tooltips
    type: class-toggle

# Typography

  -
    id: anp-typography-header
    title: Typography
    description: Fonts, Headings, Text Decoration, etc.
    type: heading
    level: 1
    collapsed: true

# Typography :: Font Families

  -
    id: anp-font-families-header
    title: Font Families
    description: 
    type: heading
    level: 2
    collapsed: true
  -
    id: anp-editor-font-source
    title: Source Editor Font
    type: variable-text
    default: Iosevka Comfy Duo
  -
    id: anp-editor-font-lp
    title: Live Preview Editor Font
    type: variable-text
    default: Noto Sans

# Typography :: Font Weights

  -
    id: anp-font-weight-header
    title: Font Weights
    description: 
    type: heading
    level: 2
    collapsed: true
  -
    id: bold-weight
    title: Bold Weight
    type: variable-select
    default: '600'
    options:
      -
        label: 'Bolder (Relative)'
        value: bolder
      -
        label: Lightest
        value: 100
      -
        label: Lighter
        value: 200
      -
        label: Light
        value: 300
      -
        label: Normal
        value: 400
      -
        label: Bold
        value: 500
      -
        label: Bolder
        value: 600
      -
        label: Extrabold
        value: 700
      -
        label: Extra-extrabold
        value: 800
      -
        label: Black
        value: 900
  -
    id: anp-font-live-preview-wt
    title: Live Preview Mode Weight
    type: variable-select
    default: '400'
    options:
      -
        label: Lightest
        value: 100
      -
        label: Lighter
        value: 200
      -
        label: Light
        value: 300
      -
        label: Normal
        value: 400
      -
        label: Bold
        value: 500
      -
        label: Bolder
        value: 600
      -
        label: Extrabold
        value: 700
      -
        label: Extra-extrabold
        value: 800
      -
        label: Black
        value: 900
  -
    id: anp-font-preview-wt
    title: Reading Mode Weight
    type: variable-select
    default: '400'
    options:
      -
        label: Lightest
        value: 100
      -
        label: Lighter
        value: 200
      -
        label: Light
        value: 300
      -
        label: Normal
        value: 400
      -
        label: Bold
        value: 500
      -
        label: Bolder
        value: 600
      -
        label: Extrabold
        value: 700
      -
        label: Extra-extrabold
        value: 800
      -
        label: Black
        value: 900
  -
    id: anp-font-editor-wt
    title: Source Mode Weight
    type: variable-select
    default: '400'
    options:
      -
        label: Lightest
        value: 100
      -
        label: Lighter
        value: 200
      -
        label: Light
        value: 300
      -
        label: Normal
        value: 400
      -
        label: Bold
        value: 500
      -
        label: Bolder
        value: 600
      -
        label: Extrabold
        value: 700
      -
        label: Extra-extrabold
        value: 800
      -
        label: Black
        value: 900

# Typography :: Headings

  -
    id: anp-header-header
    title: Headings
    description: 
    type: heading
    level: 2
    collapsed: true
  -
    id: anp-header-color-toggle
    title: Enable Custom Heading Colors
    type: class-toggle
  -
    id: anp-header-margin-toggle
    title: Enable Custom Heading Margin
    type: class-toggle
  -
    id: anp-header-divider-color-toggle
    title: Inherit Divider Color from Heading Color
    type: class-toggle
  -
    id: anp-header-margin-value
    title: Heading margin value
    description: Size unit is px
    type: variable-number-slider
    min: 0
    max: 30
    step: 2
    default: 15
    format: px

# Typography :: Headings :: H1

  -
    id: anp-h1-header
    title: H1
    description: 
    type: heading
    level: 3
    collapsed: true
  -
    id: h1-font
    title: H1 Font Family
    type: variable-text
    default: Noto Serif
  -
    id: h1-size
    title: H1 Font Size
    type: variable-number
    default: 2
    format: em
  -
    id: h1-weight
    title: H1 Font Weight
    type: variable-number
    default: 700
  -
    id: h1-line-height
    title: H1 Line Height
    type: variable-number
    default: 1.2
  -
    id: anp-h1-color-custom
    title: H1 Color
    type: class-select
    allowEmpty: true
    default: anp-h1-red
    options:
      - 
        label: Rosewater
        value: anp-h1-rosewater
      - 
        label: Flamingo
        value: anp-h1-flamingo
      - 
        label: Pink
        value: anp-h1-pink
      - 
        label: Mauve
        value: anp-h1-mauve
      - 
        label: Red
        value: anp-h1-red
      - 
        label: Maroon
        value: anp-h1-maroon
      - 
        label: Peach
        value: anp-h1-peach
      - 
        label: Yellow
        value: anp-h1-yellow
      - 
        label: Green
        value: anp-h1-green
      - 
        label: Teal
        value: anp-h1-teal
      - 
        label: Sky
        value: anp-h1-sky
      - 
        label: Sapphire
        value: anp-h1-sapphire
      - 
        label: Blue
        value: anp-h1-blue
      - 
        label: Lavender
        value: anp-h1-lavender
  -
    id: anp-h1-divider
    title: H1 Divider
    type: class-toggle

# Typography :: Headings :: H2

  -
    id: anp-h2-header
    title: H2
    description: 
    type: heading
    level: 3
    collapsed: true
  -
    id: h2-font
    title: H2 Font Family
    type: variable-text
    default: Noto Serif
  -
    id: h2-size
    title: H2 Font Size
    type: variable-number
    default: 1.6
    format: em
  -
    id: h2-weight
    title: H2 Font Weight
    type: variable-number
    default: 600
  -
    id: h2-line-height
    title: H2 Line Height
    type: variable-number
    default: 1.2
  -
    id: anp-h2-color-custom
    title: H2 Color
    type: class-select
    allowEmpty: true
    default: anp-h2-peach
    options:
      - 
        label: Rosewater
        value: anp-h2-rosewater
      - 
        label: Flamingo
        value: anp-h2-flamingo
      - 
        label: Pink
        value: anp-h2-pink
      - 
        label: Mauve
        value: anp-h2-mauve
      - 
        label: Red
        value: anp-h2-red
      - 
        label: Maroon
        value: anp-h2-maroon
      - 
        label: Peach
        value: anp-h2-peach
      - 
        label: Yellow
        value: anp-h2-yellow
      - 
        label: Green
        value: anp-h2-green
      - 
        label: Teal
        value: anp-h2-teal
      - 
        label: Sky
        value: anp-h2-sky
      - 
        label: Sapphire
        value: anp-h2-sapphire
      - 
        label: Blue
        value: anp-h2-blue
      - 
        label: Lavender
        value: anp-h2-lavender
  -
    id: anp-h2-divider
    title: H2 Divider
    type: class-toggle

# Typography :: Headings :: H3

  -
    id: anp-h3-header
    title: H3
    description: 
    type: heading
    level: 3
    collapsed: true
  -
    id: h3-font
    title: H3 Font Family
    type: variable-text
    default: Noto Serif
  -
    id: h3-size
    title: H3 Font Size
    type: variable-number
    default: 1.37
    format: em
  -
    id: h3-weight
    title: H3 Font Weight
    type: variable-number
    default: 600
  -
    id: h3-line-height
    title: H3 Line Height
    type: variable-number
    default: 1.3
  -
    id: anp-h3-color-custom
    title: H3 Color
    type: class-select
    allowEmpty: true
    default: anp-h3-green
    options:
      - 
        label: Rosewater
        value: anp-h3-rosewater
      - 
        label: Flamingo
        value: anp-h3-flamingo
      - 
        label: Pink
        value: anp-h3-pink
      - 
        label: Mauve
        value: anp-h3-mauve
      - 
        label: Red
        value: anp-h3-red
      - 
        label: Maroon
        value: anp-h3-maroon
      - 
        label: Peach
        value: anp-h3-peach
      - 
        label: Yellow
        value: anp-h3-yellow
      - 
        label: Green
        value: anp-h3-green
      - 
        label: Teal
        value: anp-h3-teal
      - 
        label: Sky
        value: anp-h3-sky
      - 
        label: Sapphire
        value: anp-h3-sapphire
      - 
        label: Blue
        value: anp-h3-blue
      - 
        label: Lavender
        value: anp-h3-lavender
  -
    id: anp-h3-divider
    title: H3 Divider
    type: class-toggle

# Typography :: Headings :: H4

  -
    id: anp-h4-header
    title: H4
    description: 
    type: heading
    level: 3
    collapsed: true
  -
    id: h4-font
    title: H4 Font Family
    type: variable-text
    default: Noto Serif
  -
    id: h4-size
    title: H4 Font Size
    type: variable-number
    default: 1.25
    format: em
  -
    id: h4-weight
    title: H4 Font Weight
    type: variable-number
    default: 600
  -
    id: h4-line-height
    title: H4 Line Height
    type: variable-number
    default: 1.4
  -
    id: anp-h4-color-custom
    title: H4 Color
    type: class-select
    allowEmpty: true
    default: anp-h4-teal
    options:
      - 
        label: Rosewater
        value: anp-h4-rosewater
      - 
        label: Flamingo
        value: anp-h4-flamingo
      - 
        label: Pink
        value: anp-h4-pink
      - 
        label: Mauve
        value: anp-h4-mauve
      - 
        label: Red
        value: anp-h4-red
      - 
        label: Maroon
        value: anp-h4-maroon
      - 
        label: Peach
        value: anp-h4-peach
      - 
        label: Yellow
        value: anp-h4-yellow
      - 
        label: Green
        value: anp-h4-green
      - 
        label: Teal
        value: anp-h4-teal
      - 
        label: Sky
        value: anp-h4-sky
      - 
        label: Sapphire
        value: anp-h4-sapphire
      - 
        label: Blue
        value: anp-h4-blue
      - 
        label: Lavender
        value: anp-h4-lavender
  -
    id: anp-h4-divider
    title: H4 Divider
    type: class-toggle

# Typography :: Headings :: H5

  -
    id: anp-h5-header
    title: H5
    description: 
    type: heading
    level: 3
    collapsed: true
  -
    id: h5-font
    title: H5 Font Family
    type: variable-text
    default: Noto Serif
  -
    id: h5-size
    title: H5 Font Size
    type: variable-number
    default: 1.12
    format: em
  -
    id: h5-weight
    title: H5 Font Weight
    type: variable-number
    default: 600
  -
    id: h5-line-height
    title: H5 Line Height
    type: variable-number
    default: 1.5
  -
    id: anp-h5-color-custom
    title: H5 Color
    type: class-select
    allowEmpty: true
    default: anp-h5-lavender
    options:
      - 
        label: Rosewater
        value: anp-h5-rosewater
      - 
        label: Flamingo
        value: anp-h5-flamingo
      - 
        label: Pink
        value: anp-h5-pink
      - 
        label: Mauve
        value: anp-h5-mauve
      - 
        label: Red
        value: anp-h5-red
      - 
        label: Maroon
        value: anp-h5-maroon
      - 
        label: Peach
        value: anp-h5-peach
      - 
        label: Yellow
        value: anp-h5-yellow
      - 
        label: Green
        value: anp-h5-green
      - 
        label: Teal
        value: anp-h5-teal
      - 
        label: Sky
        value: anp-h5-sky
      - 
        label: Sapphire
        value: anp-h5-sapphire
      - 
        label: Blue
        value: anp-h5-blue
      - 
        label: Lavender
        value: anp-h5-lavender
  -
    id: anp-h5-divider
    title: H5 Divider
    type: class-toggle

# Typography :: Headings :: H6

  -
    id: anp-h6-header
    title: H6
    description: 
    type: heading
    level: 3
    collapsed: true
  -
    id: h6-font
    title: H6 Font Family
    type: variable-text
    default: Noto Serif
  -
    id: h6-size
    title: H6 Font Size
    type: variable-number
    default: 1.12
    format: em
  -
    id: h6-weight
    title: H6 Font Weight
    type: variable-number
    default: 600
  -
    id: h6-line-height
    title: H6 Line Height
    type: variable-number
    default: 1.5
  -
    id: anp-h6-color-custom
    title: H6 Color
    type: class-select
    allowEmpty: true
    default: anp-h6-mauve
    options:
      - 
        label: Rosewater
        value: anp-h6-rosewater
      - 
        label: Flamingo
        value: anp-h6-flamingo
      - 
        label: Pink
        value: anp-h6-pink
      - 
        label: Mauve
        value: anp-h6-mauve
      - 
        label: Red
        value: anp-h6-red
      - 
        label: Maroon
        value: anp-h6-maroon
      - 
        label: Peach
        value: anp-h6-peach
      - 
        label: Yellow
        value: anp-h6-yellow
      - 
        label: Green
        value: anp-h6-green
      - 
        label: Teal
        value: anp-h6-teal
      - 
        label: Sky
        value: anp-h6-sky
      - 
        label: Sapphire
        value: anp-h6-sapphire
      - 
        label: Blue
        value: anp-h6-blue
      - 
        label: Lavender
        value: anp-h6-lavender
  -
    id: anp-h6-divider
    title: H6 Divider
    type: class-toggle

# Typography :: Text Decoration

  -
    id: anp-decor-header
    title: Text Decoration
    description: 
    type: heading
    level: 2
    collapsed: true
  -
    id: anp-decoration-toggle
    title: Enable Decoration Colors
    type: class-toggle
  -
    id: anp-bold-custom
    title: Bold Color
    type: class-select
    allowEmpty: true
    default: anp-bold-red
    options:
      - 
        label: Rosewater
        value: anp-bold-rosewater
      - 
        label: Flamingo
        value: anp-bold-flamingo
      - 
        label: Pink
        value: anp-bold-pink
      - 
        label: Mauve
        value: anp-bold-mauve
      - 
        label: Red
        value: anp-bold-red
      - 
        label: Maroon
        value: anp-bold-maroon
      - 
        label: Peach
        value: anp-bold-peach
      - 
        label: Yellow
        value: anp-bold-yellow
      - 
        label: Green
        value: anp-bold-green
      - 
        label: Teal
        value: anp-bold-teal
      - 
        label: Sky
        value: anp-bold-sky
      - 
        label: Sapphire
        value: anp-bold-sapphire
      - 
        label: Blue
        value: anp-bold-blue
      - 
        label: Lavender
        value: anp-bold-lavender
  -
    id: anp-italic-custom
    title: Italic Color
    type: class-select
    allowEmpty: true
    default: anp-italic-green
    options:
      - 
        label: Rosewater
        value: anp-italic-rosewater
      - 
        label: Flamingo
        value: anp-italic-flamingo
      - 
        label: Pink
        value: anp-italic-pink
      - 
        label: Mauve
        value: anp-italic-mauve
      - 
        label: Red
        value: anp-italic-red
      - 
        label: Maroon
        value: anp-italic-maroon
      - 
        label: Peach
        value: anp-italic-peach
      - 
        label: Yellow
        value: anp-italic-yellow
      - 
        label: Green
        value: anp-italic-green
      - 
        label: Teal
        value: anp-italic-teal
      - 
        label: Sky
        value: anp-italic-sky
      - 
        label: Sapphire
        value: anp-italic-sapphire
      - 
        label: Blue
        value: anp-italic-blue
      - 
        label: Lavender
        value: anp-italic-lavender
  -
    id: anp-highlight-custom
    title: Highlight Color
    type: class-select
    allowEmpty: true
    default: anp-highlight-yellow
    options:
      - 
        label: Rosewater
        value: anp-highlight-rosewater
      - 
        label: Flamingo
        value: anp-highlight-flamingo
      - 
        label: Pink
        value: anp-highlight-pink
      - 
        label: Mauve
        value: anp-highlight-mauve
      - 
        label: Red
        value: anp-highlight-red
      - 
        label: Maroon
        value: anp-highlight-maroon
      - 
        label: Peach
        value: anp-highlight-peach
      - 
        label: Yellow
        value: anp-highlight-yellow
      - 
        label: Green
        value: anp-highlight-green
      - 
        label: Teal
        value: anp-highlight-teal
      - 
        label: Sky
        value: anp-highlight-sky
      - 
        label: Sapphire
        value: anp-highlight-sapphire
      - 
        label: Blue
        value: anp-highlight-blue
      - 
        label: Lavender
        value: anp-highlight-lavender

# Workspace

  -
    id: anp-workspace-header
    title: Workspace
    description: Background, Colorful Frame, Layout, Sidebar, Tabs, Translucency, etc.
    type: heading
    level: 1
    collapsed: true

# Workspace :: Canvas

  -
    id: anp-canvas-header
    title: Canvas
    description: Darken Canvas Background
    type: heading
    level: 2
    collapsed: true
  -
    id: anp-canvas-dark-bg
    title: Darker Canvas Background
    type: class-toggle

# Workspace :: Custom Background

  -
    id: anp-custom-background-header
    title: Background
    description: Custom workspace backgrounds
    type: heading
    level: 2
    collapsed: true
  -
    id: anp-background-image-toggle
    title: Enable Custom Background (WIP)
    description: Not compatible with colorful frame
    type: class-toggle

# Workspace :: Custom Background :: Light Mode

  -
    id: anp-custom-background-light-header
    title: Light Mode
    description: 
    type: heading
    level: 3
    collapsed: true
  -
    id: anp-background-image-light
    title: Custom Background Image URL
    description: 'Formatted as url("https://link-to-image.png")'
    type: variable-text
    default: url("https://link-to-image.png")
  -
    id: anp-custom-bg-brightness-light
    title: Background brightness
    type: variable-number-slider
    min: 0
    max: 2
    step: 0.05
    default: 0.7
  -
    id: anp-custom-bg-blur-light
    title: Background blur
    type: variable-number-slider
    min: 0
    max: 20
    step: 1
    default: 5
    format: px
  -
    id: anp-custom-bg-card-fg-opacity-light
    title: Container opacity
    type: variable-number-slider
    min: 0
    max: 1
    step: 0.05
    default: 0.4

# Workspace :: Custom Background :: Dark Mode

  -
    id: anp-custom-background-dark-header
    title: Dark Mode
    description: 
    type: heading
    level: 3
    collapsed: true
  -
    id: anp-background-image-dark
    title: Custom Background Image URL
    description: 'Formatted as url("https://link-to-image.png")'
    type: variable-text
    default: url("https://link-to-image.png")
  -
    id: anp-custom-bg-brightness-dark
    title: Background brightness
    type: variable-number-slider
    min: 0
    max: 1
    step: 0.05
    default: 0.7
  -
    id: anp-custom-bg-blur-dark
    title: Background blur
    type: variable-number-slider
    min: 0
    max: 20
    step: 1
    default: 5
    format: px
  -
    id: anp-custom-bg-card-fg-opacity-dark
    title: Container opacity
    type: variable-number-slider
    min: 0
    max: 1
    step: 0.05
    default: 0.4

# Workspace :: Colorful Frame

  -
    id: colorful-frame
    title: Colorful Frame
    description: 
    type: heading
    level: 2
    collapsed: true
  -
    id: anp-colorful-frame
    title: "Enable Colorful Frame (WIP)"
    type: class-toggle
  -
    id: anp-colorful-frame-icon-toggle-light
    title: "Invert Icon Colors - Light"
    type: class-toggle
  -
    id: anp-colorful-frame-icon-toggle-dark
    title: "Invert Icon Colors - Dark"
    type: class-toggle
  -
    id: anp-colorful-frame-opacity
    title: Colorful Frame Opacity
    type: variable-number-slider
    default: 1
    min: 0
    max: 1
    step: 0.05
  -
    id: anp-colorful-frame-color
    title: Custom Colorful Frame Color
    type: variable-themed-color
    format: "rgb-values"
    opacity: false
    default-light: '#'
    default-dark: '#'
    alt-format:
      -
        id: anp-colorful-frame-color-hsl
        format: "hsl-values"

# Workspace :: File Browser

  -
    id: anp-filebrowser-header
    title: File Browser
    description: 'Enable/Disable File Browser elements'
    type: heading
    level: 2
    collapsed: true
  -
    id: anp-collapse-folders
    title: Enable folder icons for collapse indicators
    type: class-toggle
  -
    id: anp-file-icons
    title: Enable file icons
    type: class-toggle
  -
    id: anp-floating-header
    title: Enable floating vault title
    type: class-toggle
  -
    id: anp-custom-vault-toggle
    title: Enable custom vault title
    type: class-toggle
  -
    id: anp-file-label-align
    title: File label alignment
    type: variable-select
    default: "0"
    options:
      -
        label: Right
        value: 1
      -
        label: Left
        value: 0
  -
    id: anp-file-icon-align
    title: File Icon Alignment
    type: variable-select
    default: '0'
    format: px
    options:
      - 
        label: Default
        value: 0
      - 
        label: Aligned
        value: -17

# Workspace :: PDF Viewer

  -
    id: anp-pdf-viewer-header
    title: PDF Viewer
    description: 
    type: heading
    level: 2
    collapsed: true
  -
    id: anp-pdf-blend-toggle-dark
    title: Toggle PDF background blending - Dark Mode
    desc: Blends PDF viewer background with obsidian background in dark mode
    type: class-toggle
  -
    id: anp-pdf-blend-toggle-light
    title: Toggle PDF background blending - Light Mode
    desc: Blends PDF viewer background with obsidian background in light mode
    type: class-toggle

# Workspace :: Rainbow Folders

  -
    id: anp-rainbow-section-header
    title: Rainbow Folders
    description: 
    type: heading
    level: 2
    collapsed: true
  -
    id: anuppuccin-url-custom-rainbow-colors
    title: Custom rainbow folder colors snippet
    description: "Download the snippet through [this link](https://github.com/AnubisNekhet/AnuPpuccin/blob/main/snippets/custom-rainbow-colors.css)."
    type: info-text
    markdown: true
  -
    id: anp-alt-rainbow-style
    title: Rainbow style
    type: class-select
    allowEmpty: false
    default: anp-default-rainbow
    options:
      - 
        label: None
        value: anp-default-rainbow
      - 
        label: Full
        value: anp-full-rainbow-color-toggle
      - 
        label: Simple
        value: anp-simple-rainbow-color-toggle

# Workspace :: Rainbow Folders :: Full Folder Settings

  -
    id: anp-full-rainbow-folder-settings
    title: Full Folder Settings
    description: 
    type: heading
    level: 3
    collapsed: true
  -
    id: anp-rainbow-file-toggle
    title: File recolor toggle
    desc: Recolors files to match the folders
    type: class-toggle
  -
    id: anp-full-rainbow-text-color-toggle-light
    title: Invert title colors (Dark Mode)
    type: class-toggle
  -
    id: anp-full-rainbow-text-color-toggle-dark
    title: Invert title colors (Light Mode)
    type: class-toggle
  -
    id: anp-rainbow-folder-bg-opacity
    title: Folder background color opacity
    type: variable-number
    default: 0.7

# Workspace :: Rainbow Folders :: Simple Folder Settings

  -
    id: anp-simple-rainbow-folder-settings
    title: Simple Folder Settings
    description: 
    type: heading
    level: 3
    collapsed: true
  -
    id: anp-simple-rainbow-title-toggle
    title: Enable title recolor
    type: class-toggle
  -
    id: anp-simple-rainbow-icon-folder-toggle
    title: Enable icon recolor (For icon folder users)
    type: class-toggle
  -
    id: anp-simple-rainbow-indentation-toggle
    title: Enable collapse indent recolor
    type: class-toggle
  -
    id: anp-simple-rainbow-collapse-toggle
    title: Enable collapse indicator
    type: class-toggle
  -
    id: anp-simple-rainbow-icon-toggle
    title: Enable circular file
    type: class-toggle
  -
    id: anp-rainbow-subfolder-color-toggle
    title: Enable subfolder color inheritance
    type: class-toggle

# Workspace :: Stacked Tabs

  -
    id: anp-stacked-tabs-header
    title: Stacked Tabs
    description: 
    type: heading
    level: 2
    collapsed: true
  -
    id: anp-stacked-header-width
    title: Stacked tabs header width
    type: variable-number
    default: 40
    format: px
  -
    id: anp-tab-stacked-pane-width
    title: Stacked tabs pane width multiplier
    description: Multiplies the stack tab pane with which depends on file line width
    type: variable-number
    default: 1

# Workspace :: Status Bar

  -
    id: anp-status-bar-header
    title: Status Bar
    description: Select status bar style
    type: heading
    level: 2
    collapsed: true
  -
    id: anp-status-bar-select
    title: Status Bar Style
    type: class-select
    default: none
    options:
      -
        label: Default
        value: none
      -
        label: Floating
        value: anp-floating-status-bar
      -
        label: Fixed
        value: anp-fixed-status-bar

# Workspace :: Tabs

  -
    id: anp-alt-tab-header
    title: Tabs
    description: 
    type: heading
    level: 2
    collapsed: true
  -
    id: anp-alt-tab-style
    title: Tab style
    type: class-select
    allowEmpty: false
    default: anp-default-tab
    options:
      - 
        label: Default
        value: anp-default-tab
      - 
        label: Depth
        value: anp-depth-tab-toggle
      - 
        label: Minimalistic
        value: anp-mini-tab-toggle
      - 
        label: Safari-style (Animated)
        value: anp-alternate-tab-toggle
      - 
        label: Safari-style (Vanilla)
        value: anp-safari-tab-toggle
  -
    id: anp-alt-tab-custom-height
    title: Custom tab height
    type: variable-number
    allowEmpty: false
    default: 40
    format: px
  -
    id: anp-disable-newtab-align
    title: Disable new tab button right alignment
    type: class-toggle

# Workspace :: Tabs :: Depth Tab Settings

  -
    id: anp-depth-tab-header
    title: Depth Tab Settings
    description: Only applies to inactive tabs
    type: heading
    level: 3
    collapsed: true
  -
    id: anp-depth-tab-text-invert
    title: Invert tab text color
    type: class-toggle
  -
    id: anp-depth-tab-opacity
    title: Tab opacity
    type: variable-number-slider
    default: 0.6
    max: 1
    min: 0
    step: 0.1
  -
    id: anp-depth-tab-gap
    title: Tab gap
    type: variable-number
    default: 10
    format: px

# Workspace :: Tabs :: Animated Safari-style Tab Settings

  -
    id: anp-safari-tab-header
    title: Animated Safari-style Tab Settings
    description: Adapted from github.com/chuckharmston's snippets
    type: heading
    level: 3
    collapsed: true
  -
    id: anp-safari-tab-radius
    title: Tab radius
    type: variable-number
    default: 6
    format: px
  -
    id: anp-safari-tab-gap
    title: Tab gap
    type: variable-number
    default: 10
    format: px
  -
    id: anp-safari-border-width
    title: Tab border width
    type: variable-number
    default: 1
    format: px

# Workspace :: Tabs :: Vanilla Safari-style Tab Settings

  -
    id: anp-safari-vanilla-tab-header
    title: Vanilla Safari-style Tab Settings
    description: 
    type: heading
    level: 3
    collapsed: true
  -
    id: anp-safari-tab-animated
    title: Enable WIP animated safari tabs
    type: class-toggle

# Workspace :: Translucency

  -
    id: anp-translucency-header
    title: Translucency
    description: 
    type: heading
    level: 2
    collapsed: true
  -
    id: anp-translucency-opacity
    description: Drag to change background opacity
    title: Window opacity
    type: variable-number
    default: 0.15

# Workspace :: Workspace Layout

  -
    id: anp-layout-header
    title: Workspace Layout
    description: Workspace layout and arrangement
    type: heading
    level: 2
    collapsed: true
  -
    id: anp-layout-select
    title: Workspace Layout variant
    type: class-select
    allowEmpty: false
    default: none
    options:
      -
        label: Default
        value: none
      -
        label: Border
        value: anp-border-layout
      -
        label: Cards
        value: anp-card-layout
  -
    id: anp-bg-fix
    title: Background fix for applying colors
    type: class-toggle
  -
    id: anp-hide-borders
    title: Hide borders
    type: class-toggle

# Workspace :: Workspace Layout :: Card Layout

  -
    id: anp-layout-card
    title: Card Layout Settings
    description: 
    type: heading
    level: 3
    collapsed: true
  -
    id: anp-card-radius
    title: Card radius
    type: variable-number
    default: 16
    format: px
  -
    id: anp-card-layout-padding
    title: Card padding
    type: variable-number
    default: 10
    format: px
  -
    id: anp-card-header-left-padding
    title: Tab left padding
    type: variable-number
    default: 20
    format: px
  -
    id: anp-card-shadows
    title: Enable shadows
    type: class-toggle
  -
    id: anp-card-layout-actions
    title: Enable card format for actions
    type: class-toggle
  -
    id: anp-card-layout-filebrowser
    title: Enable card format for file browser
    type: class-toggle
  -
    id: anp-layout-border
    title: Border Layout Settings
    description: 
    type: heading
    level: 3
    collapsed: true
  -
    id: anp-border-radius
    title: Border Layout Radius
    type: variable-number
    default: 16
    format: px
  -
    id: anp-border-padding
    title: Border Layout Padding
    type: variable-number
    default: 20
    format: px

# Credits

  -
    id: anuppuccin-support
    title: Support AnuPpuccin
    description: If you like the theme, here are some ways to support development
    type: heading
    level: 1
    collapsed: true
  -
    id: anuppuccin-url-donate
    title: Buy Me a Coffee
    description: "[https://www.buymeacoffee.com/anubisnekhet](https://www.buymeacoffee.com/anubisnekhet)"
    type: info-text
    markdown: true
  -
    id: anuppuccin-url-star-repo
    title: Star the Theme on GitHub
    description: "[https://github.com/anubisnekhet/anuppuccin](https://github.com/anubisnekhet/anuppuccin)"
    type: info-text
    markdown: true
  -
    id: anuppuccin-url-submit-issue
    title: Submit an Issue on GitHub
    description: "[https://github.com/anubisnekhet/anuppuccin/issues](https://github.com/anubisnekhet/anuppuccin/issues)"
    type: info-text
    markdown: true

*/
/*------------------Defining Colorschemes-------------------*/
.theme-light.ctp-latte, .ctp-latte .themed-color-wrapper > .theme-light {
  --ctp-rosewater: var(--ctp-custom-rosewater, var(--ctp-ext-rosewater, 222, 149, 132));
  --ctp-flamingo: var(--ctp-custom-flamingo, var(--ctp-ext-flamingo, 221, 120, 120));
  --ctp-pink: var(--ctp-custom-pink, var(--ctp-ext-pink, 236, 131, 208));
  --ctp-mauve: var(--ctp-custom-mauve, var(--ctp-ext-mauve, 136, 57, 239));
  --ctp-red: var(--ctp-custom-red, var(--ctp-ext-red, 210, 15, 57));
  --ctp-maroon: var(--ctp-custom-maroon, var(--ctp-ext-maroon, 230, 69, 83));
  --ctp-peach: var(--ctp-custom-peach, var(--ctp-ext-peach, 254, 100, 11));
  --ctp-yellow: var(--ctp-custom-yellow, var(--ctp-ext-yellow, 228, 147, 32));
  --ctp-green: var(--ctp-custom-green, var(--ctp-ext-green, 64, 160, 43));
  --ctp-teal: var(--ctp-custom-teal, var(--ctp-ext-teal, 23, 146, 153));
  --ctp-sky: var(--ctp-custom-sky, var(--ctp-ext-sky, 4, 165, 229));
  --ctp-sapphire: var(--ctp-custom-sapphire, var(--ctp-ext-sapphire, 32, 159, 181));
  --ctp-blue: var(--ctp-custom-blue, var(--ctp-ext-blue, 42, 110, 245));
  --ctp-lavender: var(--ctp-custom-lavender, var(--ctp-ext-lavender, 114, 135, 253));
  --ctp-text: var(--ctp-custom-text, var(--ctp-ext-text, 76, 79, 105));
  --ctp-subtext1: var(--ctp-custom-subtext1, var(--ctp-ext-subtext1, 92, 95, 119));
  --ctp-subtext0: var(--ctp-custom-subtext0, var(--ctp-ext-subtext0, 108, 111, 133));
  --ctp-overlay2: var(--ctp-custom-overlay2, var(--ctp-ext-overlay2, 124, 127, 147));
  --ctp-overlay1: var(--ctp-custom-overlay1, var(--ctp-ext-overlay1, 140, 143, 161));
  --ctp-overlay0: var(--ctp-custom-overlay0, var(--ctp-ext-overlay0, 156, 160, 176));
  --ctp-surface2: var(--ctp-custom-surface2, var(--ctp-ext-surface2, 172, 176, 190));
  --ctp-surface1: var(--ctp-custom-surface1, var(--ctp-ext-surface1, 188, 192, 204));
  --ctp-surface0: var(--ctp-custom-surface0, var(--ctp-ext-surface0, 204, 208, 218));
  --ctp-base: var(--ctp-custom-base, var(--ctp-ext-base, 239, 241, 245));
  --ctp-mantle: var(--ctp-custom-mantle, var(--ctp-ext-mantle, 230, 233, 239));
  --ctp-crust: var(--ctp-custom-crust, var(--ctp-ext-crust, 220, 224, 232));
}

.theme-light.ctp-rosepine-light, .ctp-rosepine-light .themed-color-wrapper > .theme-light {
  --ctp-rosewater: var(--ctp-custom-rosewater, var(--ctp-ext-rosewater, 214, 129, 125));
  --ctp-flamingo: var(--ctp-custom-flamingo, var(--ctp-ext-flamingo, 214, 129, 125));
  --ctp-pink: var(--ctp-custom-pink, var(--ctp-ext-pink, 144, 122, 169));
  --ctp-mauve: var(--ctp-custom-mauve, var(--ctp-ext-mauve, 144, 122, 169));
  --ctp-red: var(--ctp-custom-red, var(--ctp-ext-red, 180, 99, 122));
  --ctp-maroon: var(--ctp-custom-maroon, var(--ctp-ext-maroon, 180, 99, 122));
  --ctp-peach: var(--ctp-custom-peach, var(--ctp-ext-peach, 214, 129, 125));
  --ctp-yellow: var(--ctp-custom-yellow, var(--ctp-ext-yellow, 234, 157, 52));
  --ctp-green: var(--ctp-custom-green, var(--ctp-ext-green, 86, 148, 159));
  --ctp-teal: var(--ctp-custom-teal, var(--ctp-ext-teal, 86, 148, 159));
  --ctp-sky: var(--ctp-custom-sky, var(--ctp-ext-sky, 40, 105, 131));
  --ctp-sapphire: var(--ctp-custom-sapphire, var(--ctp-ext-sapphire, 40, 105, 131));
  --ctp-blue: var(--ctp-custom-blue, var(--ctp-ext-blue, 40, 105, 131));
  --ctp-lavender: var(--ctp-custom-lavender, var(--ctp-ext-lavender, 144, 122, 169));
  --ctp-text: var(--ctp-custom-text, var(--ctp-ext-text, 87, 82, 121));
  --ctp-subtext1: var(--ctp-custom-subtext1, var(--ctp-ext-subtext1, 97, 92, 132));
  --ctp-subtext0: var(--ctp-custom-subtext0, var(--ctp-ext-subtext0, 121, 117, 147));
  --ctp-overlay2: var(--ctp-custom-overlay2, var(--ctp-ext-overlay2, 128, 124, 153));
  --ctp-overlay1: var(--ctp-custom-overlay1, var(--ctp-ext-overlay1, 152, 147, 165));
  --ctp-overlay0: var(--ctp-custom-overlay0, var(--ctp-ext-overlay0, 161, 156, 173));
  --ctp-surface2: var(--ctp-custom-surface2, var(--ctp-ext-surface2, 202, 193, 185));
  --ctp-surface1: var(--ctp-custom-surface1, var(--ctp-ext-surface1, 209, 201, 194));
  --ctp-surface0: var(--ctp-custom-surface0, var(--ctp-ext-surface0, 220, 211, 203));
  --ctp-base: var(--ctp-custom-base, var(--ctp-ext-base, 238, 230, 221));
  --ctp-mantle: var(--ctp-custom-mantle, var(--ctp-ext-mantle, 230, 219, 209));
  --ctp-crust: var(--ctp-custom-crust, var(--ctp-ext-crust, 221, 208, 198));
}

.theme-dark.ctp-frappe, .ctp-frappe .themed-color-wrapper > .theme-dark {
  --ctp-rosewater: var(--ctp-custom-rosewater, var(--ctp-ext-rosewater, 242, 213, 207));
  --ctp-flamingo: var(--ctp-custom-flamingo, var(--ctp-ext-flamingo, 238, 190, 190));
  --ctp-pink: var(--ctp-custom-pink, var(--ctp-ext-pink, 244, 184, 228));
  --ctp-mauve: var(--ctp-custom-mauve, var(--ctp-ext-mauve, 202, 158, 230));
  --ctp-red: var(--ctp-custom-red, var(--ctp-ext-red, 231, 130, 132));
  --ctp-maroon: var(--ctp-custom-maroon, var(--ctp-ext-maroon, 234, 153, 156));
  --ctp-peach: var(--ctp-custom-peach, var(--ctp-ext-peach, 239, 159, 118));
  --ctp-yellow: var(--ctp-custom-yellow, var(--ctp-ext-yellow, 229, 200, 144));
  --ctp-green: var(--ctp-custom-green, var(--ctp-ext-green, 166, 209, 137));
  --ctp-teal: var(--ctp-custom-teal, var(--ctp-ext-teal, 129, 200, 190));
  --ctp-sky: var(--ctp-custom-sky, var(--ctp-ext-sky, 153, 209, 219));
  --ctp-sapphire: var(--ctp-custom-sapphire, var(--ctp-ext-sapphire, 133, 193, 220));
  --ctp-blue: var(--ctp-custom-blue, var(--ctp-ext-blue, 140, 170, 238));
  --ctp-lavender: var(--ctp-custom-lavender, var(--ctp-ext-lavender, 186, 187, 241));
  --ctp-text: var(--ctp-custom-text, var(--ctp-ext-text, 198, 206, 239));
  --ctp-subtext1: var(--ctp-custom-subtext1, var(--ctp-ext-subtext1, 181, 189, 220));
  --ctp-subtext0: var(--ctp-custom-subtext0, var(--ctp-ext-subtext0, 165, 172, 201));
  --ctp-overlay2: var(--ctp-custom-overlay2, var(--ctp-ext-overlay2, 148, 155, 183));
  --ctp-overlay1: var(--ctp-custom-overlay1, var(--ctp-ext-overlay1, 131, 138, 164));
  --ctp-overlay0: var(--ctp-custom-overlay0, var(--ctp-ext-overlay0, 115, 120, 145));
  --ctp-surface2: var(--ctp-custom-surface2, var(--ctp-ext-surface2, 98, 103, 126));
  --ctp-surface1: var(--ctp-custom-surface1, var(--ctp-ext-surface1, 81, 86, 108));
  --ctp-surface0: var(--ctp-custom-surface0, var(--ctp-ext-surface0, 65, 69, 89));
  --ctp-base: var(--ctp-custom-base, var(--ctp-ext-base, 48, 52, 70));
  --ctp-mantle: var(--ctp-custom-mantle, var(--ctp-ext-mantle, 41, 44, 60));
  --ctp-crust: var(--ctp-custom-crust, var(--ctp-ext-crust, 35, 38, 52));
}

.theme-dark.ctp-macchiato, .ctp-macchiato .themed-color-wrapper > .theme-dark {
  --ctp-rosewater: var(--ctp-custom-rosewater, var(--ctp-ext-rosewater, 244, 219, 214));
  --ctp-flamingo: var(--ctp-custom-flamingo, var(--ctp-ext-flamingo, 240, 198, 198));
  --ctp-pink: var(--ctp-custom-pink, var(--ctp-ext-pink, 245, 189, 230));
  --ctp-mauve: var(--ctp-custom-mauve, var(--ctp-ext-mauve, 198, 160, 246));
  --ctp-red: var(--ctp-custom-red, var(--ctp-ext-red, 237, 135, 150));
  --ctp-maroon: var(--ctp-custom-maroon, var(--ctp-ext-maroon, 238, 153, 160));
  --ctp-peach: var(--ctp-custom-peach, var(--ctp-ext-peach, 245, 169, 127));
  --ctp-yellow: var(--ctp-custom-yellow, var(--ctp-ext-yellow, 238, 212, 159));
  --ctp-green: var(--ctp-custom-green, var(--ctp-ext-green, 166, 218, 149));
  --ctp-teal: var(--ctp-custom-teal, var(--ctp-ext-teal, 139, 213, 202));
  --ctp-sky: var(--ctp-custom-sky, var(--ctp-ext-sky, 145, 215, 227));
  --ctp-sapphire: var(--ctp-custom-sapphire, var(--ctp-ext-sapphire, 125, 196, 228));
  --ctp-blue: var(--ctp-custom-blue, var(--ctp-ext-blue, 138, 173, 244));
  --ctp-lavender: var(--ctp-custom-lavender, var(--ctp-ext-lavender, 183, 189, 248));
  --ctp-text: var(--ctp-custom-text, var(--ctp-ext-text, 197, 207, 245));
  --ctp-subtext1: var(--ctp-custom-subtext1, var(--ctp-ext-subtext1, 179, 188, 224));
  --ctp-subtext0: var(--ctp-custom-subtext0, var(--ctp-ext-subtext0, 161, 170, 203));
  --ctp-overlay2: var(--ctp-custom-overlay2, var(--ctp-ext-overlay2, 143, 151, 183));
  --ctp-overlay1: var(--ctp-custom-overlay1, var(--ctp-ext-overlay1, 125, 132, 162));
  --ctp-overlay0: var(--ctp-custom-overlay0, var(--ctp-ext-overlay0, 108, 114, 141));
  --ctp-surface2: var(--ctp-custom-surface2, var(--ctp-ext-surface2, 90, 95, 120));
  --ctp-surface1: var(--ctp-custom-surface1, var(--ctp-ext-surface1, 72, 76, 100));
  --ctp-surface0: var(--ctp-custom-surface0, var(--ctp-ext-surface0, 54, 58, 79));
  --ctp-base: var(--ctp-custom-base, var(--ctp-ext-base, 36, 39, 58));
  --ctp-mantle: var(--ctp-custom-mantle, var(--ctp-ext-mantle, 30, 32, 48));
  --ctp-crust: var(--ctp-custom-crust, var(--ctp-ext-crust, 24, 25, 38));
}

.theme-dark.ctp-mocha, .ctp-mocha .themed-color-wrapper > .theme-dark {
  --ctp-rosewater: var(--ctp-custom-rosewater, var(--ctp-ext-rosewater, 245, 224, 220));
  --ctp-flamingo: var(--ctp-custom-flamingo, var(--ctp-ext-flamingo, 242, 205, 205));
  --ctp-pink: var(--ctp-custom-pink, var(--ctp-ext-pink, 245, 194, 231));
  --ctp-mauve: var(--ctp-custom-mauve, var(--ctp-ext-mauve, 203, 166, 247));
  --ctp-red: var(--ctp-custom-red, var(--ctp-ext-red, 243, 139, 168));
  --ctp-maroon: var(--ctp-custom-maroon, var(--ctp-ext-maroon, 235, 160, 172));
  --ctp-peach: var(--ctp-custom-peach, var(--ctp-ext-peach, 250, 179, 135));
  --ctp-yellow: var(--ctp-custom-yellow, var(--ctp-ext-yellow, 249, 226, 175));
  --ctp-green: var(--ctp-custom-green, var(--ctp-ext-green, 166, 227, 161));
  --ctp-teal: var(--ctp-custom-teal, var(--ctp-ext-teal, 148, 226, 213));
  --ctp-sky: var(--ctp-custom-sky, var(--ctp-ext-sky, 137, 220, 235));
  --ctp-sapphire: var(--ctp-custom-sapphire, var(--ctp-ext-sapphire, 116, 199, 236));
  --ctp-blue: var(--ctp-custom-blue, var(--ctp-ext-blue, 135, 176, 249));
  --ctp-lavender: var(--ctp-custom-lavender, var(--ctp-ext-lavender, 180, 190, 254));
  --ctp-text: var(--ctp-custom-text, var(--ctp-ext-text, 198, 208, 245));
  --ctp-subtext1: var(--ctp-custom-subtext1, var(--ctp-ext-subtext1, 179, 188, 223));
  --ctp-subtext0: var(--ctp-custom-subtext0, var(--ctp-ext-subtext0, 161, 168, 201));
  --ctp-overlay2: var(--ctp-custom-overlay2, var(--ctp-ext-overlay2, 142, 149, 179));
  --ctp-overlay1: var(--ctp-custom-overlay1, var(--ctp-ext-overlay1, 123, 129, 157));
  --ctp-overlay0: var(--ctp-custom-overlay0, var(--ctp-ext-overlay0, 105, 109, 134));
  --ctp-surface2: var(--ctp-custom-surface2, var(--ctp-ext-surface2, 86, 89, 112));
  --ctp-surface1: var(--ctp-custom-surface1, var(--ctp-ext-surface1, 67, 70, 90));
  --ctp-surface0: var(--ctp-custom-surface0, var(--ctp-ext-surface0, 49, 50, 68));
  --ctp-base: var(--ctp-custom-base, var(--ctp-ext-base, 30, 30, 46));
  --ctp-mantle: var(--ctp-custom-mantle, var(--ctp-ext-mantle, 24, 24, 37));
  --ctp-crust: var(--ctp-custom-crust, var(--ctp-ext-crust, 17, 17, 27));
}

.theme-dark.ctp-mocha-old, .ctp-mocha-old .themed-color-wrapper > .theme-dark {
  --ctp-rosewater: var(--ctp-custom-rosewater, var(--ctp-ext-rosewater, 245, 224, 220));
  --ctp-flamingo: var(--ctp-custom-flamingo, var(--ctp-ext-flamingo, 242, 205, 205));
  --ctp-pink: var(--ctp-custom-pink, var(--ctp-ext-pink, 245, 194, 231));
  --ctp-mauve: var(--ctp-custom-mauve, var(--ctp-ext-mauve, 203, 166, 247));
  --ctp-red: var(--ctp-custom-red, var(--ctp-ext-red, 243, 139, 168));
  --ctp-maroon: var(--ctp-custom-maroon, var(--ctp-ext-maroon, 235, 160, 172));
  --ctp-peach: var(--ctp-custom-peach, var(--ctp-ext-peach, 250, 179, 135));
  --ctp-yellow: var(--ctp-custom-yellow, var(--ctp-ext-yellow, 249, 226, 175));
  --ctp-green: var(--ctp-custom-green, var(--ctp-ext-green, 166, 227, 161));
  --ctp-teal: var(--ctp-custom-teal, var(--ctp-ext-teal, 148, 226, 213));
  --ctp-sky: var(--ctp-custom-sky, var(--ctp-ext-sky, 137, 220, 235));
  --ctp-sapphire: var(--ctp-custom-sapphire, var(--ctp-ext-sapphire, 116, 199, 236));
  --ctp-blue: var(--ctp-custom-blue, var(--ctp-ext-blue, 135, 176, 249));
  --ctp-lavender: var(--ctp-custom-lavender, var(--ctp-ext-lavender, 180, 190, 254));
  --ctp-text: var(--ctp-custom-text, var(--ctp-ext-text, 217, 224, 238));
  --ctp-subtext1: var(--ctp-custom-subtext1, var(--ctp-ext-subtext1, 211, 205, 214));
  --ctp-subtext0: var(--ctp-custom-subtext0, var(--ctp-ext-subtext0, 190, 179, 193));
  --ctp-overlay2: var(--ctp-custom-overlay2, var(--ctp-ext-overlay2, 167, 156, 176));
  --ctp-overlay1: var(--ctp-custom-overlay1, var(--ctp-ext-overlay1, 152, 139, 162));
  --ctp-overlay0: var(--ctp-custom-overlay0, var(--ctp-ext-overlay0, 109, 107, 125));
  --ctp-surface2: var(--ctp-custom-surface2, var(--ctp-ext-surface2, 87, 82, 105));
  --ctp-surface1: var(--ctp-custom-surface1, var(--ctp-ext-surface1, 45, 40, 72));
  --ctp-surface0: var(--ctp-custom-surface0, var(--ctp-ext-surface0, 48, 45, 65));
  --ctp-base: var(--ctp-custom-base, var(--ctp-ext-base, 30, 30, 46));
  --ctp-mantle: var(--ctp-custom-mantle, var(--ctp-ext-mantle, 26, 24, 38));
  --ctp-crust: var(--ctp-custom-crust, var(--ctp-ext-crust, 22, 19, 32));
}

.theme-dark {
  --ctp-rosewater: var(--ctp-custom-rosewater, var(--ctp-ext-rosewater, 245, 224, 220));
  --ctp-flamingo: var(--ctp-custom-flamingo, var(--ctp-ext-flamingo, 242, 205, 205));
  --ctp-pink: var(--ctp-custom-pink, var(--ctp-ext-pink, 245, 194, 231));
  --ctp-mauve: var(--ctp-custom-mauve, var(--ctp-ext-mauve, 203, 166, 247));
  --ctp-red: var(--ctp-custom-red, var(--ctp-ext-red, 243, 139, 168));
  --ctp-maroon: var(--ctp-custom-maroon, var(--ctp-ext-maroon, 235, 160, 172));
  --ctp-peach: var(--ctp-custom-peach, var(--ctp-ext-peach, 250, 179, 135));
  --ctp-yellow: var(--ctp-custom-yellow, var(--ctp-ext-yellow, 249, 226, 175));
  --ctp-green: var(--ctp-custom-green, var(--ctp-ext-green, 166, 227, 161));
  --ctp-teal: var(--ctp-custom-teal, var(--ctp-ext-teal, 148, 226, 213));
  --ctp-sky: var(--ctp-custom-sky, var(--ctp-ext-sky, 137, 220, 235));
  --ctp-sapphire: var(--ctp-custom-sapphire, var(--ctp-ext-sapphire, 116, 199, 236));
  --ctp-blue: var(--ctp-custom-blue, var(--ctp-ext-blue, 135, 176, 249));
  --ctp-lavender: var(--ctp-custom-lavender, var(--ctp-ext-lavender, 180, 190, 254));
  --ctp-text: var(--ctp-custom-text, var(--ctp-ext-text, 198, 208, 245));
  --ctp-subtext1: var(--ctp-custom-subtext1, var(--ctp-ext-subtext1, 179, 188, 223));
  --ctp-subtext0: var(--ctp-custom-subtext0, var(--ctp-ext-subtext0, 161, 168, 201));
  --ctp-overlay2: var(--ctp-custom-overlay2, var(--ctp-ext-overlay2, 142, 149, 179));
  --ctp-overlay1: var(--ctp-custom-overlay1, var(--ctp-ext-overlay1, 123, 129, 157));
  --ctp-overlay0: var(--ctp-custom-overlay0, var(--ctp-ext-overlay0, 105, 109, 134));
  --ctp-surface2: var(--ctp-custom-surface2, var(--ctp-ext-surface2, 86, 89, 112));
  --ctp-surface1: var(--ctp-custom-surface1, var(--ctp-ext-surface1, 67, 70, 90));
  --ctp-surface0: var(--ctp-custom-surface0, var(--ctp-ext-surface0, 49, 50, 68));
  --ctp-base: var(--ctp-custom-base, var(--ctp-ext-base, 30, 30, 46));
  --ctp-mantle: var(--ctp-custom-mantle, var(--ctp-ext-mantle, 24, 24, 37));
  --ctp-crust: var(--ctp-custom-crust, var(--ctp-ext-crust, 17, 17, 27));
}

.theme-light {
  --ctp-rosewater: var(--ctp-custom-rosewater, var(--ctp-ext-rosewater, 222, 149, 132));
  --ctp-flamingo: var(--ctp-custom-flamingo, var(--ctp-ext-flamingo, 221, 120, 120));
  --ctp-pink: var(--ctp-custom-pink, var(--ctp-ext-pink, 236, 131, 208));
  --ctp-mauve: var(--ctp-custom-mauve, var(--ctp-ext-mauve, 136, 57, 239));
  --ctp-red: var(--ctp-custom-red, var(--ctp-ext-red, 210, 15, 57));
  --ctp-maroon: var(--ctp-custom-maroon, var(--ctp-ext-maroon, 230, 69, 83));
  --ctp-peach: var(--ctp-custom-peach, var(--ctp-ext-peach, 254, 100, 11));
  --ctp-yellow: var(--ctp-custom-yellow, var(--ctp-ext-yellow, 228, 147, 32));
  --ctp-green: var(--ctp-custom-green, var(--ctp-ext-green, 64, 160, 43));
  --ctp-teal: var(--ctp-custom-teal, var(--ctp-ext-teal, 23, 146, 153));
  --ctp-sky: var(--ctp-custom-sky, var(--ctp-ext-sky, 4, 165, 229));
  --ctp-sapphire: var(--ctp-custom-sapphire, var(--ctp-ext-sapphire, 32, 159, 181));
  --ctp-blue: var(--ctp-custom-blue, var(--ctp-ext-blue, 42, 110, 245));
  --ctp-lavender: var(--ctp-custom-lavender, var(--ctp-ext-lavender, 114, 135, 253));
  --ctp-text: var(--ctp-custom-text, var(--ctp-ext-text, 76, 79, 105));
  --ctp-subtext1: var(--ctp-custom-subtext1, var(--ctp-ext-subtext1, 92, 95, 119));
  --ctp-subtext0: var(--ctp-custom-subtext0, var(--ctp-ext-subtext0, 108, 111, 133));
  --ctp-overlay2: var(--ctp-custom-overlay2, var(--ctp-ext-overlay2, 124, 127, 147));
  --ctp-overlay1: var(--ctp-custom-overlay1, var(--ctp-ext-overlay1, 140, 143, 161));
  --ctp-overlay0: var(--ctp-custom-overlay0, var(--ctp-ext-overlay0, 156, 160, 176));
  --ctp-surface2: var(--ctp-custom-surface2, var(--ctp-ext-surface2, 172, 176, 190));
  --ctp-surface1: var(--ctp-custom-surface1, var(--ctp-ext-surface1, 188, 192, 204));
  --ctp-surface0: var(--ctp-custom-surface0, var(--ctp-ext-surface0, 204, 208, 218));
  --ctp-base: var(--ctp-custom-base, var(--ctp-ext-base, 239, 241, 245));
  --ctp-mantle: var(--ctp-custom-mantle, var(--ctp-ext-mantle, 230, 233, 239));
  --ctp-crust: var(--ctp-custom-crust, var(--ctp-ext-crust, 220, 224, 232));
}

/*------------------Selecting Accents-------------------*/
.anuppuccin-accent-toggle.ctp-accent-rosewater {
  --ctp-accent: var(--ctp-rosewater);
}

.theme-light.anuppuccin-accent-toggle.ctp-accent-light-rosewater {
  --ctp-accent: var(--ctp-rosewater);
}

.anp-bold-rosewater {
  --anp-bold-color: var(--ctp-rosewater);
}

.anp-italic-rosewater {
  --anp-italic-color: var(--ctp-rosewater);
}

.anp-highlight-rosewater {
  --anp-highlight-color: var(--ctp-rosewater);
}

.anuppuccin-accent-toggle.ctp-accent-flamingo {
  --ctp-accent: var(--ctp-flamingo);
}

.theme-light.anuppuccin-accent-toggle.ctp-accent-light-flamingo {
  --ctp-accent: var(--ctp-flamingo);
}

.anp-bold-flamingo {
  --anp-bold-color: var(--ctp-flamingo);
}

.anp-italic-flamingo {
  --anp-italic-color: var(--ctp-flamingo);
}

.anp-highlight-flamingo {
  --anp-highlight-color: var(--ctp-flamingo);
}

.anuppuccin-accent-toggle.ctp-accent-pink {
  --ctp-accent: var(--ctp-pink);
}

.theme-light.anuppuccin-accent-toggle.ctp-accent-light-pink {
  --ctp-accent: var(--ctp-pink);
}

.anp-bold-pink {
  --anp-bold-color: var(--ctp-pink);
}

.anp-italic-pink {
  --anp-italic-color: var(--ctp-pink);
}

.anp-highlight-pink {
  --anp-highlight-color: var(--ctp-pink);
}

.anuppuccin-accent-toggle.ctp-accent-mauve {
  --ctp-accent: var(--ctp-mauve);
}

.theme-light.anuppuccin-accent-toggle.ctp-accent-light-mauve {
  --ctp-accent: var(--ctp-mauve);
}

.anp-bold-mauve {
  --anp-bold-color: var(--ctp-mauve);
}

.anp-italic-mauve {
  --anp-italic-color: var(--ctp-mauve);
}

.anp-highlight-mauve {
  --anp-highlight-color: var(--ctp-mauve);
}

.anuppuccin-accent-toggle.ctp-accent-red {
  --ctp-accent: var(--ctp-red);
}

.theme-light.anuppuccin-accent-toggle.ctp-accent-light-red {
  --ctp-accent: var(--ctp-red);
}

.anp-bold-red {
  --anp-bold-color: var(--ctp-red);
}

.anp-italic-red {
  --anp-italic-color: var(--ctp-red);
}

.anp-highlight-red {
  --anp-highlight-color: var(--ctp-red);
}

.anuppuccin-accent-toggle.ctp-accent-maroon {
  --ctp-accent: var(--ctp-maroon);
}

.theme-light.anuppuccin-accent-toggle.ctp-accent-light-maroon {
  --ctp-accent: var(--ctp-maroon);
}

.anp-bold-maroon {
  --anp-bold-color: var(--ctp-maroon);
}

.anp-italic-maroon {
  --anp-italic-color: var(--ctp-maroon);
}

.anp-highlight-maroon {
  --anp-highlight-color: var(--ctp-maroon);
}

.anuppuccin-accent-toggle.ctp-accent-peach {
  --ctp-accent: var(--ctp-peach);
}

.theme-light.anuppuccin-accent-toggle.ctp-accent-light-peach {
  --ctp-accent: var(--ctp-peach);
}

.anp-bold-peach {
  --anp-bold-color: var(--ctp-peach);
}

.anp-italic-peach {
  --anp-italic-color: var(--ctp-peach);
}

.anp-highlight-peach {
  --anp-highlight-color: var(--ctp-peach);
}

.anuppuccin-accent-toggle.ctp-accent-yellow {
  --ctp-accent: var(--ctp-yellow);
}

.theme-light.anuppuccin-accent-toggle.ctp-accent-light-yellow {
  --ctp-accent: var(--ctp-yellow);
}

.anp-bold-yellow {
  --anp-bold-color: var(--ctp-yellow);
}

.anp-italic-yellow {
  --anp-italic-color: var(--ctp-yellow);
}

.anp-highlight-yellow {
  --anp-highlight-color: var(--ctp-yellow);
}

.anuppuccin-accent-toggle.ctp-accent-green {
  --ctp-accent: var(--ctp-green);
}

.theme-light.anuppuccin-accent-toggle.ctp-accent-light-green {
  --ctp-accent: var(--ctp-green);
}

.anp-bold-green {
  --anp-bold-color: var(--ctp-green);
}

.anp-italic-green {
  --anp-italic-color: var(--ctp-green);
}

.anp-highlight-green {
  --anp-highlight-color: var(--ctp-green);
}

.anuppuccin-accent-toggle.ctp-accent-teal {
  --ctp-accent: var(--ctp-teal);
}

.theme-light.anuppuccin-accent-toggle.ctp-accent-light-teal {
  --ctp-accent: var(--ctp-teal);
}

.anp-bold-teal {
  --anp-bold-color: var(--ctp-teal);
}

.anp-italic-teal {
  --anp-italic-color: var(--ctp-teal);
}

.anp-highlight-teal {
  --anp-highlight-color: var(--ctp-teal);
}

.anuppuccin-accent-toggle.ctp-accent-sky {
  --ctp-accent: var(--ctp-sky);
}

.theme-light.anuppuccin-accent-toggle.ctp-accent-light-sky {
  --ctp-accent: var(--ctp-sky);
}

.anp-bold-sky {
  --anp-bold-color: var(--ctp-sky);
}

.anp-italic-sky {
  --anp-italic-color: var(--ctp-sky);
}

.anp-highlight-sky {
  --anp-highlight-color: var(--ctp-sky);
}

.anuppuccin-accent-toggle.ctp-accent-sapphire {
  --ctp-accent: var(--ctp-sapphire);
}

.theme-light.anuppuccin-accent-toggle.ctp-accent-light-sapphire {
  --ctp-accent: var(--ctp-sapphire);
}

.anp-bold-sapphire {
  --anp-bold-color: var(--ctp-sapphire);
}

.anp-italic-sapphire {
  --anp-italic-color: var(--ctp-sapphire);
}

.anp-highlight-sapphire {
  --anp-highlight-color: var(--ctp-sapphire);
}

.anuppuccin-accent-toggle.ctp-accent-blue {
  --ctp-accent: var(--ctp-blue);
}

.theme-light.anuppuccin-accent-toggle.ctp-accent-light-blue {
  --ctp-accent: var(--ctp-blue);
}

.anp-bold-blue {
  --anp-bold-color: var(--ctp-blue);
}

.anp-italic-blue {
  --anp-italic-color: var(--ctp-blue);
}

.anp-highlight-blue {
  --anp-highlight-color: var(--ctp-blue);
}

.anuppuccin-accent-toggle.ctp-accent-lavender {
  --ctp-accent: var(--ctp-lavender);
}

.theme-light.anuppuccin-accent-toggle.ctp-accent-light-lavender {
  --ctp-accent: var(--ctp-lavender);
}

.anp-bold-lavender {
  --anp-bold-color: var(--ctp-lavender);
}

.anp-italic-lavender {
  --anp-italic-color: var(--ctp-lavender);
}

.anp-highlight-lavender {
  --anp-highlight-color: var(--ctp-lavender);
}

/*------------------Actual Configs--------------------*/
.anuppuccin-accent-toggle {
  --color-accent: rgb(var(--ctp-accent));
  --color-accent-1: rgb(var(--ctp-accent));
  --color-accent-2: rgba(var(--ctp-accent), 0.9);
  --text-selection: rgba(var(--ctp-accent), 0.25);
  --interactive-accent: rgb(var(--ctp-accent));
  --interactive-accent-hover: rgba(var(--ctp-accent), 0.9);
  --text-accent: rgb(var(--ctp-accent));
  --text-accent-hover: rgb(var(--ctp-accent));
  --text-highlight-bg: rgba(var(--ctp-accent), 0.4);
  --text-highlight-bg-active: rgba(var(--ctp-accent), 0.6);
  --interactive-accent: rgb(var(--ctp-accent));
  --interactive-accent-rgb: var(--ctp-accent);
  --interactive-accent-hover: rgb(var(--ctp-accent));
  --blockquote-border-color: rgb(var(--ctp-accent));
  --background-modifier-active: rgba(var(--ctp-accent), 0.1);
  --background-modifier-active-hover: rgba(var(--ctp-accent), 0.15);
  --link-unresolved-decoration-color: rgba(var(--ctp-accent), 0.3);
  --tag-background: rgba(var(--ctp-accent), 0.1);
  --tag-background-hover: rgba(var(--ctp-accent), 0.2);
  --tag-border-color: rgba(var(--ctp-accent), 0.15);
  --tag-border-color-hover: rgba(var(--ctp-accent), 0.15);
  --nav-item-background-selected: rgba(var(--ctp-accent), 0.2);
  --heading-formatting: rgb(var(--ctp-accent));
  --icon-color-focused: rgb(var(--ctp-accent));
}

.theme-dark, .theme-light {
  --mono-rgb-0: var(--ctp-crust);
  --mono-rgb-100: var(--ctp-text);
  --color-red-rgb: var(--ctp-red);
  --color-red: rgb(var(--ctp-red));
  --color-green-rgb: var(--ctp-green);
  --color-green: rgb(var(--ctp-green));
  --color-orange-rgb: var(--ctp-peach);
  --color-orange: rgb(var(--ctp-peach));
  --color-yellow-rgb: var(--ctp-yellow);
  --color-yellow: rgb(var(--ctp-yellow));
  --color-cyan-rgb: var(--ctp-sky);
  --color-cyan: rgb(var(--ctp-sky));
  --color-blue-rgb: var(--ctp-blue);
  --color-blue: rgb(var(--ctp-blue));
  --color-purple-rgb: var(--ctp-lavender);
  --color-purple: rgb(var(--ctp-lavender));
  --color-pink-rgb: var(--ctp-pink);
  --color-pink: rgb(var(--ctp-pink));
  --color-base-00: rgb(var(--ctp-crust));
  --color-base-10: rgb(var(--ctp-mantle));
  --color-base-20: rgb(var(--ctp-base));
  --color-base-25: rgb(var(--ctp-surface0));
  --color-base-30: rgb(var(--ctp-surface1));
  --color-base-35: rgb(var(--ctp-surface2));
  --color-base-40: rgb(var(--ctp-overlay0));
  --color-base-50: rgb(var(--ctp-overlay1));
  --color-base-60: rgb(var(--ctp-overlay2));
  --color-base-70: rgb(var(--ctp-subtext0));
  --color-base-100: rgb(var(--ctp-text));
  --text-highlight-bg: rgba(var(--ctp-yellow), 0.2);
  --text-highlight-bg-active: rgba(var(--ctp-yellow), 0.4);
  --input-shadow: inset 0 0.5px 0.5px 0.5px rgba(var(255, 255, 255), 0.09),
  0 2px 4px 0 rgba(var(--ctp-crust),.15),
  0 1px 1.5px 0 rgba(var(--ctp-crust),.1),
  0 1px 2px 0 rgba(var(--ctp-crust),.2),
  0 0 0 0 transparent;
  --input-shadow-hover: inset 0 0.5px 1px 0.5px rgba(var(255, 255, 255), 0.16),
  0 2px 3px 0 rgba(var(--ctp-crust),.3),
  0 1px 1.5px 0 rgba(var(--ctp-crust),.2),
  0 1px 2px 0 rgba(var(--ctp-crust),.4),
  0 0 0 0 transparent;
  --shadow-s: 0px 1px 2px rgba(var(--ctp-crust), 0.121),
  0px 3.4px 6.7px rgba(var(--ctp-crust), 0.179),
  0px 15px 30px rgba(var(--ctp-crust), 0.3);
  --shadow-l: 0px 1.8px 7.3px rgba(var(--ctp-crust), 0.071),
  0px 6.3px 24.7px rgba(var(--ctp-crust), 0.112),
  0px 30px 90px rgba(var(--ctp-crust), 0.2);
  --background-primary: rgb(var(--ctp-base));
  --background-primary-alt: rgb(var(--ctp-mantle));
  --background-secondary: rgb(var(--ctp-mantle));
  --background-secondary-alt: rgb(var(--ctp-crust));
  --background-modifier-hover: rgba(var(--ctp-text), 0.075);
  --background-modifier-form-field: rgba(var(--ctp-crust), 0.3);
  --background-modifier-success: rgba(var(--ctp-green), 1);
  --background-modifier-success-hover: rgba(var(--ctp-green), 0.9);
  --background-modifier-success-rgb: var(--ctp-green);
  --background-modifier-error: rgba(var(--ctp-red), 1);
  --background-modifier-error-rgb: var(--ctp-red);
  --background-modifier-error-hover: rgba(var(--ctp-red), 0.9);
  --background-modifier-message: rgba(var(--ctp-crust), 0.9);
  --modal-border-color:rgb(var(--ctp-surface0));
  --text-normal: rgb(var(--ctp-text));
  --text-muted: rgb(var(--ctp-overlay2));
  --text-muted-rgb: var(--ctp-overlay2);
  --text-faint: rgb(var(--ctp-subtext0));
  --text-error: rgb(var(--ctp-red));
  --text-error-hover: rgba(var(--ctp-red), 0.8);
  --text-success: rgb(var(--ctp-green));
  --text-on-accent: rgb(var(--ctp-base));
  --interactive-normal: rgb(var(--ctp-surface0));
  --interactive-hover: rgb(var(--ctp-surface1));
  --interactive-success: rgb(var(--ctp-green));
  --workspace-background-translucent: rgba(var(--ctp-crust), 0.6);
  --blockquote-background-color: rgba(var(--ctp-crust), 0.5);
  --width-image-gallery: 200px;
  --min-width-image: 50%;
  --max-width-image: 90%;
  --drag-ghost-background: rgb(var(--ctp-text));
  --drag-ghost-text-color: rgb(var(--ctp-crust));
  --nav-indentation-guide-color: rgba(var(--ctp-crust), 0.4);
  --titlebar-text-color-focused: var(--color-accent);
  --callout-title-padding: var(--size-4-2);
  --table-border-width: var(--anp-table-thickness, 1px);
  --icon-color-focused: var(--color-accent);
  --embed-block-shadow-hover: none;
}

.theme-dark {
  color-scheme: dark;
  --highlight-mix-blend-mode: none;
  --background-modifier-border: rgb(var(--ctp-surface0));
  --background-modifier-border-hover: rgb(var(--ctp-surface1));
  --background-modifier-border-focus: rgb(var(--ctp-surface2));
  --anp-speech-bubble-opacity: var(--anp-sp-op-dark, 0.9);
  --background-modifier-cover: rgba(var(--ctp-mantle), 0.4);
}
.theme-dark.theme-dark {
  --canvas-color: var(--ctp-overlay0);
}

.theme-light {
  color-scheme: light;
  --highlight-mix-blend-mode: none;
  --background-modifier-border: rgb(var(--ctp-surface1));
  --background-modifier-border-hover: rgb(var(--ctp-surface2));
  --background-modifier-border-focus: rgb(var(--ctp-overlay0));
  --anp-speech-bubble-opacity: var(--anp-sp-op-light, 0.5);
  --background-modifier-cover: #00000022;
}
.theme-light.theme-light {
  --canvas-color: var(--ctp-overlay0);
}

body {
  --anp-rainbow-folder-bg-opacity: 1;
  --anp-rainbow-folder-border-opacity: 1;
  --anp-rainbow-folder-collapse-border-custom: #00000044;
  --anp-preview-width-pct: 95%;
  --anp-preview-width-max: 800px;
  --anp-header-margin-value: 15px;
  --anp-header-font: "Noto Serif";
  --tab-stacked-pane-width: calc(var(--anp-tab-stacked-pane-width, 1)*var(--file-line-width));
  --anp-table-width-pct: 100%;
  --code-background: var(--anp-code-bg-color, var(--background-secondary-alt));
  --canvas-color: var(--ctp-overlay2);
  --card-background-color: rgb(var(--ctp-crust));
  --card-foreground-color: rgb(var(--ctp-base));
  --tab-inactive-color: rgb(var(--ctp-mantle));
  --code-normal: var(--anp-code-text-color, var(--text-normal));
  --callout-padding: 0;
  --callout-title-padding: var(--size-4-2);
  --callout-content-padding: var(--size-4-2);
  --background-modifier-active: hsla(var(--color-accent-hsl), 0.1);
  --pdf-background: var(--background-primary);
  --pdf-dark-opacity: 1;
  --pdf-shadow: 0 0 0 1px rgba(0, 0, 0, 0.15), 0 2px 8px transparent;
  --pdf-sidebar-background: var(--background-primary);
  --pdf-thumbnail-shadow: 0 0 0 1px rgba(0, 0, 0, 0.15), 0 2px 8px transparent;
}
body.theme-light {
  --anp-background-image: var(--anp-background-image-light);
  --anp-custom-bg-brightness: var(--anp-custom-bg-brightness-light);
  --anp-custom-bg-blur: var(--anp-custom-bg-blur-light);
  --anp-custom-bg-card-fg-opacity: var(--anp-custom-bg-card-fg-opacity-light);
}
body.theme-dark {
  --anp-background-image: var(--anp-background-image-dark);
  --anp-custom-bg-brightness: var(--anp-custom-bg-brightness-dark);
  --anp-custom-bg-blur: var(--anp-custom-bg-blur-dark);
  --anp-custom-bg-card-fg-opacity: var(--anp-custom-bg-card-fg-opacity-dark);
}

/*-Highlight current line-*/
.anp-current-line .markdown-source-view .cm-active.cm-line {
  background-color: rgba(var(--ctp-surface1), 0.4);
}

.anp-current-line-border .markdown-source-view .cm-active.cm-line {
  border-left: 2px solid var(--interactive-accent);
  margin-left: -2px !important;
  background-color: rgba(var(--ctp-surface1), 0.4);
}

.anp-current-line-border-only .markdown-source-view .cm-active.cm-line::before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: -1.5rem;
  width: 2px;
  background-color: rgba(var(--interactive-accent-rgb), 0.3);
}
.anp-current-line-border-only .markdown-source-view .cm-focused .cm-active.cm-line::before {
  background-color: var(--interactive-accent);
}

/*-Custom editor font-*/
.markdown-source-view:not(.is-live-preview) {
  --font-text: var(--anp-editor-font-source, var(--font-text-override)), var(--font-text-theme), var(--font-interface);
  --h1-font: var(--font-text, inherit);
  --h2-font: var(--font-text, inherit);
  --h3-font: var(--font-text, inherit);
  --h4-font: var(--font-text, inherit);
  --h5-font: var(--font-text, inherit);
  --h6-font: var(--font-text, inherit);
}

.markdown-source-view.is-live-preview {
  --font-text: var(--anp-editor-font-lp, var(--font-text-override)), var(--font-text-theme), var(--font-interface);
}

/*-Highlighted gutter number-*/
.cm-lineNumbers .cm-gutterElement.cm-active {
  color: var(--interactive-accent);
}

/*-ZWSP Highlight-*/
.ͼ2 .cm-specialChar {
  color: rgb(var(--ctp-red));
}

/*-Block edit button-*/
.markdown-source-view.mod-cm6 .edit-block-button {
  background-color: var(--background-secondary);
  cursor: pointer;
  border: 1px solid var(--background-modifer-border);
}
.markdown-source-view.mod-cm6 .edit-block-button:hover {
  background-color: var(--background-secondary);
  background-color: var(--background-secondary);
  opacity: 1;
}

.markdown-source-view.mod-cm6 .cm-embed-block:hover .edit-block-button:hover {
  background-color: var(--background-secondary);
  border: 1px solid var(--background-modifer-border);
  opacity: 1;
}

/*-Vim Terminal-*/
.ͼ2 .cm-panels-bottom {
  border-top: 1px solid rgb(var(--ctp-surface1));
}
.ͼ2 .cm-panels-bottom input {
  background-color: transparent;
  font-family: var(--font-monospace);
  width: 100%;
}
.ͼ2 .cm-panels-bottom input:hover, .ͼ2 .cm-panels-bottom input:focus, .ͼ2 .cm-panels-bottom input:focus-visible {
  border: none;
  box-shadow: none;
}

.ͼp .cm-vim-panel {
  font-family: var(--font-monospace);
}

.workspace-tab-header,
.clickable-icon,
.dropdown,
.checkbox-container,
input.slider,
.task-list-item-checkbox,
.vertical-tab-nav-item,
.nav-folder-title,
.nav-file-title,
.tree-item,
.tree-item-self.is-clickable,
.workspace-tab-header-status-icon,
.workspace-tab-header-inner-close-button,
.CodeMirror-foldmarker,
.CodeMirror-foldgutter-open,
.CodeMirror-foldgutter-folded,
.markdown-source-view.mod-cm6 .edit-block-button,
.empty-state-action,
.setting-hotkey-icon,
.setting-add-hotkey-button,
.setting-restore-hotkey-button,
.spellchecker-dictionary-remove-button,
.community-item,
.status-bar-item.mod-clickable,
.titlebar-button,
.button,
.frontmatter-container .frontmatter-container-header,
.card.u-clickable,
.list-item-part.clickable-icon,
.u-clickable,
.document-search-close-button,
.markdown-preview-view .collapse-indicator,
.markdown-source-view.mod-cm6 .cm-fold-indicator .collapse-indicator,
.input[type=color],
.menu-item,
.modal-checkbox-label,
.view-header-breadcrumb,
.nav-action-button,
.tree-item-inner,
.callout.is-collapsible .callout-title,
.modal-close-button {
  cursor: var(--anp-cursor, var(--cursor));
}

/*----------FILE PREVIEW MARGINS--------------*/
.anp-toggle-preview .markdown-preview-section {
  width: var(--anp-preview-width-pct);
  margin: 0 auto;
}

/*---------------HIGHLIGHTS-----------------*/
.markdown-rendered .search-highlight > div {
  box-shadow: none;
  opacity: 0.3;
  background-color: rgba(var(--ctp-text), 0.4);
  mix-blend-mode: var(--highlight-mix-blend-mode);
  border-radius: 2px;
}

.markdown-rendered .search-highlight > div.is-active {
  box-shadow: 0 0 0px 2px rgba(var(--ctp-yellow), 0.5);
  background-color: rgba(var(--ctp-yellow), 0.2);
  opacity: 1;
}

.cm-s-obsidian span.obsidian-search-match-highlight {
  box-shadow: 0 0 0px 2px rgba(var(--ctp-yellow), 0.5);
  background-color: rgba(var(--ctp-yellow), 0.2);
  mix-blend-mode: var(--highlight-mix-blend-mode);
  border-radius: 2px;
}

/* Font weights */
.markdown-preview-view {
  font-weight: var(--anp-font-preview-wt, normal);
}

.markdown-source-view {
  font-weight: var(--anp-font-editor-wt, normal);
}

.is-live-preview {
  font-weight: var(--anp-font-live-preview-wt, normal);
}

.workspace-split:not(.mod-root) .graph-controls.is-close,
.workspace-split:not(.mod-root) .graph-controls,
.workspace-split:not(.mod-root) .graph-controls:not(.is-close) {
  background-color: rgb(var(--ctp-crust));
}

.anp-card-layout .workspace-split:not(.mod-root) .graph-controls.is-close,
.anp-card-layout .workspace-split:not(.mod-root) .graph-controls,
.anp-card-layout .workspace-split:not(.mod-root) .graph-controls:not(.is-close),
.anp-border-layout .workspace-split:not(.mod-root) .graph-controls.is-close,
.anp-border-layout .workspace-split:not(.mod-root) .graph-controls,
.anp-border-layout .workspace-split:not(.mod-root) .graph-controls:not(.is-close),
.workspace-leaf .graph-controls.is-close,
.workspace-leaf .graph-controls,
.workspace-leaf .graph-controls:not(.is-close) {
  background-color: rgb(var(--ctp-mantle));
}

.markdown-rendered.rtl .list-bullet {
  float: right;
  margin-right: -12px;
}
.anp-button-metadata-toggle .markdown-rendered.rtl .frontmatter-container .frontmatter-container-header {
  left: unset;
  right: calc(100% - 32px);
}
.anp-button-metadata-toggle .markdown-rendered.rtl .frontmatter-container .frontmatter-container-header:after {
  margin-left: unset;
  margin-right: 7px;
}
.markdown-rendered.rtl blockquote {
  border-right: var(--blockquote-border-thickness) solid var(--blockquote-border-color);
  border-left: none;
}
.markdown-rendered.rtl .collapse-indicator {
  float: right;
  margin-left: unset;
  margin-right: -22px;
}

.anp-print .print.theme-light, .anp-print .print.theme-dark {
  --ctp-crust: inherit;
  --ctp-mantle: inherit;
  --ctp-base: inherit;
  --ctp-surface0: inherit;
  --ctp-surface1: inherit;
  --ctp-surface2: inherit;
  --ctp-overlay0: inherit;
  --ctp-overlay1: inherit;
  --ctp-overlay2: inherit;
  --ctp-subtext0: inherit;
  --ctp-subtext1: inherit;
  --ctp-text: inherit;
  --ctp-rosewater: inherit;
  --ctp-flamingo: inherit;
  --ctp-red: inherit;
  --ctp-maroon: inherit;
  --ctp-mauve: inherit;
  --ctp-pink: inherit;
  --ctp-peach: inherit;
  --ctp-yellow: inherit;
  --ctp-green: inherit;
  --ctp-teal: inherit;
  --ctp-sky: inherit;
  --ctp-sapphire: inherit;
  --ctp-blue: inherit;
  --ctp-lavender: inherit;
}

@media print {
  .anp-print .print {
    --ctp-crust: inherit;
    --ctp-mantle: inherit;
    --ctp-base: inherit;
    --ctp-surface0: inherit;
    --ctp-surface1: inherit;
    --ctp-surface2: inherit;
    --ctp-overlay0: inherit;
    --ctp-overlay1: inherit;
    --ctp-overlay2: inherit;
    --ctp-subtext0: inherit;
    --ctp-subtext1: inherit;
    --ctp-text: inherit;
    --ctp-rosewater: inherit;
    --ctp-flamingo: inherit;
    --ctp-red: inherit;
    --ctp-maroon: inherit;
    --ctp-mauve: inherit;
    --ctp-pink: inherit;
    --ctp-peach: inherit;
    --ctp-yellow: inherit;
    --ctp-green: inherit;
    --ctp-teal: inherit;
    --ctp-sky: inherit;
    --ctp-sapphire: inherit;
    --ctp-blue: inherit;
    --ctp-lavender: inherit;
  }
  .anp-print .print, body.anp-print, .anp-print .markdown-rendered, :root:has(.anp-print), :root {
    background-color: rgb(var(--ctp-base));
  }
  .anp-print .print .markdown-preview-view {
    color: rgb(var(--ctp-text));
  }
}
.theme-dark.anp-pdf-blend-toggle-dark,
.theme-light.anp-pdf-blend-toggle-light {
  --pdf-background: var(--background-secondary);
  --pdf-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1), 0 2px 8px transparent;
  --pdf-thumbnail-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1), 0 2px 8px transparent;
}
.theme-dark.anp-pdf-blend-toggle-dark .pdf-viewer .canvasWrapper,
.theme-dark.anp-pdf-blend-toggle-dark .pdf-thumbnail-view .thumbnailImage,
.theme-light.anp-pdf-blend-toggle-light .pdf-viewer .canvasWrapper,
.theme-light.anp-pdf-blend-toggle-light .pdf-thumbnail-view .thumbnailImage {
  filter: invert(1) hue-rotate(180deg);
  mix-blend-mode: screen;
}
.theme-dark.anp-pdf-blend-toggle-dark .pdf-viewer,
.theme-light.anp-pdf-blend-toggle-light .pdf-viewer {
  background-color: var(--pdf-background);
}
.theme-dark.anp-pdf-blend-toggle-dark .pdf-viewer .page,
.theme-light.anp-pdf-blend-toggle-light .pdf-viewer .page {
  background-color: var(--background-primary);
  border-color: var(--pdf-background);
}

.textLayer ::selection {
  background: var(--color-accent);
}

.canvas-card-menu {
  box-shadow: none;
  border: 1px solid var(--background-modifier-border);
  background-color: var(--background-secondary);
}
.canvas-card-menu .canvas-card-menu-button svg {
  fill: var(--background-secondary);
}
.canvas-card-menu .canvas-card-menu-button:hover {
  color: var(--text-normal);
}

.canvas-controls button {
  background-color: var(--background-secondary);
}
.canvas-controls button:hover {
  filter: brightness(95%);
}

.anp-canvas-dark-bg .canvas-wrapper {
  background-color: var(--background-secondary);
}
.anp-canvas-dark-bg .canvas-controls button, .anp-canvas-dark-bg .canvas-card-menu {
  background-color: var(--background-primary);
}
.anp-canvas-dark-bg .canvas-card-menu .canvas-card-menu-button svg {
  fill: var(--background-primary);
}

.anuppuccin-accent-toggle .canvas-selection {
  background-color: rgba(var(--ctp-accent), 0.1);
  border: 2px solid var(--color-accent);
}

.canvas-node.is-themed .canvas-node-label {
  color: rgb(var(--canvas-color));
}

.canvas-node-placeholder {
  color: rgb(var(--canvas-color));
}
.canvas-node-placeholder::after {
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
}

.anuppuccin-accent-toggle .canvas-placeholder-message {
  background: rgba(var(--ctp-accent), 0.1);
}

/*-Slider checkboxes-*/
.checkbox-container.is-enabled:after {
  background-color: var(--background-primary);
}

/*-Notices-*/
.notice {
  color: rgb(var(--ctp-text));
}

/*-Recolor copy code button-*/
.markdown-rendered button.copy-code-button {
  background-color: rgb(var(--ctp-crust));
}

/*-Banners are always sharp-*/
.obsidian-banner-wrapper .obsidian-banner .banner-image.banner-image.banner-image.banner-image {
  border-radius: 0px;
}

/*-Padding for banner pages-*/
.obsidian-banner-wrapper .frontmatter-container {
  margin-top: 20px;
}

/*-Disable scrollbars-*/
.anp-toggle-scrollbars *::-webkit-scrollbar {
  display: none;
}

/*-Smoother popover transition-*/
.popover.hover-popover.is-loaded {
  animation: fade 0.2s;
}

.popover .markdown-embed-link {
  --icon-color: var(--text-normal);
  --icon-color-hover: var(--text-faint);
}

/*-Recolor tooltips-*/
.tooltip {
  color: rgb(var(--text));
}

/*-Disable tooltips-*/
.anp-tooltip-toggle .tooltip {
  display: none;
}

/*-Tab icons-*/
.mod-left-split .workspace-tab-header.has-active-menu,
.mod-right-split .workspace-tab-header.has-active-menu,
.mod-left-split .workspace-tab-header.is-active,
.mod-right-split .workspace-tab-header.is-active {
  background-color: var(--background-modifier-active);
}

.mod-left-split .workspace-tab-header.has-active-menu:hover, .mod-right-split .workspace-tab-header.has-active-menu:hover, .mod-left-split .workspace-tab-header.is-active:hover, .mod-right-split .workspace-tab-header.is-active:hover {
  background-color: var(--background-modifier-active-hover);
}

.anp-hide-status-bar .status-bar {
  display: none;
}

.workspace, .horizontal-main-container,
.anp-card-layout .mod-vertical .workspace-tabs,
.anp-border-layout .mod-vertical .workspace-tabs {
  background-color: var(--tab-container-background);
}

/* Align file extension labels to the right in sidebar */
.nav-file-title-content {
  flex-grow: var(--anp-file-label-align);
}

.nav-folder-title-content {
  flex-grow: 1;
}

/* Hide empty frontmatter (uses :has()) */
.frontmatter-container:not(:has(.frontmatter-section)) {
  display: none;
}

/* Inline title visibility */
.inline-title {
  display: var(--anp-inline-title-vis, block);
}

/* Search page hover item fix */
.search-result-file-match:hover {
  background-color: var(--background-modifier-hover);
}

.anuppuccin-accent-toggle .drag-ghost-hidden:before {
  background-color: rgba(var(--ctp-accent), 0.3);
}

/*-Suggestion selected item bg fix-*/
.suggestion-item.is-selected {
  background-color: rgba(var(--ctp-text), 0.075);
}

.mod-community-theme .modal-content .community-modal-details .community-modal-info summary h1:first-child, .mod-community-theme .modal-content .community-modal-details .community-modal-info summary h2:first-child, .mod-community-theme .modal-content .community-modal-details .community-modal-info summary h3:first-child, .mod-community-theme .modal-content .community-modal-details .community-modal-info summary h4:first-child, .mod-community-theme .modal-content .community-modal-details .community-modal-info summary h5:first-child, .mod-community-theme .modal-content .community-modal-details .community-modal-info summary h6:first-child {
  margin-block: 0;
  display: inline;
}

.modal.mod-settings, .modal.mod-community-theme, .modal.kanban-plugin__board-settings-modal, .modal.mod-community-plugin {
  --h1-font: var(--font-interface);
  --h2-font: var(--font-interface);
  --h3-font: var(--font-interface);
  --h4-font: var(--font-interface);
  --h5-font: var(--font-interface);
  --h6-font: var(--font-interface);
  --h1-weight: 600;
  --h1-color: var(--text-normal);
  --h2-color: var(--text-normal);
  --h3-color: var(--text-normal);
  --h4-color: var(--text-normal);
  --h5-color: var(--text-normal);
  --h6-color: var(--text-normal);
}

body {
  --tab-stacked-header-width: var(--anp-stacked-header-width, 40px);
}

.anp-card-layout .workspace .mod-root .workspace-tabs.mod-stacked .workspace-tab-container .workspace-leaf {
  background-color: var(--background-primary);
}

body.is-translucent {
  background-color: transparent;
  --workspace-background-translucent: rgba(var(--ctp-crust), var(--anp-translucency-opacity));
}

.is-translucent:not(.is-fullscreen) {
  --nav-collapse-icon-color: rgba(var(--mono-rgb-100), 0.3);
  --nav-collapse-icon-color-collapsed: rgba(var(--mono-rgb-100), 0.3);
  --divider-color: rgba(var(--ctp-surface0), 0.15);
}

.is-translucent:not(.is-fullscreen) .workspace-ribbon.mod-left,
.is-translucent:not(.is-fullscreen) .workspace-tabs,
.is-translucent:not(.is-fullscreen) .mod-left-split .workspace-tab-header-container,
.is-translucent:not(.is-fullscreen) .mod-right-split .workspace-tab-header-container,
.is-translucent:not(.is-fullscreen) .mod-top .workspace-tab-header-container,
.is-translucent:not(.is-fullscreen) .workspace-tabs .workspace-leaf,
.is-translucent:not(.is-fullscreen) .workspace-ribbon.mod-left:before,
.is-translucent:not(.is-fullscreen) .workspace-split.mod-root,
.is-translucent:not(.is-fullscreen) .workspace-split.mod-root.mod-left-split .view-content,
.is-translucent:not(.is-fullscreen) .view-header,
.is-translucent:not(.is-fullscreen) .horizontal-main-container {
  background-color: transparent;
}
.is-translucent:not(.is-fullscreen) .view-header-title-container:not(.mod-at-end):after {
  background-image: none;
}
.is-translucent:not(.is-fullscreen) .workspace-tabs.mod-stacked .workspace-tab-header, .is-translucent:not(.is-fullscreen) .workspace-tabs.mod-stacked .view-header {
  background-color: var(--background-primary);
}

.is-translucent .workspace {
  background-color: transparent;
}

.is-translucent .workspace-split.mod-left-split.is-sidedock-collapsed .workspace-tabs,
.is-translucent .workspace-split.mod-right-split.is-sidedock-collapsed .workspace-tabs {
  visibility: hidden;
}

.is-translucent.anp-card-layout, .is-translucent.anp-border-layout {
  --card-background-color: transparent;
}

.anp-autohide-titlebar:not(.is-mobile) {
  --title-bar-delay: 150ms;
  --title-bar-duration: 300ms;
  --title-bar-target-translate-y: 18px;
  --title-bar-translate-y: 40px;
  --title-bar-windows-fix: 5;
}
.anp-autohide-titlebar:not(.is-mobile) .workspace-leaf-content .view-header {
  margin-top: calc(var(--title-bar-translate-y) * -1);
  position: relative;
  transition: transform var(--title-bar-duration) var(--title-bar-delay), margin-right 0s calc(var(--title-bar-duration) + var(--title-bar-delay)), margin-right 0s calc(var(--title-bar-duration) + var(--title-bar-delay)), margin-left 0s calc(var(--title-bar-duration) + var(--title-bar-delay)), padding 0s calc(var(--title-bar-duration) + var(--title-bar-delay));
  margin-left: 80px;
  margin-right: 80px;
}
.anp-autohide-titlebar:not(.is-mobile):not(.mod-macos) .workspace-tabs:is(.mod-top-right-space, .mod-top-left-space) .workspace-leaf-content .view-header {
  margin-right: calc(var(--title-bar-windows-fix) * var(--frame-right-space));
  padding: 0;
}
.anp-autohide-titlebar:not(.is-mobile):not(.mod-macos) .workspace-tabs:is(.mod-top-right-space, .mod-top-left-space) .workspace-leaf-content .view-header:is(:hover, :focus-within) {
  margin-right: 0;
  transition: transform var(--title-bar-duration) var(--title-bar-delay), margin-right 0s, padding 0s;
  padding: 0 var(--size-4-3);
}
.anp-autohide-titlebar:not(.is-mobile):not(.mod-macos) .workspace-tabs:is(.mod-top-right-space, .mod-top-left-space) .workspace-leaf-content .view-header::before {
  width: calc(100% + var(--title-bar-windows-fix) * var(--frame-right-space));
  top: var(--header-height);
}
.anp-autohide-titlebar:not(.is-mobile) .workspace-leaf-content .view-header::before {
  bottom: calc(var(--title-bar-target-translate-y) * -1);
  content: "";
  left: -80px;
  position: absolute;
  top: 0;
  transition: transform var(--title-bar-duration) var(--title-bar-delay);
  width: calc(100% + 160px);
}
.anp-autohide-titlebar:not(.is-mobile) .workspace-leaf-content .view-content {
  transition: transform var(--title-bar-duration) var(--title-bar-delay);
}
.anp-autohide-titlebar:not(.is-mobile) .workspace-leaf-content .view-header:is(:hover, :focus-within)::before {
  transform: translateY(var(--title-bar-target-translate-y));
  left: 0px;
  width: 100%;
}
.anp-autohide-titlebar:not(.is-mobile) .workspace-leaf-content .view-header:is(:hover, :focus-within),
.anp-autohide-titlebar:not(.is-mobile) .workspace-leaf-content .view-header:is(:hover, :focus-within) + .view-content {
  transition: transform var(--title-bar-duration) var(--title-bar-delay), margin-right 0s, margin-left 0s, padding 0s calc(var(--title-bar-duration) + var(--title-bar-delay));
  transform: translateY(var(--title-bar-translate-y));
  margin-left: 0;
  margin-right: 0;
}
.anp-autohide-titlebar:not(.is-mobile) .workspace-leaf-content .view-header:not(:hover, :focus-within) .clickable-icon {
  app-region: drag;
}
.anp-autohide-titlebar:not(.is-mobile) .view-header::before {
  z-index: 0;
}
.anp-autohide-titlebar:not(.is-mobile) .view-header > div {
  z-index: 1;
}
.anp-autohide-titlebar:not(.is-mobile) .view-header-title {
  --file-header-font-size: var(--font-ui-medium);
}

body:not(.anp-alternate-tab-toggle):not(.anp-disable-newtab-align) .workspace-split.mod-vertical .workspace-tab-header-spacer {
  display: none;
}
body:not(.anp-alternate-tab-toggle):not(.anp-disable-newtab-align) .workspace-split.mod-vertical .workspace-tab-header-new-tab {
  margin-left: auto;
}

.anp-safari-tab-toggle .workspace-split.mod-vertical.mod-root {
  /*-----VARIABLES---------*/
  --tab-background-inactive: var(--background-secondary-alt);
  --tab-background-active: var(--background-primary);
  --tab-background-inactive-hover: var(--background-primary-secondary);
  --tab-text-color: var(--text-faint);
  --tab-text-color-active: var(--text-muted);
  --tab-text-color-focused: var(--text-muted);
  --tab-text-color-focused-active: var(--text-muted);
  --tab-text-color-focused-highlighted: var(--text-accent);
  --tab-text-color-focused-active-current: var(--text-accent);
  --tab-divider-color: var(--background-modifier-border-hover);
  --tab-width: 200px;
  --tab-max-width: 320px;
  /*-------CONFIGS---------*/
}
.anp-safari-tab-toggle .workspace-split.mod-vertical.mod-root .workspace-tabs .workspace-tab-header-container .workspace-tab-header-container-inner {
  margin: 5px 0 !important;
  gap: var(--anp-safari-tab-gap, 10px);
}
.anp-safari-tab-toggle .workspace-split.mod-vertical.mod-root .workspace-tabs .workspace-tab-header-container .workspace-tab-header-container-inner .workspace-tab-header {
  border-radius: var(--anp-safari-tab-radius, 5px);
  border: var(--anp-safari-border-width, 1px) solid var(--tab-outline-color);
  background-color: var(--tab-background-inactive);
  color: var(--text-muted);
  max-width: var(--tab-max-width);
}
.anp-safari-tab-toggle .workspace-split.mod-vertical.mod-root .workspace-tabs .workspace-tab-header-container .workspace-tab-header-container-inner .workspace-tab-header:not(.is-active) {
  --icon-color: var(--text-muted);
  --icon-color-hover: var(--text-muted);
}
.anp-safari-tab-toggle .workspace-split.mod-vertical.mod-root .workspace-tabs .workspace-tab-header-container .workspace-tab-header-container-inner .workspace-tab-header .workspace-tab-header-inner .workspace-tab-header-inner-title {
  margin-top: 3px;
}
.anp-safari-tab-toggle .workspace-split.mod-vertical.mod-root .workspace-tabs .workspace-tab-header-container .workspace-tab-header-container-inner .workspace-tab-header::before, .anp-safari-tab-toggle .workspace-split.mod-vertical.mod-root .workspace-tabs .workspace-tab-header-container .workspace-tab-header-container-inner .workspace-tab-header::after {
  display: none;
}
.anp-safari-tab-toggle .workspace-split.mod-vertical.mod-root .workspace-tabs .workspace-tab-header-container .workspace-tab-header-container-inner .workspace-tab-header.is-active {
  background-color: var(--tab-background-active);
  max-width: var(--tab-max-width);
  box-shadow: none;
}
.anp-safari-tab-toggle .workspace-split.mod-vertical.mod-root .workspace-tabs .workspace-tab-header-container .workspace-tab-header-container-inner .workspace-tab-header:not(.is-active):hover .workspace-tab-header-inner {
  background-color: transparent;
}
.anp-safari-tab-toggle:not(.anp-disable-newtab-align) .workspace-tab-header-container-inner {
  width: 100%;
}

.anp-safari-tab-toggle.anp-safari-tab-animated .workspace-split.mod-vertical.mod-root .workspace-tabs .workspace-tab-header-container .workspace-tab-header-container-inner .workspace-tab-header {
  transition: max-width 400ms ease-in-out, width 400ms ease-in-out, flex-grow 400ms ease, background-color 150ms;
}
.anp-safari-tab-toggle.anp-safari-tab-animated .workspace-split.mod-vertical.mod-root .workspace-tabs .workspace-tab-header-container .workspace-tab-header-container-inner .workspace-tab-header.is-active {
  flex-grow: 1.67;
  max-width: var(--tab-max-width-active);
}

.anp-alternate-tab-toggle, .anp-default-tab, .anp-safari-tab-toggle {
  --header-height: var(--anp-alt-tab-custom-height, 40px);
  --tab-stacked-header-width: var(--anp-stacked-header-width, 40px);
}

.anp-alternate-tab-toggle .workspace-tabs:not(.mod-stacked),
.anp-safari-tab-toggle .workspace-tabs:not(.mod-stacked) {
  --tab-background: var(--background-secondary);
  --tab-background-active: var(--background-primary);
  --tab-font-size: 12px;
  --tab-height: calc(var(--header-height)*0.7);
  --tab-max-width: 200px;
  --tab-max-width-active: 300px ;
}

.anp-alternate-tab-toggle .workspace-ribbon.mod-left {
  margin-top: var(--header-height);
}

.anp-alternate-tab-toggle .workspace-ribbon.mod-left:before {
  padding-bottom: 0px;
}

.anp-alternate-tab-toggle .workspace-tabs:not(.mod-stacked) .workspace-tab-header::before,
.anp-alternate-tab-toggle .workspace-tabs:not(.mod-stacked) .workspace-tab-header::after,
.anp-alternate-tab-toggle .workspace .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header-inner::after {
  display: none;
}

.anp-alternate-tab-toggle .mod-vertical:not(.mod-left-split):not(.mod-right-split) .workspace-tabs:not(.mod-stacked) .workspace-tab-header-container-inner,
.anp-alternate-tab-toggle .mod-horizontal:not(.mod-left-split):not(.mod-right-split) .workspace-tabs:not(.mod-stacked) .workspace-tab-header-container-inner {
  align-items: center;
  justify-content: var(--anp-safari-tab-align, center);
  margin: 0;
  width: 100%;
  gap: var(--anp-safari-tab-gap, 10px);
}

.anp-disable-newtab-align.anp-alternate-tab-toggle .mod-vertical:not(.mod-left-split):not(.mod-right-split) .workspace-tabs:not(.mod-stacked) .workspace-tab-header-container-inner,
.anp-disable-newtab-align.anp-alternate-tab-toggle .mod-horizontal:not(.mod-left-split):not(.mod-right-split) .workspace-tabs:not(.mod-stacked) .workspace-tab-header-container-inner {
  width: unset;
  gap: var(--anp-safari-tab-gap, 10px);
}

.anp-alternate-tab-toggle .mod-vertical:not(.mod-left-split):not(.mod-right-split) .workspace-tabs:not(.mod-stacked) .workspace-tab-header,
.anp-alternate-tab-toggle .mod-horizontal:not(.mod-left-split):not(.mod-right-split) .workspace-tabs:not(.mod-stacked) .workspace-tab-header {
  background: var(--tab-background);
  border-radius: var(--anp-safari-tab-radius, 6px);
  box-shadow: none !important;
  height: var(--tab-height);
  padding: 0 !important;
  transition: 400ms, background-color 150ms ease-in-out;
}

.anp-alternate-tab-toggle .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header {
  max-width: var(--tab-max-width);
  border: var(--anp-safari-border-width, 1px) solid var(--tab-outline-color);
}

.anp-alternate-tab-toggle.anp-alt-tab-anim-toggle .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header {
  transition: 400ms, background-color 150ms ease-in-out;
}

.anp-alternate-tab-toggle .workspace-tabs:not(.mod-stacked) .workspace-tab-header:first-child {
  margin-left: 0;
}

.anp-alternate-tab-toggle .mod-root:not(.mod-left-split):not(.mod-right-split) .workspace-tabs:not(.mod-stacked) .workspace-tab-header-inner {
  padding: 7px 8px;
}

.anp-alternate-tab-toggle .mod-root:not(.mod-left-split):not(.mod-right-split) .workspace-tabs:not(.mod-stacked) .workspace-tab-header-inner {
  padding: 0 4px 0 8px;
  transition: 400ms, background-color 150ms ease-in-out, max-width 400ms;
}
.anp-alternate-tab-toggle .mod-root:not(.mod-left-split):not(.mod-right-split) .workspace-tabs:not(.mod-stacked) .workspace-tab-header-inner, .anp-alternate-tab-toggle .mod-root:not(.mod-left-split):not(.mod-right-split) .workspace-tabs:not(.mod-stacked) .workspace-tab-header-inner-close-button, .anp-alternate-tab-toggle .mod-root:not(.mod-left-split):not(.mod-right-split) .workspace-tabs:not(.mod-stacked) .workspace-tab-header-inner-icon {
  color: var(--text-muted);
}

.anp-alternate-tab-toggle .workspace .mod-root .workspace-tab-header-inner::after {
  display: none;
}

.anp-alternate-tab-toggle .mod-root:not(.mod-left-split):not(.mod-right-split) .workspace-tabs:not(.mod-stacked) .workspace-tab-header.is-active {
  flex-grow: 1.67;
  max-width: var(--tab-max-width-active);
  background-color: var(--background-primary);
}

.anp-alternate-tab-toggle .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header.is-active .workspace-tab-header-inner-title {
  color: var(--color-accent);
}

.anp-alternate-tab-toggle .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header.is-active .workspace-tab-header-inner, .anp-alternate-tab-toggle .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header.is-active .workspace-tab-header-inner-title, .anp-alternate-tab-toggle .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header.is-active .workspace-tab-header-inner-close-button, .anp-alternate-tab-toggle .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header.is-active .workspace-tab-header-inner-icon {
  color: var(--text-faint);
}
.anp-alternate-tab-toggle .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header.is-active .workspace-tab-header-inner:hover {
  background-color: rgba(var(--ctp-surface1), 0.4);
}

.anp-alternate-tab-toggle .mod-root .workspace-tabs:not(.mod-stacked).mod-active .workspace-tab-header.is-active .workspace-tab-header-inner, .anp-alternate-tab-toggle .mod-root .workspace-tabs:not(.mod-stacked).mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-title, .anp-alternate-tab-toggle .mod-root .workspace-tabs:not(.mod-stacked).mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-close-button, .anp-alternate-tab-toggle .mod-root .workspace-tabs:not(.mod-stacked).mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-icon {
  color: var(--color-accent) !important;
}

.anp-alternate-tab-toggle .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header.is-active:hover {
  background-color: var(--background-modifier-border);
}

.anp-alternate-tab-toggle .sidebar-toggle-button.mod-left,
.anp-alternate-tab-toggle .sidebar-toggle-button.mod-right {
  align-items: center;
}

.anp-alternate-tab-toggle .sidebar-toggle-button.mod-left .clickable-icon,
.anp-alternate-tab-toggle .sidebar-toggle-button.mod-right .clickable-icon {
  height: var(--tab-height);
}

.is-focused:not(.anp-mini-tab-toggle.anp-colorful-frame) .mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-icon, .is-focused .mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-title {
  color: var(--color-accent);
}

.anp-mini-tab-toggle {
  --header-height: var(--anp-alt-tab-custom-height, 40px);
  --tab-stacked-header-width: var(--anp-stacked-header-width, 40px);
}
.anp-mini-tab-toggle .workspace-split:not(.mod-left-split):not(.mod-right-split) .workspace-tab-header-container-inner .workspace-tab-header {
  background-color: transparent;
  box-shadow: none;
}
.anp-mini-tab-toggle .workspace-split:not(.mod-left-split):not(.mod-right-split) .workspace-tab-header-container-inner .workspace-tab-header::before, .anp-mini-tab-toggle .workspace-split:not(.mod-left-split):not(.mod-right-split) .workspace-tab-header-container-inner .workspace-tab-header::after {
  display: none;
}
.anp-mini-tab-toggle .workspace-split:not(.mod-left-split):not(.mod-right-split) .workspace-tab-header-container-inner .workspace-tab-header.is-active {
  border-bottom: 2px solid var(--color-accent);
  margin-bottom: 0px;
  padding-bottom: 0px;
}
.anp-mini-tab-toggle .workspace-split:not(.mod-left-split):not(.mod-right-split) .workspace-tab-header-container-inner .workspace-tab-header.is-active .workspace-tab-header-inner {
  border-radius: var(--tab-radius) var(--tab-radius) 0px 0px;
  margin-top: 0.5px;
  padding-bottom: 4px;
}
.anp-mini-tab-toggle .workspace-split:not(.mod-left-split):not(.mod-right-split) .workspace-tab-header-container-inner .workspace-tab-header ::after {
  display: none;
}
.anp-mini-tab-toggle .workspace-split:not(.mod-left-split):not(.mod-right-split) .workspace-tab-header-container-inner .workspace-tab-header:hover .workspace-tab-header-inner {
  background-color: var(--background-modifier-hover);
}

.anp-depth-tab-toggle .workspace .mod-root .workspace-tab-header {
  --tab-text-color-focused-active-current: rgb(var(--anp-depth-tab-text, var(--ctp-text)));
  --tab-text-color-focused: rgb(var(--anp-depth-tab-text, var(--ctp-text)));
}
.anp-depth-tab-toggle .workspace .mod-root .workspace-tab-header:not(.is-active) {
  --tab-text-color: rgb(var(--anp-depth-tab-text));
  --icon-color: rgb(var(--anp-depth-tab-text));
  --icon-color-hover: rgb(var(--anp-depth-tab-text));
  background-color: rgba(var(--ctp-mantle), var(--anp-depth-tab-opacity, 0.6));
}
.anp-depth-tab-toggle .workspace .mod-root .workspace-tab-header:not(.is-active)::before, .anp-depth-tab-toggle .workspace .mod-root .workspace-tab-header:not(.is-active)::after {
  box-shadow: inset 0 0 0 var(--tab-outline-width) transparent, 0 0 0 calc(var(--tab-curve) * 4) rgba(var(--ctp-mantle), var(--anp-depth-tab-opacity, 0.6));
}
.anp-depth-tab-toggle .workspace .mod-root .workspace-tab-header:not(.is-active)::after {
  clip-path: inset(50% 50% 0 calc(var(--tab-curve) * 0));
}
.anp-depth-tab-toggle .workspace .mod-root .workspace-tab-header:not(.is-active)::before {
  clip-path: inset(50% calc(var(--tab-curve) * 0) 0 50%);
}
.anp-depth-tab-toggle .workspace .mod-root .workspace-tab-header-inner::after {
  display: none;
}
.anp-depth-tab-toggle .workspace .mod-root .workspace-tab-header-container-inner {
  gap: var(--anp-depth-tab-gap, 10px);
}
.anp-depth-tab-toggle .workspace .mod-root .workspace-tab-header:not(.is-active):hover .workspace-tab-header-inner {
  background-color: transparent;
}
.anp-depth-tab-toggle .workspace .mod-root .workspace-tab-header-inner-close-button:hover {
  background-color: var(--background-modifier-hover);
}

.anp-depth-tab-toggle.anp-depth-tab-text-invert {
  --anp-depth-tab-text: var(--ctp-crust);
}

body.anp-card-layout {
  --card-background-color: rgb(var(--ctp-crust));
  --card-foreground-color: var(--background-primary);
  --divider-color: transparent;
  --tab-outline-color: var(--background-modifier-border);
  --divider-color-hover: var(--backgrouund-modifier-border);
  --divider-width: 4px;
  --divider-width-hover: 4px;
  --tab-container-background: var(--card-background-color);
  --file-header-border: var(--border-width) dashed var(--background-modifier-border);
  --ribbon-padding: 0;
}
body.anp-card-layout.anp-hide-borders {
  --tab-outline-color: transparent;
}
body.anp-card-layout.anp-hide-borders .menu {
  border: 0px !important;
}
body.anp-card-layout .sidebar-toggle-button, body.anp-card-layout .workspace-tabs.mod-top {
  --tab-container-background: var(--card-background-color);
}
body.anp-card-layout .workspace-tabs.mod-top:has(.obsidian-banner-wrapper) {
  --file-header-border: none;
}
body.anp-card-layout.is-focused,
body.anp-card-layout.is-focused .sidebar-toggle-button,
body.anp-card-layout.is-focused .workspace-tabs.mod-top {
  --tab-container-background: var(--card-background-color);
}
body.anp-card-layout .mod-left-split .workspace-tabs .workspace-leaf,
body.anp-card-layout .mod-right-split .workspace-tabs .workspace-leaf,
body.anp-card-layout .mod-left-split,
body.anp-card-layout .mod-vertical .workspace-tab-container,
body.anp-card-layout .mod-vertical,
body.anp-card-layout .workspace-split.mod-vertical,
body.anp-card-layout .workspace-fake-target-overlay:not(.is-in-sidebar) .workspace-tabs .workspace-leaf,
body.anp-card-layout .mod-root .workspace-tabs .workspace-leaf,
body.anp-card-layout .workspace-ribbon.mod-left,
body.anp-card-layout .workspace-ribbon.mod-left:before {
  background-color: var(--tab-container-background);
}
body.anp-card-layout.anp-card-layout-actions .workspace-ribbon.mod-left {
  margin-right: var(--anp-card-layout-padding, 10px);
}
body.anp-card-layout.anp-card-layout-actions .side-dock-actions {
  border-radius: 0px var(--anp-card-radius, var(--radius-xl)) var(--anp-card-radius, var(--radius-xl)) 0px;
  margin-top: 2px;
  border-width: 1px 1px 0px 1px;
}
body.anp-card-layout.anp-card-layout-actions .side-dock-actions, body.anp-card-layout.anp-card-layout-actions .side-dock-settings {
  border-style: solid;
  border-color: var(--tab-outline-color);
  background-color: var(--card-foreground-color);
}
body.anp-card-layout.anp-card-layout-actions .side-dock-settings {
  border-width: 1px 1px 0px 0px;
  border-radius: 0px var(--anp-card-radius, var(--radius-xl)) 0px 0px;
}
body.anp-card-layout.anp-card-layout-actions.anp-fixed-status-bar .side-dock-settings {
  border-width: 1px;
  border-radius: 0px var(--anp-card-radius, var(--radius-xl)) var(--anp-card-radius, var(--radius-xl)) 0px;
}
body.anp-card-layout .side-dock-actions,
body.anp-card-layout .side-dock-settings {
  padding: var(--size-4-2) var(--size-4-1) var(--size-4-3);
}
body.anp-card-layout .workspace-ribbon.mod-left {
  margin-top: calc(var(--header-height) - 1px);
}
body.anp-card-layout .workspace-tab-header-container, body.anp-card-layout .workspace-ribbon.mod-left:before {
  border-bottom: none;
}
body.anp-card-layout .mod-vertical .workspace-tabs {
  padding-left: var(--anp-card-layout-padding, 10px);
  padding-right: var(--anp-card-layout-padding, 10px);
}
body.anp-card-layout .mod-vertical .workspace-tabs .workspace-tab-header-container {
  padding-left: var(--anp-card-header-left-padding, 20px);
}
body.anp-card-layout .mod-vertical .workspace-tabs .workspace-tab-header-container .workspace-tab-header-container-inner {
  margin: 6px -5px calc(var(--tab-outline-width) * -1);
  z-index: 1;
}
body.anp-card-layout .mod-left-split .workspace-tab-container,
body.anp-card-layout .mod-right-split .workspace-tab-container {
  padding-left: var(--anp-card-layout-padding, 10px);
  padding-right: var(--anp-card-layout-padding, 10px);
  background-color: var(--tab-container-background);
}
body.anp-card-layout .workspace-split .workspace-leaf-content:not([data-type=file-explorer]), body.anp-card-layout.anp-card-layout-filebrowser .workspace-leaf-content[data-type=file-explorer] {
  border-radius: var(--anp-card-radius, var(--radius-xl));
  background-color: var(--card-foreground-color);
  border: 1px solid var(--tab-outline-color);
  margin-bottom: var(--anp-card-layout-padding, 10px);
}
body.anp-card-layout.anp-card-shadows .workspace-split .workspace-leaf-content, body.anp-card-layout.anp-card-shadows.anp-card-layout-actions .side-dock-actions, body.anp-card-layout.anp-card-shadows.anp-card-layout-filebrowser .workspace-split .workspace-leaf-content[data-type=file-explorer] {
  box-shadow: 0 3px 4px 0px rgba(0, 0, 0, 0.05);
}
body.anp-card-layout .workspace-split .mod-stacked .workspace-leaf-content {
  border-radius: 0;
  border: none;
  margin-bottom: 0px;
  border-left: none;
}
body.anp-card-layout .workspace-split.mod-horizontal > * {
  width: unset;
}
body.anp-card-layout .workspace .workspace-tabs.mod-stacked .workspace-tab-container .workspace-tab-header {
  border-style: solid;
  border-radius: var(--anp-card-radius, var(--radius-xl)) 0px 0px var(--anp-card-radius, var(--radius-xl));
  border-width: var(--tab-outline-width);
  border-color: var(--tab-outline-color);
  border-right: var(--tab-outline-width) dashed var(--tab-outline-color);
}
body.anp-card-layout .workspace .workspace-tabs.mod-stacked .workspace-leaf {
  border-left-width: 0px;
  border-top-width: var(--tab-outline-width);
  border-bottom-width: var(--tab-outline-width);
  border-style: solid;
  border-color: var(--tab-outline-color);
  border-radius: 0 var(--anp-card-radius, var(--radius-xl)) var(--anp-card-radius, var(--radius-xl)) 0;
}
body.anp-card-layout .workspace .workspace-tabs.mod-stacked .workspace-tab-container {
  padding-bottom: var(--anp-card-layout-padding, 10px);
}
body.anp-card-layout .workspace-drop-overlay:before {
  width: calc(100% - 6px - var(--anp-card-layout-padding, 0) * 2);
  height: calc(100% - 6px - var(--anp-card-layout-padding, 0) * 2);
  margin: auto;
}

/*--------------------------
Inspired by https://github.com/Akifyss/obsidian-border
---------------------------*/
body.anp-border-layout {
  --card-background-color: rgb(var(--ctp-crust));
  --card-foreground-color: var(--background-primary);
  --border-border-style: 1px solid var(--tab-outline-color);
  --anp-border-bottom-padding: var(--anp-border-padding, 20px);
}
body.anp-border-layout .horizontal-main-container {
  background-color: var(--card-background-color);
  --titlebar-background: var(--card-background-color);
  --ribbon-background: var(--card-background-color);
  --ribbon-background-collapsed: var(--card-background-color);
}
body.anp-border-layout.anp-fixed-status-bar {
  --anp-border-bottom-padding: calc(var(--anp-border-padding, 20px) - 18px);
}
body.anp-border-layout .workspace {
  margin-right: var(--anp-border-padding, 20px);
}
body.anp-border-layout .workspace:not(.is-left-sidedock-open) .workspace-split.mod-vertical .workspace-tabs .workspace-tab-container {
  border-left: var(--border-border-style);
}
body.anp-border-layout .workspace:not(.is-left-sidedock-open) .workspace-split.mod-vertical .workspace-tabs.mod-top-left-space .workspace-tab-container {
  border-top-left-radius: var(--anp-border-radius, var(--radius-xl));
}
body.anp-border-layout .workspace:not(.is-left-sidedock-open) .workspace-split.mod-vertical .workspace-tabs:last-child .workspace-tab-container {
  border-bottom-left-radius: var(--anp-border-radius, var(--radius-xl));
}
body.anp-border-layout .workspace:not(.is-left-sidedock-open) .workspace-split.mod-vertical .workspace-tabs:not(.mod-top) .workspace-tab-header-container {
  border-left: var(--border-border-style);
}
body.anp-border-layout .workspace:not(.is-right-sidedock-open) .workspace-split.mod-vertical .workspace-tabs .workspace-tab-container {
  border-right: var(--border-border-style);
}
body.anp-border-layout .workspace:not(.is-right-sidedock-open) .workspace-split.mod-vertical .workspace-tabs.mod-top-right-space .workspace-tab-container {
  border-top-right-radius: var(--anp-border-radius, var(--radius-xl));
}
body.anp-border-layout .workspace:not(.is-right-sidedock-open) .workspace-split.mod-vertical .workspace-tabs:last-child .workspace-tab-container {
  border-bottom-right-radius: var(--anp-border-radius, var(--radius-xl));
}
body.anp-border-layout .workspace:not(.is-right-sidedock-open) .workspace-split.mod-vertical .workspace-tabs:not(.mod-top) .workspace-tab-header-container {
  border-right: var(--border-border-style);
}
body.anp-border-layout .workspace .workspace-split.mod-left-split {
  background-color: var(--card-background-color);
  padding-left: calc(var(--anp-border-padding, 20px) - 20px);
  padding-bottom: var(--anp-border-bottom-padding, 20px);
}
body.anp-border-layout .workspace .workspace-split.mod-left-split .workspace-tabs {
  background-color: var(--card-background-color);
}
body.anp-border-layout .workspace .workspace-split.mod-left-split .workspace-tabs .workspace-tab-container {
  border-left: var(--border-border-style);
}
body.anp-border-layout .workspace .workspace-split.mod-left-split .workspace-tabs .workspace-tab-container .workspace-leaf {
  background-color: var(--card-foreground-color);
}
body.anp-border-layout .workspace .workspace-split.mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header-container {
  border-left: var(--border-border-style);
}
body.anp-border-layout .workspace .workspace-split.mod-left-split .workspace-tabs:last-child .workspace-tab-container {
  border-bottom-left-radius: var(--anp-border-radius, var(--radius-xl));
  border-bottom: var(--border-border-style);
}
body.anp-border-layout .workspace .workspace-split.mod-left-split .workspace-tabs.mod-top-left-space .workspace-tab-container {
  border-top-left-radius: var(--anp-border-radius, var(--radius-xl));
  border-top: var(--border-border-style);
  border-left: var(--border-border-style);
}
body.anp-border-layout .workspace .workspace-split.mod-right-split {
  padding-bottom: var(--anp-border-bottom-padding, 20px);
  background-color: var(--card-background-color);
}
body.anp-border-layout .workspace .workspace-split.mod-right-split .workspace-tabs {
  background-color: var(--card-background-color);
}
body.anp-border-layout .workspace .workspace-split.mod-right-split .workspace-tabs .workspace-tab-container {
  border-right: var(--border-border-style);
}
body.anp-border-layout .workspace .workspace-split.mod-right-split .workspace-tabs .workspace-tab-container .workspace-leaf {
  background-color: var(--card-foreground-color);
}
body.anp-border-layout .workspace .workspace-split.mod-right-split .workspace-tabs:not(.mod-top) .workspace-tab-header-container {
  border-right: var(--border-border-style);
}
body.anp-border-layout .workspace .workspace-split.mod-right-split .workspace-tabs:last-child .workspace-tab-container {
  border-bottom-right-radius: var(--anp-border-radius, var(--radius-xl));
  border-bottom: var(--border-border-style);
}
body.anp-border-layout .workspace .workspace-split.mod-right-split .workspace-tabs.mod-top-right-space .workspace-tab-container {
  border-top-right-radius: var(--anp-border-radius, var(--radius-xl));
  border-top: var(--border-border-style);
  border-right: var(--border-border-style);
}
body.anp-border-layout .workspace .workspace-split:not(.mod-right-split):not(.mod-left-split) {
  background-color: var(--card-background-color);
  padding-bottom: var(--anp-border-bottom-padding, 20px);
}
body.anp-border-layout .workspace .workspace-split:not(.mod-right-split):not(.mod-left-split) > .workspace-split {
  padding-bottom: 0;
}
body.anp-border-layout .workspace .workspace-split:not(.mod-right-split):not(.mod-left-split) .workspace-tabs {
  background-color: var(--card-background-color);
}
body.anp-border-layout .workspace .workspace-split:not(.mod-right-split):not(.mod-left-split) .workspace-tabs:last-child .workspace-tab-container {
  border-bottom: var(--border-border-style);
}
body.anp-border-layout .workspace .workspace-split:not(.mod-right-split):not(.mod-left-split) .workspace-tabs.mod-top .workspace-tab-container {
  border-top: var(--border-border-style);
}
body.anp-border-layout .workspace .workspace-tabs:not(.mod-top) .workspace-tab-header-container {
  background-color: var(--card-foreground-color);
}
body.anp-border-layout .workspace .workspace-tabs.mod-top .workspace-tab-header-container {
  border-bottom: none;
}
body.anp-border-layout .workspace-split.mod-left-split > .workspace-leaf-resize-handle,
body.anp-border-layout .workspace-split.mod-right-split > .workspace-leaf-resize-handle {
  height: calc(100% + -1 * var(--header-height) + -1 * var(--anp-border-bottom-padding, 20px));
  bottom: var(--anp-border-bottom-padding, 20px);
}
body.anp-border-layout .workspace-split {
  --divider-vertical-height: calc(100% + -1*var(--header-height));
}
body.anp-border-layout .workspace-ribbon.mod-left:before {
  border-bottom: 1px solid var(--card-background-color);
}
body.anp-border-layout .workspace-ribbon.mod-left, body.anp-border-layout .workspace-ribbon.mod-left.is-focused {
  border-right-color: var(--card-background-color);
}
body.anp-border-layout.is-popout-window .workspace {
  margin-left: var(--anp-border-padding, 20px);
}

.anp-bg-fix .workspace,
.anp-bg-fix .app-container,
.anp-bg-fix .horizontal-main-container {
  background-color: var(--tab-container-background);
}

.anp-hide-borders {
  --tab-outline-color: transparent;
  --divider-color: transparent;
}

/* AGPLv3 License
Floating Status Bar
Author: AnubisNekhet
Note: If you decide to implement it in your theme or redistribute it, please keep this comment (Especially for *certain* individuals who may try to rebrand it as their own :))
Support me: https://buymeacoffee.com/AnubisNekhet
*/
.anp-floating-status-bar {
  --status-bar-position: absolute;
  --status-bar-radius: var(--radius-m);
  --status-bar-border-width: 1px;
}
.anp-floating-status-bar .status-bar {
  transform: translateX(calc(100% + 5px));
  transition: transform 300ms 150ms;
  bottom: 5px;
  right: 5px;
  box-shadow: 0 3px 4px 0px rgba(0, 0, 0, 0.05);
}
.anp-floating-status-bar .status-bar::before {
  width: 100%;
  min-height: 100%;
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  transform: translateX(-100%);
}
.anp-floating-status-bar .status-bar:hover {
  transform: none;
  transition: transform 300ms 150ms;
}

.anp-fixed-status-bar .status-bar {
  --status-bar-position: relative;
  --status-bar-radius: 0;
}
.anp-fixed-status-bar.anp-card-layout .status-bar, .anp-fixed-status-bar.anp-border-layout .status-bar {
  --status-bar-border-width: 0;
  --status-bar-background: transparent;
  --status-bar-border-color: transparent;
  --status-bar-background: var(--card-background-color);
}

/*----------------CALLOUTS-------------------*/
.callout:not([data-callout-metadata*=revert]) {
  --callout-blend-mode: normal;
}
.callout:not([data-callout-metadata*=revert]) > .callout-title > .callout-title-inner {
  flex-grow: var(--anp-callout-fold-position, 0);
}
.theme-light .callout:not([data-callout-metadata*=revert]) > .callout-title > .callout-title-inner {
  color: var(--text-normal);
}
.callout:not([data-callout-metadata*=revert]):not(.is-collapsible) > .callout-title .callout-title-inner {
  font-weight: 500;
}
.callout:not([data-callout-metadata*=revert]) .callout-title {
  --bold-color: currentColor;
  --italic-color: currentColor;
}

.callout[data-callout-metadata*=revert] {
  --callout-padding: var(--size-4-3) var(--size-4-3) var(--size-4-3) var(--size-4-6);
  --callout-title-padding: 0;
  --callout-content-padding: 0;
}

.callout-content > p:first-child, .callout-content > ul:first-child, .callout-content > ol:first-child {
  margin-top: 0px;
}
.callout-content > p:last-child, .callout-content > ul:last-child, .callout-content > ol:last-child {
  margin-bottom: 0px;
}

.anp-callout-block .callout:not([data-callout-metadata*=anp-sleek],
[data-callout-metadata*=anp-vanilla-normal],
[data-callout-metadata*=anp-vanilla-plus]):not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]),
.callout[data-callout-metadata*=anp-block]:not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]) {
  border-left: var(--size-2-3) solid rgb(var(--callout-color));
}

.anp-callout-sleek .callout:not([data-callout-metadata*=anp-block],
[data-callout-metadata*=anp-vanilla-normal],
[data-callout-metadata*=anp-vanilla-plus]):not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]),
.callout[data-callout-metadata*=anp-sleek]:not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]) {
  --callout-padding: 0;
  --callout-border-width: 1px;
  --callout-border-opacity: 0.4;
  --callout-border-width: 1px;
  background-color: rgba(var(--ctp-mantle), 0.4);
}
.anp-callout-sleek .callout:not([data-callout-metadata*=anp-block],
[data-callout-metadata*=anp-vanilla-normal],
[data-callout-metadata*=anp-vanilla-plus]):not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]) > .callout-title,
.callout[data-callout-metadata*=anp-sleek]:not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]) > .callout-title {
  background-color: rgba(var(--callout-color), var(--callout-title-opacity, 0.1));
}
.anp-callout-sleek .callout:not([data-callout-metadata*=anp-block],
[data-callout-metadata*=anp-vanilla-normal],
[data-callout-metadata*=anp-vanilla-plus]):not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]) > .callout-content,
.callout[data-callout-metadata*=anp-sleek]:not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]) > .callout-content {
  border-top: var(--callout-border-width) dashed rgba(var(--callout-color), var(--callout-border-opacity));
}
.anp-callout-sleek .callout:not([data-callout-metadata*=anp-block],
[data-callout-metadata*=anp-vanilla-normal],
[data-callout-metadata*=anp-vanilla-plus]):not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]) .list-collapse-indicator,
.callout[data-callout-metadata*=anp-sleek]:not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]) .list-collapse-indicator {
  margin-left: -35px;
  padding-right: 3px;
}

.anp-callout-vanilla-normal .callout:not([data-callout-metadata*=anp-sleek],
[data-callout-metadata*=anp-vanilla-plus],
[data-callout-metadata*=anp-block]):not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]),
.callout[data-callout-metadata*=anp-vanilla-normal]:not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]) {
  background-color: transparent;
}
.anp-callout-vanilla-normal .callout:not([data-callout-metadata*=anp-sleek],
[data-callout-metadata*=anp-vanilla-plus],
[data-callout-metadata*=anp-block]):not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]) > .callout-title,
.callout[data-callout-metadata*=anp-vanilla-normal]:not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]) > .callout-title {
  background-color: rgba(var(--callout-color), var(--callout-title-opacity, 0.1));
}
.anp-callout-vanilla-normal .callout:not([data-callout-metadata*=anp-sleek],
[data-callout-metadata*=anp-vanilla-plus],
[data-callout-metadata*=anp-block]):not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]) > .callout-content,
.callout[data-callout-metadata*=anp-vanilla-normal]:not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]) > .callout-content {
  background-color: rgb(var(--ctp-mantle));
}
.anp-callout-vanilla-normal .callout:not([data-callout-metadata*=anp-sleek],
[data-callout-metadata*=anp-vanilla-plus],
[data-callout-metadata*=anp-block]):not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]) > .callout-title, .anp-callout-vanilla-normal .callout:not([data-callout-metadata*=anp-sleek],
[data-callout-metadata*=anp-vanilla-plus],
[data-callout-metadata*=anp-block]):not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]) > .callout-content,
.callout[data-callout-metadata*=anp-vanilla-normal]:not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]) > .callout-title,
.callout[data-callout-metadata*=anp-vanilla-normal]:not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]) > .callout-content {
  border-left: var(--size-2-3) solid rgb(var(--callout-color));
}

.anp-callout-vanilla-plus .callout:not([data-callout-metadata*=anp-sleek],
[data-callout-metadata*=anp-vanilla-normal],
[data-callout-metadata*=anp-block]):not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]),
.callout[data-callout-metadata*=anp-vanilla-plus]:not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]) {
  background-color: transparent;
}
.anp-callout-vanilla-plus .callout:not([data-callout-metadata*=anp-sleek],
[data-callout-metadata*=anp-vanilla-normal],
[data-callout-metadata*=anp-block]):not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]) > .callout-title,
.callout[data-callout-metadata*=anp-vanilla-plus]:not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]) > .callout-title {
  background-color: rgba(var(--callout-color), var(--callout-title-opacity, 0.1));
  border-left: var(--size-2-3) solid rgb(var(--callout-color));
}
.anp-callout-vanilla-plus .callout:not([data-callout-metadata*=anp-sleek],
[data-callout-metadata*=anp-vanilla-normal],
[data-callout-metadata*=anp-block]):not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]) > .callout-content,
.callout[data-callout-metadata*=anp-vanilla-plus]:not([data-callout-metadata*=revert],
[data-callout=blank-container],
[data-callout=multi-column]) > .callout-content {
  background-color: rgb(var(--ctp-mantle));
}

/*---------------CALLOUT COLORS----------------*/
.anp-callout-color-toggle .callout[data-callout=note] {
  --callout-color: var(--ctp-blue);
}
.anp-callout-color-toggle .callout[data-callout=abstract], .anp-callout-color-toggle .callout[data-callout=summary], .anp-callout-color-toggle .callout[data-callout=tldr], .anp-callout-color-toggle .callout[data-callout=seealso] {
  --callout-color: var(--ctp-blue);
}
.anp-callout-color-toggle .callout[data-callout=info], .anp-callout-color-toggle .callout[data-callout=todo], .anp-callout-color-toggle .callout[data-callout=tip], .anp-callout-color-toggle .callout[data-callout=hint], .anp-callout-color-toggle .callout[data-callout=important] {
  --callout-color: var(--ctp-teal);
}
.anp-callout-color-toggle .callout[data-callout=success], .anp-callout-color-toggle .callout[data-callout=check], .anp-callout-color-toggle .callout[data-callout=done], .anp-callout-color-toggle .callout[data-callout=question], .anp-callout-color-toggle .callout[data-callout=help], .anp-callout-color-toggle .callout[data-callout=faq] {
  --callout-color: var(--ctp-green);
}
.anp-callout-color-toggle .callout[data-callout=warning], .anp-callout-color-toggle .callout[data-callout=caution], .anp-callout-color-toggle .callout[data-callout=attention] {
  --callout-color: var(--ctp-yellow);
}
.anp-callout-color-toggle .callout[data-callout=failure], .anp-callout-color-toggle .callout[data-callout=fail], .anp-callout-color-toggle .callout[data-callout=missing] {
  --callout-color: var(--ctp-maroon);
}
.anp-callout-color-toggle .callout[data-callout=danger], .anp-callout-color-toggle .callout[data-callout=error], .anp-callout-color-toggle .callout[data-callout=bug] {
  --callout-color: var(--ctp-red);
}
.anp-callout-color-toggle .callout[data-callout=example] {
  --callout-color: var(--ctp-mauve);
}
.anp-callout-color-toggle .callout[data-callout=quote], .anp-callout-color-toggle .callout[data-callout=cite] {
  --callout-color: var(--ctp-surface2);
}
.anp-callout-color-toggle.anuppuccin-accent-toggle .callout[data-callout=note] {
  --callout-color: var(--ctp-accent);
}

/*---------------CUSTOM CALLOUTS---------------*/
.callout[data-callout=formula] {
  --callout-color: var(--ctp-text);
  width: fit-content;
  height: fit-content;
  mix-blend-mode: normal;
  background-color: transparent !important;
}
.callout[data-callout=formula] .callout-title {
  background-color: rgba(var(--callout-color), 0.05) !important;
  color: var(--ctp-text);
}
.callout[data-callout=formula] .callout-title .callout-icon, .callout[data-callout=formula] .callout-title .callout-fold {
  display: none;
}
.callout[data-callout=formula] .callout-content {
  background-color: transparent !important;
}
.callout[data-callout=formula] .callout-content ul, .callout[data-callout=formula] .callout-content ol {
  padding-inline-start: 12px;
}

/*-Blockquote spacing-*/
.markdown-rendered blockquote {
  padding: 5px 5px 5px 10px;
}

/*-Decorations for bold and italics-*/
.anp-decoration-toggle {
  --italic-color: rgb(var(--anp-italic-color, var(--ctp-green)));
  --bold-color: rgb(var(--anp-bold-color, var(--ctp-red)));
  --text-highlight-bg: rgba(var(--anp-highlight-color, var(--ctp-yellow)), 0.2);
}

.markdown-rendered .internal-link {
  text-decoration-line: none;
}
.markdown-rendered .internal-link:hover, .markdown-rendered .internal-link:active, .markdown-rendered .internal-link.is-unresolved:hover, .markdown-rendered .internal-link.is-unresolved {
  text-decoration-line: none;
}

.cm-formatting-link-string {
  color: var(--text-normal);
}

.cm-hmd-internal-link {
  --link-decoration: none;
  --link-decoration-hover: none;
  --link-decoration-active: none;
}

.external-link,
.external-link:hover {
  background-image: none;
  padding-right: 0px;
}

.cm-s-obsidian span.cm-formatting-strong, .cm-s-obsidian span.cm-strong {
  color: var(--bold-color);
}
.cm-s-obsidian span.cm-formatting-em, .cm-s-obsidian span.cm-em {
  color: var(--italic-color);
}

/*-Footnote-*/
sup[data-footnote-id] {
  font-size: var(--font-smallest);
}

.internal-embed[alt*=seamless].markdown-embed {
  --embed-padding: 0;
  border-width: 0px;
}
.internal-embed[alt*=seamless].markdown-embed > .markdown-embed-title {
  width: fit-content;
  position: absolute;
  left: unset;
  right: 33px;
  top: 5px;
  opacity: 0;
  transition: opacity 0.1s;
}
.internal-embed[alt*=seamless].markdown-embed > .markdown-embed-link {
  opacity: 0;
  transition: opacity 0.1s;
}
.internal-embed[alt*=seamless].markdown-embed:hover > .markdown-embed-title, .internal-embed[alt*=seamless].markdown-embed:hover > .markdown-embed-link {
  opacity: 1;
  transition: opacity 0.1s;
}

/*--------------HEADERS----------------*/
.markdown-source-view.mod-cm6 .cm-fold-indicator .collapse-indicator {
  margin-left: -22px;
  padding: 0px 6px;
}

.anp-header-color-toggle.anp-h1-rosewater .app-container, .anp-header-color-toggle.anp-h1-rosewater .print {
  --h1-color: rgb(var(--ctp-rosewater));
}

.anp-header-color-toggle.anp-h1-flamingo .app-container, .anp-header-color-toggle.anp-h1-flamingo .print {
  --h1-color: rgb(var(--ctp-flamingo));
}

.anp-header-color-toggle.anp-h1-pink .app-container, .anp-header-color-toggle.anp-h1-pink .print {
  --h1-color: rgb(var(--ctp-pink));
}

.anp-header-color-toggle.anp-h1-mauve .app-container, .anp-header-color-toggle.anp-h1-mauve .print {
  --h1-color: rgb(var(--ctp-mauve));
}

.anp-header-color-toggle.anp-h1-red .app-container, .anp-header-color-toggle.anp-h1-red .print {
  --h1-color: rgb(var(--ctp-red));
}

.anp-header-color-toggle.anp-h1-maroon .app-container, .anp-header-color-toggle.anp-h1-maroon .print {
  --h1-color: rgb(var(--ctp-maroon));
}

.anp-header-color-toggle.anp-h1-peach .app-container, .anp-header-color-toggle.anp-h1-peach .print {
  --h1-color: rgb(var(--ctp-peach));
}

.anp-header-color-toggle.anp-h1-yellow .app-container, .anp-header-color-toggle.anp-h1-yellow .print {
  --h1-color: rgb(var(--ctp-yellow));
}

.anp-header-color-toggle.anp-h1-green .app-container, .anp-header-color-toggle.anp-h1-green .print {
  --h1-color: rgb(var(--ctp-green));
}

.anp-header-color-toggle.anp-h1-teal .app-container, .anp-header-color-toggle.anp-h1-teal .print {
  --h1-color: rgb(var(--ctp-teal));
}

.anp-header-color-toggle.anp-h1-sky .app-container, .anp-header-color-toggle.anp-h1-sky .print {
  --h1-color: rgb(var(--ctp-sky));
}

.anp-header-color-toggle.anp-h1-sapphire .app-container, .anp-header-color-toggle.anp-h1-sapphire .print {
  --h1-color: rgb(var(--ctp-sapphire));
}

.anp-header-color-toggle.anp-h1-blue .app-container, .anp-header-color-toggle.anp-h1-blue .print {
  --h1-color: rgb(var(--ctp-blue));
}

.anp-header-color-toggle.anp-h1-lavender .app-container, .anp-header-color-toggle.anp-h1-lavender .print {
  --h1-color: rgb(var(--ctp-lavender));
}

.anp-h1-divider .markdown-rendered h1,
.anp-h1-divider .HyperMD-header-1 {
  border-bottom: var(--hr-thickness) solid var(--background-modifier-border);
  padding-bottom: 2px;
}
.anp-h1-divider.anp-header-divider-color-toggle .markdown-rendered h1,
.anp-h1-divider.anp-header-divider-color-toggle .HyperMD-header-1 {
  border-bottom-color: currentColor;
}

.cm-formatting-header-1 {
  color: var(--h1-color);
}

.anp-header-color-toggle.anp-h2-rosewater .app-container, .anp-header-color-toggle.anp-h2-rosewater .print {
  --h2-color: rgb(var(--ctp-rosewater));
}

.anp-header-color-toggle.anp-h2-flamingo .app-container, .anp-header-color-toggle.anp-h2-flamingo .print {
  --h2-color: rgb(var(--ctp-flamingo));
}

.anp-header-color-toggle.anp-h2-pink .app-container, .anp-header-color-toggle.anp-h2-pink .print {
  --h2-color: rgb(var(--ctp-pink));
}

.anp-header-color-toggle.anp-h2-mauve .app-container, .anp-header-color-toggle.anp-h2-mauve .print {
  --h2-color: rgb(var(--ctp-mauve));
}

.anp-header-color-toggle.anp-h2-red .app-container, .anp-header-color-toggle.anp-h2-red .print {
  --h2-color: rgb(var(--ctp-red));
}

.anp-header-color-toggle.anp-h2-maroon .app-container, .anp-header-color-toggle.anp-h2-maroon .print {
  --h2-color: rgb(var(--ctp-maroon));
}

.anp-header-color-toggle.anp-h2-peach .app-container, .anp-header-color-toggle.anp-h2-peach .print {
  --h2-color: rgb(var(--ctp-peach));
}

.anp-header-color-toggle.anp-h2-yellow .app-container, .anp-header-color-toggle.anp-h2-yellow .print {
  --h2-color: rgb(var(--ctp-yellow));
}

.anp-header-color-toggle.anp-h2-green .app-container, .anp-header-color-toggle.anp-h2-green .print {
  --h2-color: rgb(var(--ctp-green));
}

.anp-header-color-toggle.anp-h2-teal .app-container, .anp-header-color-toggle.anp-h2-teal .print {
  --h2-color: rgb(var(--ctp-teal));
}

.anp-header-color-toggle.anp-h2-sky .app-container, .anp-header-color-toggle.anp-h2-sky .print {
  --h2-color: rgb(var(--ctp-sky));
}

.anp-header-color-toggle.anp-h2-sapphire .app-container, .anp-header-color-toggle.anp-h2-sapphire .print {
  --h2-color: rgb(var(--ctp-sapphire));
}

.anp-header-color-toggle.anp-h2-blue .app-container, .anp-header-color-toggle.anp-h2-blue .print {
  --h2-color: rgb(var(--ctp-blue));
}

.anp-header-color-toggle.anp-h2-lavender .app-container, .anp-header-color-toggle.anp-h2-lavender .print {
  --h2-color: rgb(var(--ctp-lavender));
}

.anp-h2-divider .markdown-rendered h2,
.anp-h2-divider .HyperMD-header-2 {
  border-bottom: var(--hr-thickness) solid var(--background-modifier-border);
  padding-bottom: 2px;
}
.anp-h2-divider.anp-header-divider-color-toggle .markdown-rendered h2,
.anp-h2-divider.anp-header-divider-color-toggle .HyperMD-header-2 {
  border-bottom-color: currentColor;
}

.cm-formatting-header-2 {
  color: var(--h2-color);
}

.anp-header-color-toggle.anp-h3-rosewater .app-container, .anp-header-color-toggle.anp-h3-rosewater .print {
  --h3-color: rgb(var(--ctp-rosewater));
}

.anp-header-color-toggle.anp-h3-flamingo .app-container, .anp-header-color-toggle.anp-h3-flamingo .print {
  --h3-color: rgb(var(--ctp-flamingo));
}

.anp-header-color-toggle.anp-h3-pink .app-container, .anp-header-color-toggle.anp-h3-pink .print {
  --h3-color: rgb(var(--ctp-pink));
}

.anp-header-color-toggle.anp-h3-mauve .app-container, .anp-header-color-toggle.anp-h3-mauve .print {
  --h3-color: rgb(var(--ctp-mauve));
}

.anp-header-color-toggle.anp-h3-red .app-container, .anp-header-color-toggle.anp-h3-red .print {
  --h3-color: rgb(var(--ctp-red));
}

.anp-header-color-toggle.anp-h3-maroon .app-container, .anp-header-color-toggle.anp-h3-maroon .print {
  --h3-color: rgb(var(--ctp-maroon));
}

.anp-header-color-toggle.anp-h3-peach .app-container, .anp-header-color-toggle.anp-h3-peach .print {
  --h3-color: rgb(var(--ctp-peach));
}

.anp-header-color-toggle.anp-h3-yellow .app-container, .anp-header-color-toggle.anp-h3-yellow .print {
  --h3-color: rgb(var(--ctp-yellow));
}

.anp-header-color-toggle.anp-h3-green .app-container, .anp-header-color-toggle.anp-h3-green .print {
  --h3-color: rgb(var(--ctp-green));
}

.anp-header-color-toggle.anp-h3-teal .app-container, .anp-header-color-toggle.anp-h3-teal .print {
  --h3-color: rgb(var(--ctp-teal));
}

.anp-header-color-toggle.anp-h3-sky .app-container, .anp-header-color-toggle.anp-h3-sky .print {
  --h3-color: rgb(var(--ctp-sky));
}

.anp-header-color-toggle.anp-h3-sapphire .app-container, .anp-header-color-toggle.anp-h3-sapphire .print {
  --h3-color: rgb(var(--ctp-sapphire));
}

.anp-header-color-toggle.anp-h3-blue .app-container, .anp-header-color-toggle.anp-h3-blue .print {
  --h3-color: rgb(var(--ctp-blue));
}

.anp-header-color-toggle.anp-h3-lavender .app-container, .anp-header-color-toggle.anp-h3-lavender .print {
  --h3-color: rgb(var(--ctp-lavender));
}

.anp-h3-divider .markdown-rendered h3,
.anp-h3-divider .HyperMD-header-3 {
  border-bottom: var(--hr-thickness) solid var(--background-modifier-border);
  padding-bottom: 2px;
}
.anp-h3-divider.anp-header-divider-color-toggle .markdown-rendered h3,
.anp-h3-divider.anp-header-divider-color-toggle .HyperMD-header-3 {
  border-bottom-color: currentColor;
}

.cm-formatting-header-3 {
  color: var(--h3-color);
}

.anp-header-color-toggle.anp-h4-rosewater .app-container, .anp-header-color-toggle.anp-h4-rosewater .print {
  --h4-color: rgb(var(--ctp-rosewater));
}

.anp-header-color-toggle.anp-h4-flamingo .app-container, .anp-header-color-toggle.anp-h4-flamingo .print {
  --h4-color: rgb(var(--ctp-flamingo));
}

.anp-header-color-toggle.anp-h4-pink .app-container, .anp-header-color-toggle.anp-h4-pink .print {
  --h4-color: rgb(var(--ctp-pink));
}

.anp-header-color-toggle.anp-h4-mauve .app-container, .anp-header-color-toggle.anp-h4-mauve .print {
  --h4-color: rgb(var(--ctp-mauve));
}

.anp-header-color-toggle.anp-h4-red .app-container, .anp-header-color-toggle.anp-h4-red .print {
  --h4-color: rgb(var(--ctp-red));
}

.anp-header-color-toggle.anp-h4-maroon .app-container, .anp-header-color-toggle.anp-h4-maroon .print {
  --h4-color: rgb(var(--ctp-maroon));
}

.anp-header-color-toggle.anp-h4-peach .app-container, .anp-header-color-toggle.anp-h4-peach .print {
  --h4-color: rgb(var(--ctp-peach));
}

.anp-header-color-toggle.anp-h4-yellow .app-container, .anp-header-color-toggle.anp-h4-yellow .print {
  --h4-color: rgb(var(--ctp-yellow));
}

.anp-header-color-toggle.anp-h4-green .app-container, .anp-header-color-toggle.anp-h4-green .print {
  --h4-color: rgb(var(--ctp-green));
}

.anp-header-color-toggle.anp-h4-teal .app-container, .anp-header-color-toggle.anp-h4-teal .print {
  --h4-color: rgb(var(--ctp-teal));
}

.anp-header-color-toggle.anp-h4-sky .app-container, .anp-header-color-toggle.anp-h4-sky .print {
  --h4-color: rgb(var(--ctp-sky));
}

.anp-header-color-toggle.anp-h4-sapphire .app-container, .anp-header-color-toggle.anp-h4-sapphire .print {
  --h4-color: rgb(var(--ctp-sapphire));
}

.anp-header-color-toggle.anp-h4-blue .app-container, .anp-header-color-toggle.anp-h4-blue .print {
  --h4-color: rgb(var(--ctp-blue));
}

.anp-header-color-toggle.anp-h4-lavender .app-container, .anp-header-color-toggle.anp-h4-lavender .print {
  --h4-color: rgb(var(--ctp-lavender));
}

.anp-h4-divider .markdown-rendered h4,
.anp-h4-divider .HyperMD-header-4 {
  border-bottom: var(--hr-thickness) solid var(--background-modifier-border);
  padding-bottom: 2px;
}
.anp-h4-divider.anp-header-divider-color-toggle .markdown-rendered h4,
.anp-h4-divider.anp-header-divider-color-toggle .HyperMD-header-4 {
  border-bottom-color: currentColor;
}

.cm-formatting-header-4 {
  color: var(--h4-color);
}

.anp-header-color-toggle.anp-h5-rosewater .app-container, .anp-header-color-toggle.anp-h5-rosewater .print {
  --h5-color: rgb(var(--ctp-rosewater));
}

.anp-header-color-toggle.anp-h5-flamingo .app-container, .anp-header-color-toggle.anp-h5-flamingo .print {
  --h5-color: rgb(var(--ctp-flamingo));
}

.anp-header-color-toggle.anp-h5-pink .app-container, .anp-header-color-toggle.anp-h5-pink .print {
  --h5-color: rgb(var(--ctp-pink));
}

.anp-header-color-toggle.anp-h5-mauve .app-container, .anp-header-color-toggle.anp-h5-mauve .print {
  --h5-color: rgb(var(--ctp-mauve));
}

.anp-header-color-toggle.anp-h5-red .app-container, .anp-header-color-toggle.anp-h5-red .print {
  --h5-color: rgb(var(--ctp-red));
}

.anp-header-color-toggle.anp-h5-maroon .app-container, .anp-header-color-toggle.anp-h5-maroon .print {
  --h5-color: rgb(var(--ctp-maroon));
}

.anp-header-color-toggle.anp-h5-peach .app-container, .anp-header-color-toggle.anp-h5-peach .print {
  --h5-color: rgb(var(--ctp-peach));
}

.anp-header-color-toggle.anp-h5-yellow .app-container, .anp-header-color-toggle.anp-h5-yellow .print {
  --h5-color: rgb(var(--ctp-yellow));
}

.anp-header-color-toggle.anp-h5-green .app-container, .anp-header-color-toggle.anp-h5-green .print {
  --h5-color: rgb(var(--ctp-green));
}

.anp-header-color-toggle.anp-h5-teal .app-container, .anp-header-color-toggle.anp-h5-teal .print {
  --h5-color: rgb(var(--ctp-teal));
}

.anp-header-color-toggle.anp-h5-sky .app-container, .anp-header-color-toggle.anp-h5-sky .print {
  --h5-color: rgb(var(--ctp-sky));
}

.anp-header-color-toggle.anp-h5-sapphire .app-container, .anp-header-color-toggle.anp-h5-sapphire .print {
  --h5-color: rgb(var(--ctp-sapphire));
}

.anp-header-color-toggle.anp-h5-blue .app-container, .anp-header-color-toggle.anp-h5-blue .print {
  --h5-color: rgb(var(--ctp-blue));
}

.anp-header-color-toggle.anp-h5-lavender .app-container, .anp-header-color-toggle.anp-h5-lavender .print {
  --h5-color: rgb(var(--ctp-lavender));
}

.anp-h5-divider .markdown-rendered h5,
.anp-h5-divider .HyperMD-header-5 {
  border-bottom: var(--hr-thickness) solid var(--background-modifier-border);
  padding-bottom: 2px;
}
.anp-h5-divider.anp-header-divider-color-toggle .markdown-rendered h5,
.anp-h5-divider.anp-header-divider-color-toggle .HyperMD-header-5 {
  border-bottom-color: currentColor;
}

.cm-formatting-header-5 {
  color: var(--h5-color);
}

.anp-header-color-toggle.anp-h6-rosewater .app-container, .anp-header-color-toggle.anp-h6-rosewater .print {
  --h6-color: rgb(var(--ctp-rosewater));
}

.anp-header-color-toggle.anp-h6-flamingo .app-container, .anp-header-color-toggle.anp-h6-flamingo .print {
  --h6-color: rgb(var(--ctp-flamingo));
}

.anp-header-color-toggle.anp-h6-pink .app-container, .anp-header-color-toggle.anp-h6-pink .print {
  --h6-color: rgb(var(--ctp-pink));
}

.anp-header-color-toggle.anp-h6-mauve .app-container, .anp-header-color-toggle.anp-h6-mauve .print {
  --h6-color: rgb(var(--ctp-mauve));
}

.anp-header-color-toggle.anp-h6-red .app-container, .anp-header-color-toggle.anp-h6-red .print {
  --h6-color: rgb(var(--ctp-red));
}

.anp-header-color-toggle.anp-h6-maroon .app-container, .anp-header-color-toggle.anp-h6-maroon .print {
  --h6-color: rgb(var(--ctp-maroon));
}

.anp-header-color-toggle.anp-h6-peach .app-container, .anp-header-color-toggle.anp-h6-peach .print {
  --h6-color: rgb(var(--ctp-peach));
}

.anp-header-color-toggle.anp-h6-yellow .app-container, .anp-header-color-toggle.anp-h6-yellow .print {
  --h6-color: rgb(var(--ctp-yellow));
}

.anp-header-color-toggle.anp-h6-green .app-container, .anp-header-color-toggle.anp-h6-green .print {
  --h6-color: rgb(var(--ctp-green));
}

.anp-header-color-toggle.anp-h6-teal .app-container, .anp-header-color-toggle.anp-h6-teal .print {
  --h6-color: rgb(var(--ctp-teal));
}

.anp-header-color-toggle.anp-h6-sky .app-container, .anp-header-color-toggle.anp-h6-sky .print {
  --h6-color: rgb(var(--ctp-sky));
}

.anp-header-color-toggle.anp-h6-sapphire .app-container, .anp-header-color-toggle.anp-h6-sapphire .print {
  --h6-color: rgb(var(--ctp-sapphire));
}

.anp-header-color-toggle.anp-h6-blue .app-container, .anp-header-color-toggle.anp-h6-blue .print {
  --h6-color: rgb(var(--ctp-blue));
}

.anp-header-color-toggle.anp-h6-lavender .app-container, .anp-header-color-toggle.anp-h6-lavender .print {
  --h6-color: rgb(var(--ctp-lavender));
}

.anp-h6-divider .markdown-rendered h6,
.anp-h6-divider .HyperMD-header-6 {
  border-bottom: var(--hr-thickness) solid var(--background-modifier-border);
  padding-bottom: 2px;
}
.anp-h6-divider.anp-header-divider-color-toggle .markdown-rendered h6,
.anp-h6-divider.anp-header-divider-color-toggle .HyperMD-header-6 {
  border-bottom-color: currentColor;
}

.cm-formatting-header-6 {
  color: var(--h6-color);
}

.anp-header-margin-toggle .cm-header-1, .anp-header-margin-toggle .markdown-preview-view h1 {
  margin-block-start: var(--anp-header-margin-value);
  margin-block-end: var(--anp-header-margin-value);
}

.anp-header-margin-toggle .cm-header-2, .anp-header-margin-toggle .markdown-preview-view h2 {
  margin-block-start: var(--anp-header-margin-value);
  margin-block-end: var(--anp-header-margin-value);
}

.anp-header-margin-toggle .cm-header-3, .anp-header-margin-toggle .markdown-preview-view h3 {
  margin-block-start: var(--anp-header-margin-value);
  margin-block-end: var(--anp-header-margin-value);
}

.anp-header-margin-toggle .cm-header-4, .anp-header-margin-toggle .markdown-preview-view h4 {
  margin-block-start: var(--anp-header-margin-value);
  margin-block-end: var(--anp-header-margin-value);
}

.anp-header-margin-toggle .cm-header-5, .anp-header-margin-toggle .markdown-preview-view h5 {
  margin-block-start: var(--anp-header-margin-value);
  margin-block-end: var(--anp-header-margin-value);
}

.anp-header-margin-toggle .cm-header-6, .anp-header-margin-toggle .markdown-preview-view h6 {
  margin-block-start: var(--anp-header-margin-value);
  margin-block-end: var(--anp-header-margin-value);
}

.cm-header {
  display: inline-block;
}

.anp-editor-font-toggle .markdown-source-view:not(.is-live-preview) {
  --h1-font: var(--anp-editor-font);
  --h2-font: var(--anp-editor-font);
  --h3-font: var(--anp-editor-font);
  --h4-font: var(--anp-editor-font);
  --h5-font: var(--anp-editor-font);
  --h6-font: var(--anp-editor-font);
}

/*-Custom hr margins-*/
hr {
  margin-block-start: 5px;
  margin-block-end: 5px;
}

.anp-list-toggle div.el-ul > ul.has-list-bullet > li > ul.has-list-bullet > li > .list-bullet::after {
  --list-bullet-border: 1px solid var(--list-marker-color);
  background-color: transparent;
  width: calc(var(--list-bullet-size) - 1px);
  height: calc(var(--list-bullet-size) - 1px);
}
.anp-list-toggle div.el-ul > ul.has-list-bullet > li > ul.has-list-bullet > li > ul.has-list-bullet > li > .list-bullet::after {
  --list-bullet-radius: 0;
  background-color: var(--list-marker-color);
}
.anp-list-toggle div.el-ul > ul.has-list-bullet > li > ul.has-list-bullet > li > ul.has-list-bullet > li > ul.has-list-bullet > li > .list-bullet::after {
  width: calc(var(--list-bullet-size) - 1px);
  height: calc(var(--list-bullet-size) - 1px);
  --list-bullet-radius: 0;
  --list-bullet-border: 1px solid var(--list-marker-color);
  background-color: transparent;
}

.anp-toggle-metadata .frontmatter-container, .markdown-rendered.hide-metadata .frontmatter-container {
  display: none;
}

/*-Recolor table borders-*/
.anp-table-toggle .markdown-preview-view:not(.cards):not(.table-disable) table,
.anp-table-toggle .markdown-preview-view:not(.cards):not(.table-disable) table td,
.anp-table-toggle .markdown-preview-view:not(.cards):not(.table-disable) table th,
.anp-table-toggle .markdown-preview-view:not(.cards):not(.table-disable) table thead tr > th:first-child,
.anp-table-toggle .markdown-preview-view:not(.cards):not(.table-disable) table thead tr > th,
.anp-table-toggle .markdown-preview-view:not(.cards):not(.table-disable) table thead tr > th:last-child,
.anp-table-toggle .markdown-preview-view.cards:not(.table-disable) table:not(.dataview),
.anp-table-toggle .markdown-preview-view.cards:not(.table-disable) table:not(.dataview) td,
.anp-table-toggle .markdown-preview-view.cards:not(.table-disable) table:not(.dataview) th,
.anp-table-toggle .markdown-preview-view.cards:not(.table-disable) table:not(.dataview) thead tr > th:first-child,
.anp-table-toggle .markdown-preview-view.cards:not(.table-disable) table:not(.dataview) thead tr > th,
.anp-table-toggle .markdown-preview-view.cards:not(.table-disable) table:not(.dataview) thead tr > th:last-child,
.anp-table-toggle .is-live-preview:not(.cards):not(.table-disable) table,
.anp-table-toggle .is-live-preview:not(.cards):not(.table-disable) table td,
.anp-table-toggle .is-live-preview:not(.cards):not(.table-disable) table th,
.anp-table-toggle .is-live-preview:not(.cards):not(.table-disable) table thead tr > th:first-child,
.anp-table-toggle .is-live-preview:not(.cards):not(.table-disable) table thead tr > th,
.anp-table-toggle .is-live-preview:not(.cards):not(.table-disable) table thead tr > th:last-child,
.anp-table-toggle .is-live-preview.cards:not(.table-disable) table:not(.dataview),
.anp-table-toggle .is-live-preview.cards:not(.table-disable) table:not(.dataview) td,
.anp-table-toggle .is-live-preview.cards:not(.table-disable) table:not(.dataview) th,
.anp-table-toggle .is-live-preview.cards:not(.table-disable) table:not(.dataview) thead tr > th:first-child,
.anp-table-toggle .is-live-preview.cards:not(.table-disable) table:not(.dataview) thead tr > th,
.anp-table-toggle .is-live-preview.cards:not(.table-disable) table:not(.dataview) thead tr > th:last-child {
  border: var(--anp-table-thickness, 1px) solid var(--table-border-color);
}
.anp-table-toggle .markdown-preview-view:not(.cards):not(.table-disable) table th:not([align]),
.anp-table-toggle .markdown-preview-view:not(.cards):not(.table-disable) table thead tr > th:first-child:not([align]),
.anp-table-toggle .markdown-preview-view:not(.cards):not(.table-disable) table thead tr > th:not([align]),
.anp-table-toggle .markdown-preview-view:not(.cards):not(.table-disable) table thead tr > th:last-child:not([align]),
.anp-table-toggle .markdown-preview-view.cards:not(.table-disable) table:not(.dataview) th:not([align]),
.anp-table-toggle .markdown-preview-view.cards:not(.table-disable) table:not(.dataview) thead tr > th:first-child:not([align]),
.anp-table-toggle .markdown-preview-view.cards:not(.table-disable) table:not(.dataview) thead tr > th:not([align]),
.anp-table-toggle .markdown-preview-view.cards:not(.table-disable) table:not(.dataview) thead tr > th:last-child:not([align]),
.anp-table-toggle .is-live-preview:not(.cards):not(.table-disable) table th:not([align]),
.anp-table-toggle .is-live-preview:not(.cards):not(.table-disable) table thead tr > th:first-child:not([align]),
.anp-table-toggle .is-live-preview:not(.cards):not(.table-disable) table thead tr > th:not([align]),
.anp-table-toggle .is-live-preview:not(.cards):not(.table-disable) table thead tr > th:last-child:not([align]),
.anp-table-toggle .is-live-preview.cards:not(.table-disable) table:not(.dataview) th:not([align]),
.anp-table-toggle .is-live-preview.cards:not(.table-disable) table:not(.dataview) thead tr > th:first-child:not([align]),
.anp-table-toggle .is-live-preview.cards:not(.table-disable) table:not(.dataview) thead tr > th:not([align]),
.anp-table-toggle .is-live-preview.cards:not(.table-disable) table:not(.dataview) thead tr > th:last-child:not([align]) {
  text-align: var(--anp-table-align-th, center);
}
.anp-table-toggle .markdown-preview-view:not(.cards):not(.table-disable) table td:not([align]),
.anp-table-toggle .markdown-preview-view.cards:not(.table-disable) table:not(.dataview) td:not([align]),
.anp-table-toggle .is-live-preview:not(.cards):not(.table-disable) table td:not([align]),
.anp-table-toggle .is-live-preview.cards:not(.table-disable) table:not(.dataview) td:not([align]) {
  text-align: var(--anp-table-align-td, center);
}

table.dataview.table-view-table > tbody > tr:hover {
  background-color: rgba(var(--ctp-surface1), 0.1) !important;
}

.anp-table-toggle.anp-table-th-highlight .markdown-preview-view:not(.cards):not(.table-disable) th,
.anp-table-toggle.anp-table-th-highlight .markdown-preview-view.cards:not(.table-disable) table:not(.dataview) th,
.anp-table-toggle.anp-table-th-highlight .is-live-preview:not(.cards):not(.table-disable) th,
.anp-table-toggle.anp-table-th-highlight .is-live-preview.cards:not(.table-disable) table:not(.dataview) th {
  background-color: rgba(var(--ctp-mantle), var(--anp-table-highlight-opacity, 0.5));
}

.th-highlight.markdown-preview-view:not(.cards):not(.table-disable) th, .th-highlight.markdown-preview-view.cards:not(.table-disable) table:not(.dataview) th, .th-highlight.is-live-preview:not(.cards):not(.table-disable) th, .th-highlight.is-live-preview.cards:not(.table-disable) table:not(.dataview) th {
  background-color: rgba(var(--ctp-mantle), var(--anp-table-highlight-opacity, 0.5));
}

.anp-table-toggle.anp-table-row-alt .markdown-preview-view:not(.cards):not(.table-disable) tr:nth-child(2n) td,
.anp-table-toggle.anp-table-row-alt .markdown-preview-view.cards:not(.table-disable) table:not(.dataview) tr:nth-child(2n) td,
.anp-table-toggle.anp-table-row-alt .is-live-preview:not(.cards):not(.table-disable) tr:nth-child(2n) td,
.anp-table-toggle.anp-table-row-alt .is-live-preview.cards:not(.table-disable) table:not(.dataview) tr:nth-child(2n) td {
  background-color: rgba(var(--ctp-mantle), var(--anp-table-highlight-opacity, 0.5));
}

.row-alt.markdown-preview-view:not(.cards):not(.table-disable) tr:nth-child(2n) td, .row-alt.markdown-preview-view.cards:not(.table-disable) table:not(.dataview) tr:nth-child(2n) td, .row-alt.is-live-preview:not(.cards):not(.table-disable) tr:nth-child(2n) td, .row-alt.is-live-preview.cards:not(.table-disable) table:not(.dataview) tr:nth-child(2n) td {
  background-color: rgba(var(--ctp-mantle), var(--anp-table-highlight-opacity, 0.5));
}

.anp-table-toggle.anp-table-col-alt .markdown-preview-view:not(.cards):not(.table-disable) tr td:nth-child(2n),
.anp-table-toggle.anp-table-col-alt .markdown-preview-view.cards:not(.table-disable) table:not(.dataview) tr td:nth-child(2n),
.anp-table-toggle.anp-table-col-alt .is-live-preview:not(.cards):not(.table-disable) tr td:nth-child(2n),
.anp-table-toggle.anp-table-col-alt .is-live-preview.cards:not(.table-disable) table:not(.dataview) tr td:nth-child(2n) {
  background-color: rgba(var(--ctp-mantle), var(--anp-table-highlight-opacity, 0.5));
}

.col-alt.markdown-preview-view:not(.cards):not(.table-disable) tr td:nth-child(2n), .col-alt.markdown-preview-view.cards:not(.table-disable) table:not(.dataview) tr td:nth-child(2n), .col-alt.is-live-preview:not(.cards):not(.table-disable) tr td:nth-child(2n), .col-alt.is-live-preview.cards:not(.table-disable) table:not(.dataview) tr td:nth-child(2n) {
  background-color: rgba(var(--ctp-mantle), var(--anp-table-highlight-opacity, 0.5));
}

.anp-table-toggle.anp-table-full .markdown-preview-view:not(.cards):not(.table-disable) td,
.anp-table-toggle.anp-table-full .markdown-preview-view.cards:not(.table-disable) table:not(.dataview) td,
.anp-table-toggle.anp-table-full .is-live-preview:not(.cards):not(.table-disable) td,
.anp-table-toggle.anp-table-full .is-live-preview.cards:not(.table-disable) table:not(.dataview) td {
  background-color: rgba(var(--ctp-mantle), var(--anp-table-highlight-opacity, 0.5));
}

.table-full.markdown-preview-view:not(.cards):not(.table-disable) td, .table-full.markdown-preview-view.cards:not(.table-disable) table:not(.dataview) td, .table-full.is-live-preview:not(.cards):not(.table-disable) td, .table-full.is-live-preview.cards:not(.table-disable) table:not(.dataview) td {
  background-color: rgba(var(--ctp-mantle), var(--anp-table-highlight-opacity, 0.5));
}

.anp-table-toggle.anp-table-checkered .markdown-preview-view:not(.cards):not(.table-disable) tr td:nth-child(2n),
.anp-table-toggle.anp-table-checkered .markdown-preview-view:not(.cards):not(.table-disable) tr:nth-child(2n) td,
.anp-table-toggle.anp-table-checkered .markdown-preview-view.cards:not(.table-disable) table:not(.dataview) tr td:nth-child(2n),
.anp-table-toggle.anp-table-checkered .markdown-preview-view.cards:not(.table-disable) table:not(.dataview) tr:nth-child(2n) td,
.anp-table-toggle.anp-table-checkered .is-live-preview:not(.cards):not(.table-disable) tr td:nth-child(2n),
.anp-table-toggle.anp-table-checkered .is-live-preview:not(.cards):not(.table-disable) tr:nth-child(2n) td,
.anp-table-toggle.anp-table-checkered .is-live-preview.cards:not(.table-disable) table:not(.dataview) tr td:nth-child(2n),
.anp-table-toggle.anp-table-checkered .is-live-preview.cards:not(.table-disable) table:not(.dataview) tr:nth-child(2n) td {
  background-color: rgba(var(--ctp-mantle), var(--anp-table-highlight-opacity, 0.5));
}
.anp-table-toggle.anp-table-checkered .markdown-preview-view:not(.cards):not(.table-disable) tr:nth-child(2n) td:nth-child(2n),
.anp-table-toggle.anp-table-checkered .markdown-preview-view.cards:not(.table-disable) table:not(.dataview) tr:nth-child(2n) td:nth-child(2n),
.anp-table-toggle.anp-table-checkered .is-live-preview:not(.cards):not(.table-disable) tr:nth-child(2n) td:nth-child(2n),
.anp-table-toggle.anp-table-checkered .is-live-preview.cards:not(.table-disable) table:not(.dataview) tr:nth-child(2n) td:nth-child(2n) {
  background-color: rgba(var(--ctp-crust), var(--anp-table-highlight-opacity, 0.5));
}

.checkered.markdown-preview-view:not(.cards):not(.table-disable) tr td:nth-child(2n),
.checkered.markdown-preview-view:not(.cards):not(.table-disable) tr:nth-child(2n) td, .checkered.markdown-preview-view.cards:not(.table-disable) table:not(.dataview) tr td:nth-child(2n),
.checkered.markdown-preview-view.cards:not(.table-disable) table:not(.dataview) tr:nth-child(2n) td, .checkered.is-live-preview:not(.cards):not(.table-disable) tr td:nth-child(2n),
.checkered.is-live-preview:not(.cards):not(.table-disable) tr:nth-child(2n) td, .checkered.is-live-preview.cards:not(.table-disable) table:not(.dataview) tr td:nth-child(2n),
.checkered.is-live-preview.cards:not(.table-disable) table:not(.dataview) tr:nth-child(2n) td {
  background-color: rgba(var(--ctp-mantle), var(--anp-table-highlight-opacity, 0.5));
}
.checkered.markdown-preview-view:not(.cards):not(.table-disable) tr:nth-child(2n) td:nth-child(2n), .checkered.markdown-preview-view.cards:not(.table-disable) table:not(.dataview) tr:nth-child(2n) td:nth-child(2n), .checkered.is-live-preview:not(.cards):not(.table-disable) tr:nth-child(2n) td:nth-child(2n), .checkered.is-live-preview.cards:not(.table-disable) table:not(.dataview) tr:nth-child(2n) td:nth-child(2n) {
  background-color: rgba(var(--ctp-crust), var(--anp-table-highlight-opacity, 0.5));
}

.anp-table-auto.markdown-rendered:not(.cards):not(.table-disable) table,
.anp-table-auto.markdown-rendered.cards:not(.table-disable) table:not(.dataview) {
  margin: 0 auto;
}

.anp-table-width.markdown-rendered:not(.cards):not(.table-disable) table,
.anp-table-width.markdown-rendered.cards:not(.table-disable) table:not(.dataview) {
  width: var(--anp-table-width-pct);
}

.anp-table-auto.anp-table-toggle .markdown-rendered:not(.cards):not(.table-disable) table,
.anp-table-auto.anp-table-toggle .markdown-rendered.cards:not(.table-disable) table:not(.dataview) {
  margin: 0 auto;
}

.anp-table-width.anp-table-toggle .markdown-rendered:not(.cards):not(.table-disable) table,
.anp-table-width.anp-table-toggle .markdown-rendered.cards:not(.table-disable) table:not(.dataview) {
  width: var(--anp-table-width-pct);
  margin-left: calc(50% - var(--anp-table-width-pct) / 2);
  margin-right: calc(50% - var(--anp-table-width-pct) / 2);
}

/*-Custom folder title-*/
.anp-custom-vault-toggle .nav-folder.mod-root > .nav-folder-title > .nav-folder-title-content {
  font-size: 130%;
  font-weight: bold;
  text-align: center;
  margin-left: 30px;
}

.anp-custom-vault-toggle .nav-folder.mod-root > .nav-folder-title > .nav-folder-title-content:before {
  content: " ";
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' data-darkreader-inline-stroke='' style='--darkreader-inline-stroke:currentColor;'%3E%3Cpath d='m6 14 1.45-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.55 6a2 2 0 0 1-1.94 1.5H4a2 2 0 0 1-2-2V5c0-1.1.9-2 2-2h3.93a2 2 0 0 1 1.66.9l.82 1.2a2 2 0 0 0 1.66.9H18a2 2 0 0 1 2 2v2'%3E%3C/path%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' data-darkreader-inline-stroke='' style='--darkreader-inline-stroke:currentColor;'%3E%3Cpath d='m6 14 1.45-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.55 6a2 2 0 0 1-1.94 1.5H4a2 2 0 0 1-2-2V5c0-1.1.9-2 2-2h3.93a2 2 0 0 1 1.66.9l.82 1.2a2 2 0 0 0 1.66.9H18a2 2 0 0 1 2 2v2'%3E%3C/path%3E%3C/svg%3E");
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  mask-size: contain;
  background-size: contain;
  text-align: center;
  width: 26px;
  height: 26px;
  margin-left: -30px;
  margin-top: -4px;
  color: rgb(var(--ctp-yellow));
  position: absolute;
  background-color: rgb(var(--ctp-yellow));
}

/*-----------------------------------
Rainbow tags is a snippet by @raisabelatrix
Link: https://gist.github.com/raisabelatrix/eb383f7e19b59f951430c2f3c6ed80b0
-----------------------------------*/
body.rainbow-tags {
  --rainbow-tags-opacity: 0.3;
  --r1-color: rgb(var(--ctp-red));
  --r2-color: rgb(var(--ctp-peach));
  --r3-color: rgb(var(--ctp-yellow));
  --r4-color: rgb(var(--ctp-green));
  --r5-color: rgb(var(--ctp-blue));
  --r6-color: rgb(var(--ctp-lavender));
  --r7-color: rgb(var(--ctp-mauve));
  --r1-color-light: rgba(var(--ctp-red), var(--rainbow-tags-opacity));
  --r2-color-light: rgba(var(--ctp-peach), var(--rainbow-tags-opacity));
  --r3-color-light: rgba(var(--ctp-yellow), var(--rainbow-tags-opacity));
  --r4-color-light: rgba(var(--ctp-green), var(--rainbow-tags-opacity));
  --r5-color-light: rgba(var(--ctp-blue), var(--rainbow-tags-opacity));
  --r6-color-light: rgba(var(--ctp-lavender), var(--rainbow-tags-opacity));
  --r7-color-light: rgba(var(--ctp-mauve), var(--rainbow-tags-opacity));
}

.anp-collapse-folders .nav-folder .nav-folder-collapse-indicator,
.anp-collapse-folders [data-type=bookmarks] .tree-item .collapse-icon {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 27 24' fill='none' stroke='currentColor' stroke-linejoin='round' stroke-linecap='round' stroke-width='2'%3E%3Cpath d='M6 14l1.45-2.9A2 2 0 0 1 9.24 10H22a2 2 0 0 1 1.94 2.5l-1.55 6a2 2 0 0 1-1.94 1.5H4a2 2 0 0 1-2-2V5c0-1.1.9-2 2-2h3.93a2 2 0 0 1 1.66.9l.82 1.2a2 2 0 0 0 1.66.9H20a2 2 0 0 1 2 2v2'/%3E%3C/svg%3E%0A");
  -webkit-mask-repeat: no-repeat;
  background-color: currentColor;
  height: 16px;
  width: 17px;
  margin-right: 4px;
}
.anp-collapse-folders .nav-folder.is-collapsed .nav-folder-collapse-indicator,
.anp-collapse-folders [data-type=bookmarks] .tree-item.is-collapsed .collapse-icon {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 27 24' fill='none' stroke='currentColor' stroke-linejoin='round' stroke-linecap='round' stroke-width='2'%3E%3Cpath d='M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2z'/%3E%3Cpath d='M2 10h20' /%3E%3C/svg%3E%0A");
}
.anp-collapse-folders .nav-folder-collapse-indicator svg.svg-icon,
.anp-collapse-folders [data-type=bookmarks] .collapse-icon svg.svg-icon {
  color: transparent !important;
}

.anp-color-transition-toggle .horizontal-tab-content,
.anp-color-transition-toggle .vertical-tab-content,
.anp-color-transition-toggle .vertical-tab-header,
.anp-color-transition-toggle .workspace-tab-header-container,
.anp-color-transition-toggle .workspace-fake-target-overlay:not(.is-in-sidebar) .workspace-tabs .workspace-leaf,
.anp-color-transition-toggle .mod-root .workspace-tabs .workspace-leaf,
.anp-color-transition-toggle .setting-item-name {
  transition: background-color 0.5s ease-in-out, color 0.5s ease-in-out, border-color 0.5s ease-in-out !important;
}

.callout[data-callout=capacities-card] {
  --h1-font: var(--font-text);
  --h2-font: var(--font-text);
  --h3-font: var(--font-text);
  --h4-font: var(--font-text);
  --h5-font: var(--font-text);
  --h6-font: var(--font-text);
  --h1-color: var(--text-normal);
  --h2-color: var(--text-normal);
  --h3-color: var(--text-normal);
  --h4-color: var(--text-normal);
  --h5-color: var(--text-normal);
  --h6-color: var(--text-normal);
  --italic-color: var(--text-normal);
  --bold-color: var(--text-normal);
}

.callout[data-callout=capacities-index] {
  --highlight-mix-blend-mode: none;
  margin-left: 10px;
}

.anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-card], .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-card] {
  background-color: var(--background-primary);
  border-radius: 6px;
  mix-blend-mode: normal;
  overflow-y: hidden;
}
.anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-card] > .callout-content, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-card] > .callout-content {
  padding: 0;
  background-color: var(--background-primary);
  overflow-y: hidden;
}
.anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-card-prop], .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-card-prop] {
  border-radius: 0;
  top: -1px;
  padding-top: 1px;
  right: 11px;
  padding-right: 1px;
  height: calc(100% + 6px);
}
.anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-card-prop] > .callout-content, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-card-prop] > .callout-content {
  padding: 0;
}
.anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-prop], .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-prop] {
  border: none;
  padding: 0;
}
.anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-prop] > .callout-content, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-prop] > .callout-content {
  margin: 0 10px 7px 10px;
  background-color: var(--background-primary);
  border-color: var(--background-modifier-border);
  padding: 3px 0;
  border-top-style: solid;
  border-radius: 6px;
}
.anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-prop] > .callout-title, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-prop] > .callout-title {
  text-transform: uppercase;
  background-color: transparent;
}
.anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-prop] > .callout-title > .callout-title-inner, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-prop] > .callout-title > .callout-title-inner {
  color: rgba(var(--ctp-text), 0.7);
  font-size: 80%;
}
.anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-index], .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-index] {
  border-radius: 0;
  margin: -1px;
}
.anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-index] > .callout-content, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-index] > .callout-content {
  padding: 0;
}
.anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-index] p:first-child, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-index] p:first-child {
  margin-bottom: 0;
}
.anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-sidebar], .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-sidebar] {
  background-color: var(--background-secondary);
}
.anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-sidebar] > .callout-content, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-sidebar] > .callout-content {
  padding-right: 0;
  padding-left: 0;
  background-color: var(--background-secondary);
}
.anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-prop-image] > .callout-content, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-prop-image] > .callout-content {
  padding: 0;
}
.anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-body], .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-body] {
  border-width: 0 1px 0 0;
  background-color: var(--background-primary);
  mix-blend-mode: normal;
}
.anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-body] > .callout-content, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-body] > .callout-content {
  padding: var(--callout-padding);
  background-color: var(--background-primary);
}
.anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-index], .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-card-prop], .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-prop-image], .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-sidebar], .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-body], .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-card], .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-index], .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-card-prop], .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-prop-image], .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-sidebar], .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-body], .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-card] {
  border-color: var(--background-modifier-border);
}
.anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-index] > .callout-content, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-card-prop] > .callout-content, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-prop-image] > .callout-content, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-sidebar] > .callout-content, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-body] > .callout-content, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-card] > .callout-content, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-index] > .callout-content, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-card-prop] > .callout-content, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-prop-image] > .callout-content, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-sidebar] > .callout-content, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-body] > .callout-content, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-card] > .callout-content {
  border-top: none;
}

.anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-vanilla div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-index] .callout-content, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-vanilla div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-card-prop] .callout-content, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-vanilla div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-sidebar] .callout-content, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-vanilla.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-index] .callout-content, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-vanilla.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-card-prop] .callout-content, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-vanilla.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-sidebar] .callout-content {
  border-left: none;
  padding: 0;
}
.anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-vanilla div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-prop] .callout-title, .anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-vanilla.anp-callout-normal-toggle div.callout:not([data-callout-metadata*=revert])[data-callout=capacities-prop] .callout-title {
  border-left: none;
  background-color: transparent;
}

.anp-card-layout.anp-callout-toggle.anp-callout-toggle.anp-callout-toggle.anp-callout-sleek {
  --capacities-sidebar-bottom: calc(var(--anp-card-layout-padding, 10px) + 80px);
}

.markdown-reading-view .markdown-preview-view.capacities-card {
  overflow-y: hidden;
}
.markdown-reading-view .markdown-preview-view.capacities-card::after {
  display: none;
}

/*-Button for metadata-*/
body.anuppuccin-accent-toggle.anp-button-metadata-toggle .frontmatter-container .frontmatter-container-header, body.anuppuccin-accent-toggle .markdown-rendered.metadata-button .frontmatter-container .frontmatter-container-header {
  background-color: rgba(var(--ctp-accent), 0.8);
}
body.anuppuccin-accent-toggle.anp-button-metadata-toggle .frontmatter-container .frontmatter-container-header:hover, body.anuppuccin-accent-toggle .markdown-rendered.metadata-button .frontmatter-container .frontmatter-container-header:hover {
  background-color: rgba(var(--ctp-accent), 0.7);
}
body.anuppuccin-accent-toggle.anp-button-metadata-toggle .frontmatter-container .frontmatter-container-header:active, body.anuppuccin-accent-toggle .markdown-rendered.metadata-button .frontmatter-container .frontmatter-container-header:active {
  background-color: rgba(var(--ctp-accent), 0.6);
}

body:not(.anuppuccin-accent-toggle).anp-button-metadata-toggle .frontmatter-container .frontmatter-container-header, body:not(.anuppuccin-accent-toggle) .markdown-rendered.metadata-button .frontmatter-container .frontmatter-container-header {
  background-color: hsla(var(--color-accent-hsl), 0.8);
}
body:not(.anuppuccin-accent-toggle).anp-button-metadata-toggle .frontmatter-container .frontmatter-container-header:hover, body:not(.anuppuccin-accent-toggle) .markdown-rendered.metadata-button .frontmatter-container .frontmatter-container-header:hover {
  background-color: hsla(var(--color-accent-hsl), 0.7);
}
body:not(.anuppuccin-accent-toggle).anp-button-metadata-toggle .frontmatter-container .frontmatter-container-header:active, body:not(.anuppuccin-accent-toggle) .markdown-rendered.metadata-button .frontmatter-container .frontmatter-container-header:active {
  background-color: hsla(var(--color-accent-hsl), 0.6);
}

.anp-button-metadata-toggle .frontmatter-container, .markdown-rendered.metadata-button .frontmatter-container {
  padding: 0;
}
.anp-button-metadata-toggle .frontmatter-container .frontmatter-container-header, .markdown-rendered.metadata-button .frontmatter-container .frontmatter-container-header {
  width: 32px !important;
  z-index: 1;
  height: 32px !important;
  text-overflow: clip;
  overflow: hidden;
  color: transparent !important;
  position: absolute;
  left: calc(100% - 32px);
  border-radius: 6px;
}
.anp-button-metadata-toggle .frontmatter-container .frontmatter-container-header:after, .markdown-rendered.metadata-button .frontmatter-container .frontmatter-container-header:after {
  position: absolute;
  content: " ";
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' data-darkreader-inline-stroke='' style='--darkreader-inline-stroke:currentColor;'%3E%3Cpolygon points='12 2 2 7 12 12 22 7 12 2'%3E%3C/polygon%3E%3Cpolyline points='2 17 12 22 22 17'%3E%3C/polyline%3E%3Cpolyline points='2 12 12 17 22 12'%3E%3C/polyline%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' data-darkreader-inline-stroke='' style='--darkreader-inline-stroke:currentColor;'%3E%3Cpolygon points='12 2 2 7 12 12 22 7 12 2'%3E%3C/polygon%3E%3Cpolyline points='2 17 12 22 22 17'%3E%3C/polyline%3E%3Cpolyline points='2 12 12 17 22 12'%3E%3C/polyline%3E%3C/svg%3E");
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  background-size: contain;
  background-color: var(--background-primary);
  color: var(--background-secondary);
  width: 22px;
  height: 22px;
  margin-left: 5px;
  margin-top: 5px;
}

/*-------Tweak for Minimal Cards--------------
Minimal cards are a snippet designed by Kepano for the Minimal theme.
None of the original code was used in this theme, and the following code is merely some tweaks to the snippet.
--------------------------------------------*/
.cards .table-view-table > tbody > tr > td:last-child {
  border-bottom-color: transparent !important; /*-No border for last cell-*/
}
.cards .table-view-table > tbody > tr > td:first-child {
  width: 100% !important; /*-Max width for first cell-*/
  border-bottom: 1px dashed var(--background-modifier-border) !important; /*-Border for first table that spans to the end because of max width-*/
}
.cards .table-view-table > tbody > tr > td:first-child span {
  margin: 2px calc(var(--cards-padding) * 0.5) 0 calc(var(--cards-padding) * 0.5); /*-Add padding to text-*/
}
.cards .table-view-table > tbody > tr > td:first-child span a {
  padding: 0 !important; /*-0 padding on link-*/
}
.cards .table-view-table > tbody > tr > td:not(:last-child):not(:first-child) > .el-p.el-p.el-p:not(.el-embed-image) {
  border-bottom: 1px dashed var(--background-modifier-border);
}
.cards:not([class*=cards-cols-]) {
  --cards-columns: repeat(auto-fit, minmax(var(--cards-min-width), var(--cards-max-width))); /*-force card width to be obeyed on notes not having cards-cols css class-*/
}

.cards.cards-bookmark .table-view-table .table-view-tbody tr::after {
  --text-normal: var(--text-muted);
  content: " ";
  width: 25px;
  height: 25px;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 384 512'%3E%3C!--!  --%3E%3Cpath d='M0 48V487.7C0 501.1 10.9 512 24.3 512c5 0 9.9-1.5 14-4.4L192 400 345.7 507.6c4.1 2.9 9 4.4 14 4.4c13.4 0 24.3-10.9 24.3-24.3V48c0-26.5-21.5-48-48-48H48C21.5 0 0 21.5 0 48z'/%3E%3C/svg%3E"); /*-SVG for bookmark-*/
  -webkit-mask-repeat: no-repeat;
  background-color: var(--color-accent); /*-Bookmark follows accent color-*/
  position: absolute;
  margin-top: calc(-2px + -1 * var(--cards-border-width)); /*-Bookmark is a bit higher than the card-*/
  margin-left: 5px; /*-Bookmark is 5px from the left*/
}
.cards.cards-bookmark .table-view-table tbody tr > td:first-child span {
  margin: 2px calc(var(--cards-padding) * 0.5) 0 calc(var(--cards-padding) * 0.5 + 20px); /*-Shift first cell title to the left by about 20px-*/
}

/*---------------COLORFUL FRAME--------------*/
/*----------------------------------
This feature is inspired by Kepano's Minimal theme: https://github.com/kepano/obsidian-minimal
None of the original code was used, and was written from scratch.
----------------------------------*/
body {
  --colorful-icon-color: var(--background-secondary);
  --colorful-icon-color-alt: var(--background-secondary-alt);
  --colorful-frame-color: rgba(var(--anp-colorful-frame-color, var(--ctp-accent)), var(--anp-colorful-frame-opacity, 1));
}

body.anp-colorful-frame-icon-toggle-light.theme-light,
body.anp-colorful-frame-icon-toggle-dark.theme-dark {
  --colorful-icon-color: var(--text-normal);
  --colorful-icon-color-alt: var(--text-muted);
}

.anp-colorful-frame.anp-fixed-status-bar.anp-card-layout .status-bar, .anp-colorful-frame.anp-fixed-status-bar.anp-border-layout .status-bar {
  --status-bar-background: var(--colorful-frame-color);
  --status-bar-text-color: var(--colorful-icon-color);
}
.anp-colorful-frame.anp-fixed-status-bar.anp-card-layout .status-bar .status-bar-item.mod-clickable:hover, .anp-colorful-frame.anp-fixed-status-bar.anp-border-layout .status-bar .status-bar-item.mod-clickable:hover {
  color: var(--colorful-icon-color);
  background-color: rgba(var(--ctp-crust), 0.2);
}
.anp-colorful-frame.anp-card-layout .workspace-split.mod-right-split .workspace-tabs .workspace-tab-header:not(.is-active):hover .workspace-tab-header-inner, .anp-colorful-frame.anp-card-layout .workspace-split.mod-left-split .workspace-tabs .workspace-tab-header:not(.is-active):hover .workspace-tab-header-inner, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .workspace-split.mod-left-split .workspace-tabs.mod-top .workspace-tab-header:not(.is-active):hover .workspace-tab-header-inner, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .workspace-split.mod-right-split .workspace-tabs.mod-top .workspace-tab-header:not(.is-active):hover .workspace-tab-header-inner {
  background-color: rgba(var(--ctp-crust), 0.2);
}
.anp-colorful-frame.is-frameless.is-frameless:not(.is-hidden-frameless) .sidebar-toggle-button.mod-left {
  border-radius: 0 var(--radius-m) var(--radius-m) 0;
}
.anp-colorful-frame.anuppuccin-accent-toggle.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .mod-top .workspace-tab-header-container, .anp-colorful-frame.anuppuccin-accent-toggle.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .workspace-ribbon.mod-left:before, .anp-colorful-frame.anuppuccin-accent-toggle.anp-colorful-frame.anp-card-layout, .anp-colorful-frame.anuppuccin-accent-toggle.anp-colorful-frame.anp-card-layout .workspace-tabs.mod-top, .anp-colorful-frame.anuppuccin-accent-toggle.anp-colorful-frame.anp-card-layout .sidebar-toggle-button, .anp-colorful-frame.anuppuccin-accent-toggle.anp-colorful-frame.anp-card-layout .workspace-ribbon.mod-left:before {
  --tab-container-background: rgba(var(--anp-colorful-frame-color, var(--ctp-accent)), var(--anp-colorful-frame-opacity, 1));
  --titlebar-background: rgba(var(--anp-colorful-frame-color, var(--ctp-accent)), var(--anp-colorful-frame-opacity, 1));
}
.anp-colorful-frame.anuppuccin-accent-toggle.anp-colorful-frame.anp-card-layout .prompt {
  --background-modifier-hover: rgba(var(--ctp-text), 0.075) ;
}
.anp-colorful-frame.anuppuccin-accent-toggle.anp-colorful-frame .sidebar-toggle-button, .anp-colorful-frame.anp-colorful-frame.anp-colorful-frame.anp-colorful-frame .sidebar-toggle-button {
  --background-modifier-hover: rgba(var(--ctp-text), 0.075);
}
.anp-colorful-frame.is-focused .workspace-tabs.mod-stacked .workspace-tab-container .workspace-tab-header-inner .workspace-tab-header-inner-title, .anp-colorful-frame.is-focused .workspace-tabs.mod-stacked .workspace-tab-container .workspace-tab-header-inner .workspace-tab-header-inner-close-button {
  color: var(--text-muted);
}
.anp-colorful-frame .workspace-tabs.mod-stacked .workspace-tab-container .workspace-tab-header-inner .workspace-tab-header-inner-close-button {
  color: var(--text-muted);
}
.anp-colorful-frame.is-focused .workspace-tabs.mod-stacked .workspace-tab-container .workspace-tab-header.is-active .workspace-tab-header-inner .workspace-tab-header-inner-title, .anp-colorful-frame.is-focused .workspace-tabs.mod-stacked .workspace-tab-container .workspace-tab-header.is-active .workspace-tab-header-inner .workspace-tab-header-inner-close-button {
  color: var(--color-accent);
}
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .mod-top .workspace-tab-header-container, .anp-colorful-frame.anp-colorful-frame.anp-card-layout, .anp-colorful-frame.anp-colorful-frame.anp-card-layout .workspace-tabs.mod-top, .anp-colorful-frame.anp-colorful-frame.anp-card-layout .sidebar-toggle-button, .anp-colorful-frame.anp-colorful-frame.anp-card-layout .workspace-ribbon.mod-left:before, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .workspace-ribbon.mod-left:before {
  --tab-container-background: hsla(var(--anp-colorful-frame-color-hsl, var(--color-accent-hsl)), var(--anp-colorful-frame-opacity, 1));
  --titlebar-background: hsla(var(--anp-colorful-frame-color-hsl, var(--color-accent-hsl)), var(--anp-colorful-frame-opacity, 1));
}
.anp-colorful-frame.anp-card-layout {
  --divider-color-hover: var(--background-secondary-alt);
}
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .mod-top:not(.mod-stacked) .workspace-tab-header,
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .mod-top:not(.mod-stacked) .workspace-tab-header.is-active,
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .mod-top:not(.mod-stacked) .is-focused.workspace-tab-header,
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .mod-top:not(.mod-stacked) .workspace-tab-header-container, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused .mod-top:not(.mod-stacked) .workspace-tab-header,
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused .mod-top:not(.mod-stacked) .workspace-tab-header.is-active,
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused .mod-top:not(.mod-stacked) .is-focused.workspace-tab-header,
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused .mod-top:not(.mod-stacked) .workspace-tab-header-container {
  --tab-text-color: var(--colorful-icon-color-alt);
  --icon-color: var(--colorful-icon-color-alt);
  --icon-color-hover: var(--colorful-icon-color);
  --tab-text-color-focused: var(--colorful-icon-color-alt);
}
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .mod-top.mod-stacked .workspace-tab-header-new-tab .clickable-icon, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused .mod-top.mod-stacked .workspace-tab-header-new-tab .clickable-icon {
  --icon-color: var(--colorful-icon-color-alt);
  --icon-color-hover: var(--colorful-icon-color);
}
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .mod-top .workspace-tab-header-tab-list .clickable-icon, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .mod-top .workspace-tab-header-tab-list .clickable-icon:hover, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .mod-top .workspace-tab-header-new-tab .clickable-icon, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .mod-top .workspace-tab-header-new-tab .clickable-icon:hover, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused .mod-top .workspace-tab-header-tab-list .clickable-icon, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused .mod-top .workspace-tab-header-tab-list .clickable-icon:hover, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused .mod-top .workspace-tab-header-new-tab .clickable-icon, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused .mod-top .workspace-tab-header-new-tab .clickable-icon:hover {
  color: var(--colorful-icon-color-alt);
}
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).anp-mini-tab-toggle .mod-top, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused.anp-mini-tab-toggle .mod-top, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused.anp-mini-tab-toggle .mod-top, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused.is-focused.anp-mini-tab-toggle .mod-top {
  --tab-text-color-focused-active-current: var(--colorful-icon-color-alt);
}
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).anp-mini-tab-toggle .mod-top .workspace-tab-header, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).anp-mini-tab-toggle .mod-top .workspace-tab-header.is-active, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused.anp-mini-tab-toggle .mod-top .workspace-tab-header, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused.anp-mini-tab-toggle .mod-top .workspace-tab-header.is-active, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused.anp-mini-tab-toggle .mod-top .workspace-tab-header, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused.anp-mini-tab-toggle .mod-top .workspace-tab-header.is-active, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused.is-focused.anp-mini-tab-toggle .mod-top .workspace-tab-header, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused.is-focused.anp-mini-tab-toggle .mod-top .workspace-tab-header.is-active {
  --tab-text-color-focused-active: var(--colorful-icon-color-alt);
  --tab-text-color-active: var(--colorful-icon-color-alt);
  --tab-text-color-focused: var(--colorful-icon-color-alt);
  --tab-text-color-focused-active-current: var(--colorful-icon-color-alt);
}
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).anp-mini-tab-toggle .mod-top .workspace-tab-header .workspace-tab-header-inner-close-button, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).anp-mini-tab-toggle .mod-top .workspace-tab-header.is-active .workspace-tab-header-inner-close-button, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused.anp-mini-tab-toggle .mod-top .workspace-tab-header .workspace-tab-header-inner-close-button, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused.anp-mini-tab-toggle .mod-top .workspace-tab-header.is-active .workspace-tab-header-inner-close-button, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused.anp-mini-tab-toggle .mod-top .workspace-tab-header .workspace-tab-header-inner-close-button, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused.anp-mini-tab-toggle .mod-top .workspace-tab-header.is-active .workspace-tab-header-inner-close-button, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused.is-focused.anp-mini-tab-toggle .mod-top .workspace-tab-header .workspace-tab-header-inner-close-button, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused.is-focused.anp-mini-tab-toggle .mod-top .workspace-tab-header.is-active .workspace-tab-header-inner-close-button {
  --tab-text-color-focused: var(--colorful-icon-color-alt);
  --tab-text-color-focused-active-current: var(--colorful-icon-color-alt);
}
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .mod-right-split .mod-top .workspace-tab-header.is-active .workspace-tab-header-inner-icon,
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .mod-left-split .mod-top .workspace-tab-header.is-active .workspace-tab-header-inner-icon,
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .mod-top .workspace-tab-header-container .clickable-icon.has-active-menu,
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .mod-top .workspace-tab-header-container .clickable-icon:active {
  --icon-color-focused: var(--colorful-icon-color-alt);
}
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .workspace-tab-header,
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .workspace-tab-header .workspace-tab-header-inner-close-button {
  --tab-text-color-focused: var(--colorful-icon-color-alt);
  --tab-text-color-focused-active-current: var(--colorful-icon-color-alt);
}
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .workspace-tab-header.is-active,
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .workspace-tab-header.is-active .workspace-tab-header-inner-close-button {
  --tab-text-color-focused: var(--text-faint);
  --tab-text-color-focused-active-current: var(--text-normal);
}
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .mod-left-split .mod-top .workspace-tab-header.is-active,
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .mod-right-split .mod-top .workspace-tab-header.is-active {
  background-color: rgba(var(--ctp-crust), 0.2);
}
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .mod-left-split .mod-top .workspace-tab-header.is-active:hover,
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .mod-right-split .mod-top .workspace-tab-header.is-active:hover {
  background-color: rgba(var(--ctp-crust), 0.3);
}
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).anp-colorful-frame-icon-toggle-light.theme-light .mod-left-split .mod-top .workspace-tab-header.is-active,
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).anp-colorful-frame-icon-toggle-light.theme-light .mod-right-split .mod-top .workspace-tab-header.is-active, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).anp-colorful-frame-icon-toggle-dark.theme-dark .mod-left-split .mod-top .workspace-tab-header.is-active,
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).anp-colorful-frame-icon-toggle-dark.theme-dark .mod-right-split .mod-top .workspace-tab-header.is-active {
  background-color: rgba(var(--ctp-text), 0.3);
}
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused.anp-mini-tab-toggle .mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-icon, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused.anp-mini-tab-toggle .mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-title, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused .mod-active:not(.mod-stacked).mod-top .workspace-tab-header.is-active .workspace-tab-header-inner-icon, .anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused.anp-mini-tab-toggle .mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-title,
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .mod-left-split .mod-top .workspace-tab-header:active .workspace-tab-header-inner-icon,
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .mod-right-split .mod-top .workspace-tab-header:active .workspace-tab-header-inner-icon {
  color: var(--colorful-icon-color-alt);
}
.anp-colorful-frame.anp-card-layout .mod-vertical .workspace-tabs {
  background-color: var(--tab-container-background);
}

.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).is-focused.anp-mini-tab-toggle .mod-top:not(.mod-stacked) .workspace-tab-header:not(.is-active),
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).anp-mini-tab-toggle .mod-top:not(.mod-stacked) .workspace-tab-header:not(.is-active):hover .workspace-tab-header-inner-close-button {
  color: var(--colorful-icon-color-alt);
  --tab-text-color-focused-active-current: var(--colorful-icon-color-alt);
}

.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .workspace-split.mod-vertical > * > .workspace-leaf-resize-handle,
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .workspace-split.mod-left-split > .workspace-leaf-resize-handle,
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout) .workspace-split.mod-right-split > .workspace-leaf-resize-handle {
  height: calc(100% - var(--header-height));
}

.anp-colorful-frame .sidebar-toggle-button.sidebar-toggle-button {
  background-color: var(--background-primary) !important;
}

.anp-colorful-frame .sidebar-toggle-button {
  padding: var(--size-4-1) !important;
  background-color: var(--background-primary);
  --tab-container-background: var(--background-primary) !important;
  height: calc(var(--header-height) - 5px);
  margin-right: 0;
  border-radius: 0 0 var(--radius-m) var(--radius-m);
  border-width: 0 1px 1px 1px;
  border-style: solid;
  border-color: var(--tab-outline-color);
  --icon-color: var(--text-muted);
  --icon-color-hover: var(--text-muted);
  --icon-color-active: var(--text-accent);
  --icon-color-focused: var(--text-normal);
}
.anp-colorful-frame .sidebar-toggle-button .clickable-icon {
  --icon-color: var(--text-muted) !important;
  --icon-color-hover: var(--text-muted) !important;
  --icon-color-active: var(--text-accent) !important;
  --icon-color-focused: var(--text-normal) !important;
}

.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).anp-mini-tab-toggle.is-focused .mod-active.mod-top .workspace-tab-header.is-active .workspace-tab-header-inner-icon,
.anp-colorful-frame:not(.anp-card-layout, .anp-border-layout).anp-mini-tab-toggle.is-focused .mod-active.mod-top .workspace-tab-header.is-active .workspace-tab-header-inner-title {
  color: var(--background-primary);
}

.anp-colorful-frame.mod-macos .sidebar-toggle-button.mod-right {
  border-radius: 0 0 0 var(--radius-m);
}

.anp-colorful-frame.is-fullscreen:not(.is-popout-window) .sidebar-toggle-button.mod-left {
  border-radius: 0 0 var(--radius-m) 0;
}

body.anp-colorful-frame.anp-card-layout .workspace,
.anp-colorful-frame.anuppuccin-accent-toggle.anp-card-layout .workspace-tabs {
  background-color: var(--tab-container-background);
}

.anp-colorful-frame.anp-card-layout,
.anp-colorful-frame.anp-card-layout .workspace-tab-container,
.anp-colorful-frame.anp-card-layout.anp-mini-tab-toggle .mod-vertical .workspace-tab-header.is-active,
.anp-colorful-frame.anp-card-layout:not(.anp-card-layout-filebrowser) .workspace-leaf-content[data-type=file-explorer] {
  --tab-text-color: var(--background-secondary-alt);
  --tab-text-color-focused: var(--background-secondary-alt);
  --tab-text-color-focused-active: var(--background-secondary-alt);
  --tab-text-color-focused-highlighted: var(--background-secondary-alt);
  --tab-text-color-focused-active-current: var(--background-secondary-alt);
  --icon-color: var(--background-secondary-alt);
  --icon-color-hover: var(--background-secondary-alt);
  --icon-color-active: var(--background-secondary-alt);
  --icon-color-focused: var(--background-secondary-alt);
}

.anp-colorful-frame.anp-card-layout:not(.anp-card-layout-filebrowser).anp-colorful-frame-icon-toggle-light.theme-light .workspace-leaf-content[data-type=file-explorer], .anp-colorful-frame.anp-card-layout:not(.anp-card-layout-filebrowser).anp-colorful-frame-icon-toggle-dark.theme-dark .workspace-leaf-content[data-type=file-explorer] {
  --tab-text-color: var(--text-normal);
  --tab-text-color-focused: var(--text-normal);
  --tab-text-color-focused-active: var(--text-normal);
  --tab-text-color-focused-highlighted: var(--text-normal);
  --tab-text-color-focused-active-current: var(--text-normal);
  --icon-color: var(--text-normal);
  --icon-color-hover: var(--text-normal);
  --icon-color-active: var(--text-normal);
  --icon-color-focused: var(--text-normal);
}

.anp-colorful-frame.anp-card-layout.anp-colorful-frame-icon-toggle-light.theme-light,
.anp-colorful-frame.anp-card-layout.anp-colorful-frame-icon-toggle-dark.theme-dark,
.anp-colorful-frame.anp-card-layout.anp-card-layout-actions .workspace-ribbon,
.anp-colorful-frame.anp-card-layout .workspace-tab-container,
.anp-colorful-frame.anp-card-layout .modal,
.anp-colorful-frame.anp-card-layout .mod-vertical .workspace-tab-header.is-active,
.anp-colorful-frame.anp-card-layout.anp-alternate-tab-toggle .mod-vertical .workspace-tab-header {
  --tab-text-color: var(--text-faint);
  --tab-text-color-active: var(--text-muted);
  --tab-text-color-focused: var(--text-muted);
  --tab-text-color-focused-active: var(--text-muted);
  --tab-text-color-focused-highlighted: var(--text-accent);
  --tab-text-color-focused-active-current: var(--text-normal);
  --icon-color: var(--text-muted);
  --icon-color-hover: var(--text-muted);
  --icon-color-active: var(--text-accent);
  --icon-color-focused: var(--text-normal);
}

.anp-colorful-frame.anp-card-layout:not(.anp-alternate-layout) .workspace-tab-header-status-icon {
  color: var(--icon-color);
}

.anp-colorful-frame.anp-card-layout.is-focused:not(.anp-mini-tab-toggle) .mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-icon,
.anp-colorful-frame.anp-card-layout.is-focused:not(.anp-mini-tab-toggle) .mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-title {
  color: var(--color-accent);
}

.anp-colorful-frame.anp-card-layout.anp-mini-tab-toggle .mod-vertical .workspace-tab-header .workspace-tab-header-inner-title,
.anp-colorful-frame.anp-card-layout.anp-mini-tab-toggle .mod-vertical .workspace-tab-header .workspace-tab-header-inner-title,
.anp-colorful-frame.anp-card-layout.is-focused:not(.anp-colorful-frame-icon-toggle-dark.theme-dark) .mod-left-split .mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-icon,
.anp-colorful-frame.anp-card-layout.is-focused:not(.anp-colorful-frame-icon-toggle-light.theme-light) .mod-left-split .mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-icon,
.anp-colorful-frame.anp-card-layout.is-focused:not(.anp-colorful-frame-icon-toggle-dark.theme-dark) .mod-right-split .mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-icon,
.anp-colorful-frame.anp-card-layout.is-focused:not(.anp-colorful-frame-icon-toggle-light.theme-light) .mod-right-split .mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-icon {
  color: var(--background-secondary-alt);
}

.anp-colorful-frame.anp-card-layout.is-focused.anp-colorful-frame-icon-toggle-light.theme-light .mod-left-split .mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-icon, .anp-colorful-frame.anp-card-layout.is-focused.anp-colorful-frame-icon-toggle-light.theme-light .mod-right-split .mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-icon, .anp-colorful-frame.anp-card-layout.is-focused.anp-colorful-frame-icon-toggle-dark.theme-dark .mod-left-split .mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-icon, .anp-colorful-frame.anp-card-layout.is-focused.anp-colorful-frame-icon-toggle-dark.theme-dark .mod-right-split .mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-icon {
  color: var(--text-normal);
}

.anp-colorful-frame.anp-card-layout .mod-left-split .workspace-tab-container, .anp-colorful-frame.anp-card-layout .mod-right-split .workspace-tab-container {
  background-color: transparent;
}
.anp-colorful-frame.anp-card-layout .mod-left-split {
  background-color: transparent;
}
.anp-colorful-frame.anp-card-layout .mod-vertical .workspace-tab-container {
  background-color: transparent;
}

.anp-colorful-frame.anp-card-layout .mod-right-split .workspace-tab-header.is-active,
.anp-colorful-frame.anp-card-layout .mod-left-split .workspace-tab-header.is-active {
  background-color: rgba(var(--ctp-crust), 0.2);
}
.anp-colorful-frame.anp-card-layout .mod-right-split .workspace-tab-header.is-active:hover,
.anp-colorful-frame.anp-card-layout .mod-left-split .workspace-tab-header.is-active:hover {
  background-color: rgba(var(--ctp-crust), 0.3);
}

.anp-colorful-frame.anp-card-layout.anp-colorful-frame-icon-toggle-light.theme-light .mod-right-split .workspace-tab-header.is-active, .anp-colorful-frame.anp-card-layout.anp-colorful-frame-icon-toggle-light.theme-light .mod-left-split .workspace-tab-header.is-active, .anp-colorful-frame.anp-card-layout.anp-colorful-frame-icon-toggle-dark.theme-dark .mod-right-split .workspace-tab-header.is-active, .anp-colorful-frame.anp-card-layout.anp-colorful-frame-icon-toggle-dark.theme-dark .mod-left-split .workspace-tab-header.is-active {
  background-color: rgba(var(--ctp-text), 0.3);
}

.mod-top .workspace-tab-header-inner::after {
  display: none;
}

.anp-colorful-frame.anp-card-layout:not(.anp-mini-tab-toggle) .mod-vertical .workspace-tab-header.is-active {
  --tab-text-color-focused: var(--text-muted);
  --tab-text-color-focused-active: var(--text-normal);
}

.anp-mini-tab-toggle.anp-colorful-frame .workspace-split:not(.mod-left-split):not(.mod-right-split) .workspace-tabs.mod-top .workspace-tab-header-container-inner .workspace-tab-header.is-active {
  border-bottom: 2px solid var(--background-secondary-alt);
}

.anp-colorful-frame.anp-card-layout .workspace-drop-overlay:before {
  background-color: var(--background-secondary-alt);
  border-radius: var(--radius-m);
  opacity: 0.5;
}

.anp-colorful-frame {
  --titlebar-background: var(--anp-colorful-frame-color, var(--color-accent));
  --titlebar-background-focused: var(--anp-colorful-frame-color, var(--color-accent));
  --titlebar-text-color: var(--background-primary);
  --titlebar-text-color-focused: var(--background-secondary-alt);
}

.anp-colorful-frame.is-frameless:not(.is-hidden-frameless) .sidebar-toggle-button.mod-right, .anp-colorful-frame.is-frameless:not(.is-hidden-frameless) .sidebar-toggle-button.mod-left {
  border-radius: var(--radius-m);
}

.anp-colorful-frame:not(.is-frameless) .sidebar-toggle-button.mod-right, .anp-colorful-frame:not(.is-frameless) .sidebar-toggle-button.mod-left {
  border-radius: var(--radius-m);
}

body.anp-colorful-frame.anp-border-layout .horizontal-main-container {
  background-color: var(--colorful-frame-color);
  --titlebar-background: var(--colorful-frame-color);
  --ribbon-background: var(--colorful-frame-color);
  --ribbon-background-collapsed: var(--colorful-frame-color);
  --card-background-color: var(--colorful-frame-color);
}
body.anp-colorful-frame.anp-border-layout .workspace-ribbon {
  --icon-color: var(--colorful-icon-color);
  --icon-color-hover: var(--colorful-icon-color-alt);
  --icon-color-active: var(--colorful-icon-color-alt);
  --icon-color-focused: var(--colorful-icon-color);
}
body.anp-colorful-frame.anp-border-layout .workspace-ribbon .clickable-icon:hover {
  background-color: rgba(var(--ctp-crust), 0.2);
}
body.anp-colorful-frame.anp-border-layout .workspace-ribbon .clickable-icon:active {
  background-color: rgba(var(--ctp-crust), 0.3);
}
body.anp-colorful-frame.anp-border-layout .workspace-tabs.mod-top .workspace-tab-header-container .clickable-icon {
  --icon-color: var(--colorful-icon-color);
  --icon-color-hover: var(--colorful-icon-color-alt);
  --icon-color-active: var(--colorful-icon-color-alt);
  --icon-color-focused: var(--colorful-icon-color);
}
body.anp-colorful-frame.anp-border-layout .workspace-tabs.mod-top .workspace-tab-header-container .clickable-icon:hover {
  background-color: rgba(var(--ctp-crust), 0.2);
}
body.anp-colorful-frame.anp-border-layout .workspace-tabs.mod-top .workspace-tab-header-container .clickable-icon:active {
  background-color: rgba(var(--ctp-crust), 0.3);
}
body.anp-colorful-frame.anp-border-layout .workspace-split.mod-right-split .workspace-tabs.mod-top > .workspace-tab-header-container, body.anp-colorful-frame.anp-border-layout .workspace-split.mod-left-split .workspace-tabs.mod-top > .workspace-tab-header-container {
  --icon-color: var(--colorful-icon-color);
  --icon-color-hover: var(--colorful-icon-color-alt);
  --icon-color-active: var(--colorful-icon-color-alt);
  --icon-color-focused: var(--colorful-icon-color);
}
body.anp-colorful-frame.anp-border-layout .workspace-split.mod-right-split .workspace-tabs.mod-top > .workspace-tab-header-container .workspace-tab-header:not(.is-active):hover .workspace-tab-header-inner, body.anp-colorful-frame.anp-border-layout .workspace-split.mod-left-split .workspace-tabs.mod-top > .workspace-tab-header-container .workspace-tab-header:not(.is-active):hover .workspace-tab-header-inner {
  background-color: rgba(var(--ctp-crust), 0.2);
}
body.anp-colorful-frame.anp-border-layout .workspace-split.mod-right-split .workspace-tabs.mod-top > .workspace-tab-header-container .workspace-tab-header.is-active .workspace-tab-header-inner, body.anp-colorful-frame.anp-border-layout .workspace-split.mod-left-split .workspace-tabs.mod-top > .workspace-tab-header-container .workspace-tab-header.is-active .workspace-tab-header-inner {
  background-color: rgba(var(--ctp-crust), 0.3);
}
body.anp-colorful-frame.anp-border-layout .workspace-split.mod-right-split .workspace-tabs.mod-top > .workspace-tab-header-container .clickable-icon:hover, body.anp-colorful-frame.anp-border-layout .workspace-split.mod-left-split .workspace-tabs.mod-top > .workspace-tab-header-container .clickable-icon:hover {
  background-color: rgba(var(--ctp-crust), 0.2);
}
body.anp-colorful-frame.anp-border-layout .workspace-split.mod-right-split .workspace-tabs.mod-top > .workspace-tab-header-container .clickable-icon:active, body.anp-colorful-frame.anp-border-layout .workspace-split.mod-left-split .workspace-tabs.mod-top > .workspace-tab-header-container .clickable-icon:active {
  background-color: rgba(var(--ctp-crust), 0.3);
}
body.anp-colorful-frame.anp-border-layout .workspace-split.mod-right-split .workspace-tabs.mod-top > .workspace-tab-header-container .workspace-tab-header.is-active .workspace-tab-header-inner-icon, body.anp-colorful-frame.anp-border-layout .workspace-split.mod-right-split .workspace-tabs.mod-top > .workspace-tab-header-container .is-focused .mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-title, body.anp-colorful-frame.anp-border-layout .workspace-split.mod-left-split .workspace-tabs.mod-top > .workspace-tab-header-container .workspace-tab-header.is-active .workspace-tab-header-inner-icon, body.anp-colorful-frame.anp-border-layout .workspace-split.mod-left-split .workspace-tabs.mod-top > .workspace-tab-header-container .is-focused .mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-title {
  color: var(--colorful-icon-color);
}
body.anp-colorful-frame.anp-border-layout.anp-mini-tab-toggle .workspace-tabs.mod-top .workspace-tab-header-inner-title,
body.anp-colorful-frame.anp-border-layout.anp-mini-tab-toggle .workspace-tabs.mod-top .workspace-tab-header-status-container,
body.anp-colorful-frame.anp-border-layout.anp-mini-tab-toggle .workspace-tabs.mod-top .workspace-tab-header-inner-close-button,
body.anp-colorful-frame.anp-border-layout.anp-mini-tab-toggle .workspace-tabs.mod-top .workspace-tab-header-inner-icon {
  color: var(--background-primary);
}

/*------------CUSTOM CHECKBOXES-----------*/
/*----------------------------------
This feature is inspired by Cecilia May's Primary theme: https://github.com/ceciliamay/obsidianmd-theme-primary
None of the original code was used and only the checkbox ideas were utilised.
Icons were taken from Font Awesome: https://fontawesome.com/
----------------------------------*/
.anp-custom-checkboxes [data-task=x] > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task=x] > p > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task=x][type=checkbox]:checked {
  --checkbox-color: rgb(var(--ctp-green));
  --checkbox-color-hover: rgb(var(--ctp-green)) ;
}
.anp-custom-checkboxes [data-task="!"] > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task="!"] > p > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task="!"][type=checkbox]:checked {
  --checkbox-color: rgb(var(--ctp-yellow));
  --checkbox-color-hover: rgb(var(--ctp-yellow));
}
.anp-custom-checkboxes [data-task="!"] > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task="!"] > p > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task="!"][type=checkbox]:checked:after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 128 512'%3E%3C!--! Font Awesome Pro 6.2.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2022 Fonticons, Inc. --%3E%3Cpath d='M96 64c0-17.7-14.3-32-32-32S32 46.3 32 64V320c0 17.7 14.3 32 32 32s32-14.3 32-32V64zM64 480c22.1 0 40-17.9 40-40s-17.9-40-40-40s-40 17.9-40 40s17.9 40 40 40z'/%3E%3C/svg%3E");
  -webkit-mask-size: 20%;
}
.anp-custom-checkboxes [data-task="!"] > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task="!"] > p > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task="!"][type=checkbox]:checked:before {
  color: var(--checkbox-color);
  margin: 0 3px;
  position: absolute;
  left: calc(var(--checkbox-size) * 1);
  font-weight: bold;
}
.anp-custom-checkboxes [data-task="?"] > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task="?"] > p > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task="?"][type=checkbox]:checked {
  --checkbox-color: transparent;
  --checkbox-color-hover: transparent;
  border-width: 0;
}
.anp-custom-checkboxes [data-task="?"] > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task="?"] > p > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task="?"][type=checkbox]:checked:after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3C!--! Font Awesome Pro 6.2.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2022 Fonticons, Inc. --%3E%3Cpath d='M256 512c141.4 0 256-114.6 256-256S397.4 0 256 0S0 114.6 0 256S114.6 512 256 512zM169.8 165.3c7.9-22.3 29.1-37.3 52.8-37.3h58.3c34.9 0 63.1 28.3 63.1 63.1c0 22.6-12.1 43.5-31.7 54.8L280 264.4c-.2 13-10.9 23.6-24 23.6c-13.3 0-24-10.7-24-24V250.5c0-8.6 4.6-16.5 12.1-20.8l44.3-25.4c4.7-2.7 7.6-7.7 7.6-13.1c0-8.4-6.8-15.1-15.1-15.1H222.6c-3.4 0-6.4 2.1-7.5 5.3l-.4 1.2c-4.4 12.5-18.2 19-30.6 14.6s-19-18.2-14.6-30.6l.4-1.2zM288 352c0 17.7-14.3 32-32 32s-32-14.3-32-32s14.3-32 32-32s32 14.3 32 32z'/%3E%3C/svg%3E");
  -webkit-mask-size: contain;
  background-color: rgb(var(--ctp-peach));
  left: 0px;
}
.anp-custom-checkboxes [data-task="?"] > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task="?"] > p > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task="?"][type=checkbox]:checked:before {
  color: rgb(var(--ctp-peach));
  margin: 0 3px;
  position: absolute;
  left: calc(var(--checkbox-size) * 1);
  font-weight: bold;
}
.anp-custom-checkboxes [data-task="-"] {
  text-decoration: var(--checklist-done-decoration);
  color: var(--checklist-done-color);
}
.anp-custom-checkboxes [data-task="-"] > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task="-"] > p > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task="-"][type=checkbox]:checked {
  --checkbox-color: rgb(var(--ctp-red));
  --checkbox-color-hover: rgb(var(--ctp-red));
}
.anp-custom-checkboxes [data-task="-"] > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task="-"] > p > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task="-"][type=checkbox]:checked:after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 320 512'%3E%3C!--! Font Awesome Pro 6.2.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2022 Fonticons, Inc. --%3E%3Cpath d='M310.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L160 210.7 54.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L114.7 256 9.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L160 301.3 265.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L205.3 256 310.6 150.6z'/%3E%3C/svg%3E");
  -webkit-mask-size: 50%;
}
.anp-custom-checkboxes [data-task="-"] > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task="-"] > p > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task="-"][type=checkbox]:checked:before {
  color: var(--checkbox-color);
  margin: 0 3px;
  position: absolute;
  left: calc(var(--checkbox-size) * 1);
  font-weight: bold;
}
.anp-custom-checkboxes [data-task=b] > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task=b] > p > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task=b][type=checkbox]:checked {
  --checkbox-color: transparent;
  --checkbox-color-hover: transparent;
  border-width: 0;
}
.anp-custom-checkboxes [data-task=b] > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task=b] > p > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task=b][type=checkbox]:checked:after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 384 512'%3E%3C!--! Font Awesome Pro 6.2.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2022 Fonticons, Inc. --%3E%3Cpath d='M0 48V487.7C0 501.1 10.9 512 24.3 512c5 0 9.9-1.5 14-4.4L192 400 345.7 507.6c4.1 2.9 9 4.4 14 4.4c13.4 0 24.3-10.9 24.3-24.3V48c0-26.5-21.5-48-48-48H48C21.5 0 0 21.5 0 48z'/%3E%3C/svg%3E");
  -webkit-mask-size: contain;
  background-color: rgb(var(--ctp-red));
  left: 0px;
}
.anp-custom-checkboxes [data-task=b] > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task=b] > p > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task=b][type=checkbox]:checked:before {
  color: rgb(var(--ctp-red));
  margin: 0 3px;
  position: absolute;
  left: calc(var(--checkbox-size) * 1);
  font-weight: bold;
}
.anp-custom-checkboxes [data-task=I] > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task=I] > p > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task=I][type=checkbox]:checked {
  --checkbox-color: transparent;
  --checkbox-color-hover: transparent;
  border-width: 0;
}
.anp-custom-checkboxes [data-task=I] > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task=I] > p > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task=I][type=checkbox]:checked:after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 384 512'%3E%3C!--! Font Awesome Pro 6.2.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2022 Fonticons, Inc. --%3E%3Cpath d='M272 384c9.6-31.9 29.5-59.1 49.2-86.2l0 0c5.2-7.1 10.4-14.2 15.4-21.4c19.8-28.5 31.4-63 31.4-100.3C368 78.8 289.2 0 192 0S16 78.8 16 176c0 37.3 11.6 71.9 31.4 100.3c5 7.2 10.2 14.3 15.4 21.4l0 0c19.8 27.1 39.7 54.4 49.2 86.2H272zM192 512c44.2 0 80-35.8 80-80V416H112v16c0 44.2 35.8 80 80 80zM112 176c0 8.8-7.2 16-16 16s-16-7.2-16-16c0-61.9 50.1-112 112-112c8.8 0 16 7.2 16 16s-7.2 16-16 16c-44.2 0-80 35.8-80 80z'/%3E%3C/svg%3E");
  -webkit-mask-size: contain;
  background-color: rgb(var(--ctp-yellow));
  left: 0px;
}
.anp-custom-checkboxes [data-task=I] > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task=I] > p > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task=I][type=checkbox]:checked:before {
  color: rgb(var(--ctp-yellow));
  margin: 0 3px;
  position: absolute;
  left: calc(var(--checkbox-size) * 1);
  font-weight: bold;
}
.anp-custom-checkboxes [data-task=p] > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task=p] > p > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task=p][type=checkbox]:checked {
  --checkbox-color: transparent;
  --checkbox-color-hover: transparent;
  border-width: 0;
}
.anp-custom-checkboxes [data-task=p] > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task=p] > p > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task=p][type=checkbox]:checked:after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3C!--! Font Awesome Pro 6.2.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2022 Fonticons, Inc. --%3E%3Cpath d='M313.4 32.9c26 5.2 42.9 30.5 37.7 56.5l-2.3 11.4c-5.3 26.7-15.1 52.1-28.8 75.2H464c26.5 0 48 21.5 48 48c0 25.3-19.5 46-44.3 47.9c7.7 8.5 12.3 19.8 12.3 32.1c0 23.4-16.8 42.9-38.9 47.1c4.4 7.2 6.9 15.8 6.9 24.9c0 21.3-13.9 39.4-33.1 45.6c.7 3.3 1.1 6.8 1.1 10.4c0 26.5-21.5 48-48 48H294.5c-19 0-37.5-5.6-53.3-16.1l-38.5-25.7C176 420.4 160 390.4 160 358.3V320 272 247.1c0-29.2 13.3-56.7 36-75l7.4-5.9c26.5-21.2 44.6-51 51.2-84.2l2.3-11.4c5.2-26 30.5-42.9 56.5-37.7zM32 192H96c17.7 0 32 14.3 32 32V448c0 17.7-14.3 32-32 32H32c-17.7 0-32-14.3-32-32V224c0-17.7 14.3-32 32-32z'/%3E%3C/svg%3E");
  -webkit-mask-size: contain;
  background-color: rgb(var(--ctp-green));
  left: 0px;
}
.anp-custom-checkboxes [data-task=p] > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task=p] > p > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task=p][type=checkbox]:checked:before {
  color: rgb(var(--ctp-green));
  margin: 0 3px;
  position: absolute;
  left: calc(var(--checkbox-size) * 1);
  font-weight: bold;
}
.anp-custom-checkboxes [data-task=c] > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task=c] > p > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task=c][type=checkbox]:checked {
  --checkbox-color: transparent;
  --checkbox-color-hover: transparent;
  border-width: 0;
}
.anp-custom-checkboxes [data-task=c] > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task=c] > p > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task=c][type=checkbox]:checked:after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3C!--! Font Awesome Pro 6.2.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2022 Fonticons, Inc. --%3E%3Cpath d='M313.4 479.1c26-5.2 42.9-30.5 37.7-56.5l-2.3-11.4c-5.3-26.7-15.1-52.1-28.8-75.2H464c26.5 0 48-21.5 48-48c0-25.3-19.5-46-44.3-47.9c7.7-8.5 12.3-19.8 12.3-32.1c0-23.4-16.8-42.9-38.9-47.1c4.4-7.3 6.9-15.8 6.9-24.9c0-21.3-13.9-39.4-33.1-45.6c.7-3.3 1.1-6.8 1.1-10.4c0-26.5-21.5-48-48-48H294.5c-19 0-37.5 5.6-53.3 16.1L202.7 73.8C176 91.6 160 121.6 160 153.7V192v48 24.9c0 29.2 13.3 56.7 36 75l7.4 5.9c26.5 21.2 44.6 51 51.2 84.2l2.3 11.4c5.2 26 30.5 42.9 56.5 37.7zM32 320H96c17.7 0 32-14.3 32-32V64c0-17.7-14.3-32-32-32H32C14.3 32 0 46.3 0 64V288c0 17.7 14.3 32 32 32z'/%3E%3C/svg%3E");
  -webkit-mask-size: 100%;
  background-color: rgb(var(--ctp-red));
  left: 0px;
}
.anp-custom-checkboxes [data-task=c] > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task=c] > p > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task=c][type=checkbox]:checked:before {
  color: var(--checkbox-color);
  margin: 0 3px;
  position: absolute;
  left: calc(var(--checkbox-size) * 1);
  font-weight: bold;
}
.anp-custom-checkboxes [data-task=i] > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task=i] > p > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task=i][type=checkbox]:checked {
  --checkbox-color: transparent;
  --checkbox-color-hover: transparent;
  border-width: 0;
}
.anp-custom-checkboxes [data-task=i] > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task=i] > p > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task=i][type=checkbox]:checked:after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3C!--! Font Awesome Pro 6.2.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2022 Fonticons, Inc. --%3E%3Cpath d='M256 512c141.4 0 256-114.6 256-256S397.4 0 256 0S0 114.6 0 256S114.6 512 256 512zM216 336h24V272H216c-13.3 0-24-10.7-24-24s10.7-24 24-24h48c13.3 0 24 10.7 24 24v88h8c13.3 0 24 10.7 24 24s-10.7 24-24 24H216c-13.3 0-24-10.7-24-24s10.7-24 24-24zm40-144c-17.7 0-32-14.3-32-32s14.3-32 32-32s32 14.3 32 32s-14.3 32-32 32z'/%3E%3C/svg%3E");
  -webkit-mask-size: contain;
  background-color: rgb(var(--ctp-blue));
  left: 0px;
}
.anp-custom-checkboxes [data-task=i] > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task=i] > p > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task=i][type=checkbox]:checked:before {
  color: rgb(var(--ctp-blue));
  margin: 0 3px;
  position: absolute;
  left: calc(var(--checkbox-size) * 1);
  font-weight: bold;
}
.anp-custom-checkboxes [data-task=l] > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task=l] > p > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task=l][type=checkbox]:checked {
  --checkbox-color: transparent;
  --checkbox-color-hover: transparent;
  border-width: 0;
}
.anp-custom-checkboxes [data-task=l] > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task=l] > p > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task=l][type=checkbox]:checked:after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 384 512'%3E%3C!--! Font Awesome Pro 6.2.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2022 Fonticons, Inc. --%3E%3Cpath d='M215.7 499.2C267 435 384 279.4 384 192C384 86 298 0 192 0S0 86 0 192c0 87.4 117 243 168.3 307.2c12.3 15.3 35.1 15.3 47.4 0zM192 256c-35.3 0-64-28.7-64-64s28.7-64 64-64s64 28.7 64 64s-28.7 64-64 64z'/%3E%3C/svg%3E");
  -webkit-mask-size: contain;
  background-color: rgb(var(--ctp-mauve));
  left: 0px;
}
.anp-custom-checkboxes [data-task=l] > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task=l] > p > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task=l][type=checkbox]:checked:before {
  color: rgb(var(--ctp-mauve));
  margin: 0 3px;
  position: absolute;
  left: calc(var(--checkbox-size) * 1);
  font-weight: bold;
}
.anp-custom-checkboxes [data-task="*"] > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task="*"] > p > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task="*"][type=checkbox]:checked {
  --checkbox-color: transparent;
  --checkbox-color-hover: transparent;
  border-width: 0;
}
.anp-custom-checkboxes [data-task="*"] > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task="*"] > p > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task="*"][type=checkbox]:checked:after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 576 512'%3E%3C!--! Font Awesome Pro 6.2.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2022 Fonticons, Inc. --%3E%3Cpath d='M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z'/%3E%3C/svg%3E");
  -webkit-mask-size: contain;
  background-color: rgb(var(--ctp-yellow));
  left: 0px;
}
.anp-custom-checkboxes [data-task="*"] > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task="*"] > p > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task="*"][type=checkbox]:checked:before {
  color: rgb(var(--ctp-yellow));
  margin: 0 3px;
  position: absolute;
  left: calc(var(--checkbox-size) * 1);
  font-weight: bold;
}
.anp-custom-checkboxes [data-task=n] > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task=n] > p > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task=n][type=checkbox]:checked {
  --checkbox-color: transparent;
  --checkbox-color-hover: transparent;
  border-width: 0;
}
.anp-custom-checkboxes [data-task=n] > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task=n] > p > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task=n][type=checkbox]:checked:after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 384 512'%3E%3C!--! Font Awesome Pro 6.2.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2022 Fonticons, Inc. --%3E%3Cpath d='M32 32C32 14.3 46.3 0 64 0H320c17.7 0 32 14.3 32 32s-14.3 32-32 32H290.5l11.4 148.2c36.7 19.9 65.7 53.2 79.5 94.7l1 3c3.3 9.8 1.6 20.5-4.4 28.8s-15.7 13.3-26 13.3H32c-10.3 0-19.9-4.9-26-13.3s-7.7-19.1-4.4-28.8l1-3c13.8-41.5 42.8-74.8 79.5-94.7L93.5 64H64C46.3 64 32 49.7 32 32zM160 384h64v96c0 17.7-14.3 32-32 32s-32-14.3-32-32V384z'/%3E%3C/svg%3E");
  -webkit-mask-size: contain;
  background-color: rgb(var(--ctp-maroon));
  left: 0px;
}
.anp-custom-checkboxes [data-task=n] > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task=n] > p > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task=n][type=checkbox]:checked:before {
  color: rgb(var(--ctp-maroon));
  margin: 0 3px;
  position: absolute;
  left: calc(var(--checkbox-size) * 1);
  font-weight: bold;
}
.anp-custom-checkboxes [data-task=S] > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task=S] > p > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task=S][type=checkbox]:checked {
  --checkbox-color: transparent;
  --checkbox-color-hover: transparent;
  border-width: 0;
}
.anp-custom-checkboxes [data-task=S] > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task=S] > p > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task=S][type=checkbox]:checked:after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3C!--! Font Awesome Pro 6.2.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2022 Fonticons, Inc. --%3E%3Cpath d='M320 96H192L144.6 24.9C137.5 14.2 145.1 0 157.9 0H354.1c12.8 0 20.4 14.2 13.3 24.9L320 96zM192 128H320c3.8 2.5 8.1 5.3 13 8.4C389.7 172.7 512 250.9 512 416c0 53-43 96-96 96H96c-53 0-96-43-96-96C0 250.9 122.3 172.7 179 136.4l0 0 0 0c4.8-3.1 9.2-5.9 13-8.4zm84.1 96c0-11.1-9-20.1-20.1-20.1s-20.1 9-20.1 20.1v6c-5.6 1.2-10.9 2.9-15.9 5.1c-15 6.8-27.9 19.4-31.1 37.7c-1.8 10.2-.8 20 3.4 29c4.2 8.8 10.7 15 17.3 19.5c11.6 7.9 26.9 12.5 38.6 16l2.2 .7c13.9 4.2 23.4 7.4 29.3 11.7c2.5 1.8 3.4 3.2 3.8 4.1c.3 .8 .9 2.6 .2 6.7c-.6 3.5-2.5 6.4-8 8.8c-6.1 2.6-16 3.9-28.8 1.9c-6-1-16.7-4.6-26.2-7.9l0 0 0 0 0 0 0 0c-2.2-.8-4.3-1.5-6.3-2.1c-10.5-3.5-21.8 2.2-25.3 12.7s2.2 21.8 12.7 25.3c1.2 .4 2.7 .9 4.4 1.5c7.9 2.7 20.3 6.9 29.8 9.1V416c0 11.1 9 20.1 20.1 20.1s20.1-9 20.1-20.1v-5.5c5.4-1 10.5-2.5 15.4-4.6c15.7-6.7 28.4-19.7 31.6-38.7c1.8-10.4 1-20.3-3-29.4c-3.9-9-10.2-15.6-16.9-20.5c-12.2-8.8-28.3-13.7-40.4-17.4l-.8-.2c-14.2-4.3-23.8-7.3-29.9-11.4c-2.6-1.8-3.4-3-3.6-3.5c-.2-.3-.7-1.6-.1-5c.3-1.9 1.9-5.2 8.2-8.1c6.4-2.9 16.4-4.5 28.6-2.6c4.3 .7 17.9 3.3 21.7 4.3c10.7 2.8 21.6-3.5 24.5-14.2s-3.5-21.6-14.2-24.5c-4.4-1.2-14.4-3.2-21-4.4V224z'/%3E%3C/svg%3E");
  -webkit-mask-size: contain;
  background-color: rgb(var(--ctp-green));
  left: 0px;
}
.anp-custom-checkboxes [data-task=S] > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task=S] > p > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task=S][type=checkbox]:checked:before {
  color: rgb(var(--ctp-green));
  margin: 0 3px;
  position: absolute;
  left: calc(var(--checkbox-size) * 1);
  font-weight: bold;
}
.anp-custom-checkboxes [data-task="/"] > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task="/"] > p > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task="/"][type=checkbox]:checked {
  --checkbox-color: rgba(var(--ctp-subtext0), 0.3);
  --checkbox-color-hover: rgba(var(--ctp-subtext0), 0.3);
  border-color: rgb(var(--ctp-subtext0)) !important;
}
.anp-custom-checkboxes [data-task="/"] > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task="/"] > p > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task="/"][type=checkbox]:checked:after {
  background-color: transparent;
}
.anp-custom-checkboxes [data-task="/"] > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task="/"] > p > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task="/"][type=checkbox]:checked:before {
  color: rgb(var(--ctp-subtext0));
  margin: 0 3px;
  position: absolute;
  left: calc(var(--checkbox-size) * 1);
  font-weight: bold;
}
.anp-custom-checkboxes [data-task="<"] > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task="<"] > p > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task="<"][type=checkbox]:checked {
  --checkbox-color: transparent;
  --checkbox-color-hover: transparent;
  border-width: 0;
}
.anp-custom-checkboxes [data-task="<"] > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task="<"] > p > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task="<"][type=checkbox]:checked:after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512'%3E%3C!--! Font Awesome Pro 6.2.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2022 Fonticons, Inc. --%3E%3Cpath d='M96 32V64H48C21.5 64 0 85.5 0 112v48H448V112c0-26.5-21.5-48-48-48H352V32c0-17.7-14.3-32-32-32s-32 14.3-32 32V64H160V32c0-17.7-14.3-32-32-32S96 14.3 96 32zM448 192H0V464c0 26.5 21.5 48 48 48H400c26.5 0 48-21.5 48-48V192z'/%3E%3C/svg%3E");
  -webkit-mask-size: contain;
  background-color: rgb(var(--ctp-teal));
  left: 0px;
}
.anp-custom-checkboxes [data-task="<"] > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task="<"] > p > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task="<"][type=checkbox]:checked:before {
  color: rgb(var(--ctp-teal));
  margin: 0 3px;
  position: absolute;
  left: calc(var(--checkbox-size) * 1);
  font-weight: bold;
}
.anp-custom-checkboxes [data-task=">"] > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task=">"] > p > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task=">"][type=checkbox]:checked {
  --checkbox-color: transparent;
  --checkbox-color-hover: transparent;
  border-width: 0;
}
.anp-custom-checkboxes [data-task=">"] > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task=">"] > p > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task=">"][type=checkbox]:checked:after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3C!--! Font Awesome Pro 6.2.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2022 Fonticons, Inc. --%3E%3Cpath d='M307 34.8c-11.5 5.1-19 16.6-19 29.2v64H176C78.8 128 0 206.8 0 304C0 417.3 81.5 467.9 100.2 478.1c2.5 1.4 5.3 1.9 8.1 1.9c10.9 0 19.7-8.9 19.7-19.7c0-7.5-4.3-14.4-9.8-19.5C108.8 431.9 96 414.4 96 384c0-53 43-96 96-96h96v64c0 12.6 7.4 24.1 19 29.2s25 3 34.4-5.4l160-144c6.7-6.1 10.6-14.7 10.6-23.8s-3.8-17.7-10.6-23.8l-160-144c-9.4-8.5-22.9-10.6-34.4-5.4z'/%3E%3C/svg%3E");
  -webkit-mask-size: contain;
  background-color: rgb(var(--ctp-sapphire));
  left: 0px;
}
.anp-custom-checkboxes [data-task=">"] > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task=">"] > p > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task=">"][type=checkbox]:checked:before {
  color: rgb(var(--ctp-sapphire));
  margin: 0 3px;
  position: absolute;
  left: calc(var(--checkbox-size) * 1);
  font-weight: bold;
}
.anp-custom-checkboxes [data-task='"'] > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task='"'] > p > input[type=checkbox]:checked, .anp-custom-checkboxes [data-task='"'][type=checkbox]:checked {
  --checkbox-color: transparent;
  --checkbox-color-hover: transparent;
  border-width: 0;
}
.anp-custom-checkboxes [data-task='"'] > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task='"'] > p > input[type=checkbox]:checked:after, .anp-custom-checkboxes [data-task='"'][type=checkbox]:checked:after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512'%3E%3C!--! Font Awesome Pro 6.2.1 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2022 Fonticons, Inc. --%3E%3Cpath d='M0 216C0 149.7 53.7 96 120 96h8c17.7 0 32 14.3 32 32s-14.3 32-32 32h-8c-30.9 0-56 25.1-56 56v8h64c35.3 0 64 28.7 64 64v64c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V320 288 216zm256 0c0-66.3 53.7-120 120-120h8c17.7 0 32 14.3 32 32s-14.3 32-32 32h-8c-30.9 0-56 25.1-56 56v8h64c35.3 0 64 28.7 64 64v64c0 35.3-28.7 64-64 64H320c-35.3 0-64-28.7-64-64V320 288 216z'/%3E%3C/svg%3E");
  -webkit-mask-size: contain;
  background-color: rgb(var(--ctp-subtext0));
  left: 0px;
}
.anp-custom-checkboxes [data-task='"'] > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task='"'] > p > input[type=checkbox]:checked:before, .anp-custom-checkboxes [data-task='"'][type=checkbox]:checked:before {
  color: rgb(var(--ctp-subtext0));
  margin: 0 3px;
  position: absolute;
  left: calc(var(--checkbox-size) * 1);
  font-weight: bold;
}

.anp-custom-checkboxes-labels [data-task="!"] input[type=checkbox]:checked:before, .anp-custom-checkboxes-labels [data-task="!"][type=checkbox]:checked:before {
  content: "IMP";
}

.anp-custom-checkboxes-labels [data-task=">"] input[type=checkbox]:checked:before, .anp-custom-checkboxes-labels [data-task=">"][type=checkbox]:checked:before {
  content: "RSCH";
}

.anp-custom-checkboxes-labels [data-task="<"] input[type=checkbox]:checked:before, .anp-custom-checkboxes-labels [data-task="<"][type=checkbox]:checked:before {
  content: "SCH";
}

.anp-custom-checkboxes-labels [data-task="-"] input[type=checkbox]:checked:before, .anp-custom-checkboxes-labels [data-task="-"][type=checkbox]:checked:before {
  content: "CNCL";
}

.anp-custom-checkboxes-labels [data-task="/"] input[type=checkbox]:checked:before, .anp-custom-checkboxes-labels [data-task="/"][type=checkbox]:checked:before {
  content: "PRG";
}

.anp-custom-checkboxes-labels [data-task="?"] input[type=checkbox]:checked:before, .anp-custom-checkboxes-labels [data-task="?"][type=checkbox]:checked:before {
  content: "QUE";
}

.anp-custom-checkboxes-labels [data-task="*"] input[type=checkbox]:checked:before, .anp-custom-checkboxes-labels [data-task="*"][type=checkbox]:checked:before {
  content: "STAR";
}

.anp-custom-checkboxes-labels [data-task=n] input[type=checkbox]:checked:before, .anp-custom-checkboxes-labels [data-task=n][type=checkbox]:checked:before {
  content: "NOTE";
}

.anp-custom-checkboxes-labels [data-task=l] input[type=checkbox]:checked:before, .anp-custom-checkboxes-labels [data-task=l][type=checkbox]:checked:before {
  content: "LCTN";
}

.anp-custom-checkboxes-labels [data-task=i] input[type=checkbox]:checked:before, .anp-custom-checkboxes-labels [data-task=i][type=checkbox]:checked:before {
  content: "INFO";
}

.anp-custom-checkboxes-labels [data-task=I] input[type=checkbox]:checked:before, .anp-custom-checkboxes-labels [data-task=I][type=checkbox]:checked:before {
  content: "IDEA";
}

.anp-custom-checkboxes-labels [data-task=S] input[type=checkbox]:checked:before, .anp-custom-checkboxes-labels [data-task=S][type=checkbox]:checked:before {
  content: "AMT";
}

.anp-custom-checkboxes-labels [data-task=p] input[type=checkbox]:checked:before, .anp-custom-checkboxes-labels [data-task=p][type=checkbox]:checked:before {
  content: "PRO";
}

.anp-custom-checkboxes-labels [data-task=c] input[type=checkbox]:checked:before, .anp-custom-checkboxes-labels [data-task=c][type=checkbox]:checked:before {
  content: "CON";
}

.anp-custom-checkboxes-labels [data-task=b] input[type=checkbox]:checked:before, .anp-custom-checkboxes-labels [data-task=b][type=checkbox]:checked:before {
  content: "BKMK";
}

.anp-custom-checkboxes-labels [data-task='"'] input[type=checkbox]:checked:before, .anp-custom-checkboxes-labels [data-task='"'][type=checkbox]:checked:before {
  content: "QUOT";
}
.anp-custom-checkboxes-labels .task-list-item .task-list-item-checkbox {
  margin-right: calc(var(--checkbox-size) * 2.7);
}

.anp-speech-bubble [data-task="0"],
.anp-speech-bubble [data-task="1"],
.anp-speech-bubble [data-task="2"],
.anp-speech-bubble [data-task="3"],
.anp-speech-bubble [data-task="4"],
.anp-speech-bubble [data-task="5"],
.anp-speech-bubble [data-task="6"],
.anp-speech-bubble [data-task="7"],
.anp-speech-bubble [data-task="8"],
.anp-speech-bubble [data-task="9"] {
  margin-inline-start: -15px;
  padding: 3px 10px !important;
  border-radius: 3px 10px 10px 10px;
  max-width: fit-content;
  color: var(--text-normal);
  --bold-color: var(--text-normal);
  --italic-color: var(--text-normal);
}
.anp-speech-bubble [data-task="0"] p,
.anp-speech-bubble [data-task="1"] p,
.anp-speech-bubble [data-task="2"] p,
.anp-speech-bubble [data-task="3"] p,
.anp-speech-bubble [data-task="4"] p,
.anp-speech-bubble [data-task="5"] p,
.anp-speech-bubble [data-task="6"] p,
.anp-speech-bubble [data-task="7"] p,
.anp-speech-bubble [data-task="8"] p,
.anp-speech-bubble [data-task="9"] p {
  margin-top: 0;
  margin-bottom: 0;
}
.anp-speech-bubble [data-task="0"] .task-list-item-checkbox,
.anp-speech-bubble [data-task="1"] .task-list-item-checkbox,
.anp-speech-bubble [data-task="2"] .task-list-item-checkbox,
.anp-speech-bubble [data-task="3"] .task-list-item-checkbox,
.anp-speech-bubble [data-task="4"] .task-list-item-checkbox,
.anp-speech-bubble [data-task="5"] .task-list-item-checkbox,
.anp-speech-bubble [data-task="6"] .task-list-item-checkbox,
.anp-speech-bubble [data-task="7"] .task-list-item-checkbox,
.anp-speech-bubble [data-task="8"] .task-list-item-checkbox,
.anp-speech-bubble [data-task="9"] .task-list-item-checkbox {
  display: none;
}
.anp-speech-bubble [data-task="0"]::selection,
.anp-speech-bubble [data-task="1"]::selection,
.anp-speech-bubble [data-task="2"]::selection,
.anp-speech-bubble [data-task="3"]::selection,
.anp-speech-bubble [data-task="4"]::selection,
.anp-speech-bubble [data-task="5"]::selection,
.anp-speech-bubble [data-task="6"]::selection,
.anp-speech-bubble [data-task="7"]::selection,
.anp-speech-bubble [data-task="8"]::selection,
.anp-speech-bubble [data-task="9"]::selection {
  background-color: rgba(var(--ctp-text), 0.2);
}

.theme-dark.anp-speech-bubble [data-task="0"],
.theme-dark.anp-speech-bubble [data-task="1"],
.theme-dark.anp-speech-bubble [data-task="2"],
.theme-dark.anp-speech-bubble [data-task="3"],
.theme-dark.anp-speech-bubble [data-task="4"],
.theme-dark.anp-speech-bubble [data-task="5"],
.theme-dark.anp-speech-bubble [data-task="6"],
.theme-dark.anp-speech-bubble [data-task="7"],
.theme-dark.anp-speech-bubble [data-task="8"],
.theme-dark.anp-speech-bubble [data-task="9"] {
  --text-normal: var(--background-primary);
  transition: background-color 0.2s, background 0.2s, border-radius 0.2s;
}
.theme-dark.anp-speech-bubble [data-task="0"]::selection,
.theme-dark.anp-speech-bubble [data-task="1"]::selection,
.theme-dark.anp-speech-bubble [data-task="2"]::selection,
.theme-dark.anp-speech-bubble [data-task="3"]::selection,
.theme-dark.anp-speech-bubble [data-task="4"]::selection,
.theme-dark.anp-speech-bubble [data-task="5"]::selection,
.theme-dark.anp-speech-bubble [data-task="6"]::selection,
.theme-dark.anp-speech-bubble [data-task="7"]::selection,
.theme-dark.anp-speech-bubble [data-task="8"]::selection,
.theme-dark.anp-speech-bubble [data-task="9"]::selection {
  background-color: rgba(var(--ctp-crust), 0.2);
  background: rgba(var(--ctp-crust), 0.2);
}

.anp-speech-bubble .is-live-preview [data-task="0"].cm-active,
.anp-speech-bubble .is-live-preview [data-task="1"].cm-active,
.anp-speech-bubble .is-live-preview [data-task="2"].cm-active,
.anp-speech-bubble .is-live-preview [data-task="3"].cm-active,
.anp-speech-bubble .is-live-preview [data-task="4"].cm-active,
.anp-speech-bubble .is-live-preview [data-task="5"].cm-active,
.anp-speech-bubble .is-live-preview [data-task="6"].cm-active,
.anp-speech-bubble .is-live-preview [data-task="7"].cm-active,
.anp-speech-bubble .is-live-preview [data-task="8"].cm-active,
.anp-speech-bubble .is-live-preview [data-task="9"].cm-active {
  border-radius: 0;
  --text-normal: rgb(var(--ctp-text));
  background: rgba(var(--ctp-surface1), 0.4);
}
.anp-speech-bubble .is-live-preview [data-task="0"]:not(.cm-active),
.anp-speech-bubble .is-live-preview [data-task="1"]:not(.cm-active),
.anp-speech-bubble .is-live-preview [data-task="2"]:not(.cm-active),
.anp-speech-bubble .is-live-preview [data-task="3"]:not(.cm-active),
.anp-speech-bubble .is-live-preview [data-task="4"]:not(.cm-active),
.anp-speech-bubble .is-live-preview [data-task="5"]:not(.cm-active),
.anp-speech-bubble .is-live-preview [data-task="6"]:not(.cm-active),
.anp-speech-bubble .is-live-preview [data-task="7"]:not(.cm-active),
.anp-speech-bubble .is-live-preview [data-task="8"]:not(.cm-active),
.anp-speech-bubble .is-live-preview [data-task="9"]:not(.cm-active) {
  max-width: max-content;
}

/* Dirty Speech Bubble Fix */
.anp-speech-bubble .HyperMD-task-line[data-task="0"], .anp-speech-bubble .HyperMD-task-line[data-task="1"], .anp-speech-bubble .HyperMD-task-line[data-task="2"], .anp-speech-bubble .HyperMD-task-line[data-task="3"], .anp-speech-bubble .HyperMD-task-line[data-task="4"], .anp-speech-bubble .HyperMD-task-line[data-task="5"], .anp-speech-bubble .HyperMD-task-line[data-task="6"], .anp-speech-bubble .HyperMD-task-line[data-task="7"], .anp-speech-bubble .HyperMD-task-line[data-task="8"], .anp-speech-bubble .HyperMD-task-line[data-task="9"] {
  text-indent: 0px !important;
}

.anp-speech-bubble [data-task="0"] {
  background-color: rgba(var(--ctp-red), var(--anp-speech-bubble-opacity));
}

.anp-speech-bubble [data-task="1"] {
  background-color: rgba(var(--ctp-peach), var(--anp-speech-bubble-opacity));
}

.anp-speech-bubble [data-task="2"] {
  background-color: rgba(var(--ctp-yellow), var(--anp-speech-bubble-opacity));
}

.anp-speech-bubble [data-task="3"] {
  background-color: rgba(var(--ctp-green), var(--anp-speech-bubble-opacity));
}

.anp-speech-bubble [data-task="4"] {
  background-color: rgba(var(--ctp-teal), var(--anp-speech-bubble-opacity));
}

.anp-speech-bubble [data-task="5"] {
  background-color: rgba(var(--ctp-sky), var(--anp-speech-bubble-opacity));
}

.anp-speech-bubble [data-task="6"] {
  background-color: rgba(var(--ctp-blue), var(--anp-speech-bubble-opacity));
}

.anp-speech-bubble [data-task="7"] {
  background-color: rgba(var(--ctp-mauve), var(--anp-speech-bubble-opacity));
}

.anp-speech-bubble [data-task="8"] {
  background: linear-gradient(to right, rgba(var(--ctp-red), var(--anp-speech-bubble-opacity)), rgba(var(--ctp-yellow), var(--anp-speech-bubble-opacity)));
}

.anp-speech-bubble [data-task="9"] {
  background: linear-gradient(to left, rgba(var(--ctp-lavender), var(--anp-speech-bubble-opacity)), rgba(var(--ctp-mauve), var(--anp-speech-bubble-opacity)));
}

/*------------FLOATING VAULT TITLE-----------*/
/*----------------------------------
This feature is inspired by Cecilia May's Primary theme: https://github.com/ceciliamay/obsidianmd-theme-primary
None of the original code was used and the feature was implemented from scratch.
----------------------------------*/
.anp-floating-header .nav-folder.mod-root > .nav-folder-title {
  background-color: var(--background-secondary);
  position: sticky;
  top: 0;
  z-index: var(--layer-popover);
  width: 100%;
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--radius-s);
  box-shadow: 0 3px 4px 0px rgba(0, 0, 0, 0.05);
}

.anp-floating-header.anp-card-layout .nav-folder.mod-root > .nav-folder-title {
  background-color: var(--card-foreground-color, var(--background-primary));
}

.anp-floating-header.anp-custom-vault-toggle .nav-folder.mod-root > .nav-folder-title {
  padding-top: 10px;
}

.anp-codeblock-numbers .HyperMD-codeblock-begin {
  counter-reset: line-numbers;
}

.anp-codeblock-numbers .HyperMD-codeblock.cm-line:not(.HyperMD-codeblock-begin):not(.HyperMD-codeblock-end) {
  padding-left: 5em;
  position: relative;
}

.anp-codeblock-numbers .HyperMD-codeblock.cm-line:not(.HyperMD-codeblock-begin):not(.HyperMD-codeblock-end)::before {
  align-items: flex-end;
  border-right: 1px solid var(--scrollbar-thumb-bg);
  color: var(--anp-code-text-color, var(--text-faint));
  content: counter(line-numbers);
  counter-increment: line-numbers;
  display: table-caption;
  font-size: 0.8em;
  height: 100%;
  justify-content: flex-end;
  left: 0;
  padding-right: 0.5em;
  padding-top: 1px;
  position: absolute;
  text-align: right;
  width: 3em;
}

.anp-codeblock-numbers .HyperMD-codeblock.cm-line.cm-active:not(.HyperMD-codeblock-begin):not(.HyperMD-codeblock-end)::before {
  color: var(--color-accent);
}

.anp-codeblock-numbers .HyperMD-codeblock .cm-foldPlaceholder::before {
  display: none;
}

.anp-file-icons .nav-file .nav-file-title[data-path]::before {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z'/%3E%3Cpath d='M14 2v6h6'/%3E%3C/svg%3E%0A");
  -webkit-mask-repeat: no-repeat;
  background-color: currentColor;
  content: "";
  display: inline-flex;
  flex-shrink: 0;
  height: 16px;
  width: 16px;
  margin-right: 5px;
  margin-left: var(--anp-file-icon-align, 0px);
}

.anp-file-icons .nav-file .nav-file-title[data-path$=".md"]::before {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z'/%3E%3Cpath d='M14 2v6h6m-4 5H8m8 4H8m2-8H8'/%3E%3C/svg%3E%0A");
  -webkit-mask-repeat: no-repeat;
}

.anp-file-icons .nav-file .nav-file-title[data-path$=".canvas"]::before {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 3h7v9H3zm11 0h7v5h-7zm0 9h7v9h-7zM3 16h7v5H3z'/%3E%3C/svg%3E");
  -webkit-mask-repeat: no-repeat;
}

.anp-file-icons .nav-file .nav-file-title[data-path$=".svg"]::before,
.anp-file-icons .nav-file .nav-file-title[data-path$=".bmp"]::before,
.anp-file-icons .nav-file .nav-file-title[data-path$=".jpg"]::before,
.anp-file-icons .nav-file .nav-file-title[data-path$=".gif"]::before,
.anp-file-icons .nav-file .nav-file-title[data-path$=".webp"]::before,
.anp-file-icons .nav-file .nav-file-title[data-path$=".jpeg"]::before,
.anp-file-icons .nav-file .nav-file-title[data-path$=".png"]::before {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z'/%3E%3Cpath d='M14 2v6h6'/%3E%3Ccircle cx='10' cy='13' r='2'/%3E%3Cpath d='m20 17-1.09-1.09a2 2 0 0 0-2.82 0L10 22'/%3E%3C/svg%3E%0A");
  -webkit-mask-repeat: no-repeat;
}

.anp-file-icons .nav-file .nav-file-title[data-path$=".mp3"]::before,
.anp-file-icons .nav-file .nav-file-title[data-path$=".wav"]::before,
.anp-file-icons .nav-file .nav-file-title[data-path$=".m4a"]::before,
.anp-file-icons .nav-file .nav-file-title[data-path$=".ogg"]::before,
.anp-file-icons .nav-file .nav-file-title[data-path$=".flac"]::before,
.anp-file-icons .nav-file .nav-file-title[data-path$=".3gp"]::before {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M9 18V5l12-2v13M9 9l12-2'/%3E%3Ccircle cx='6' cy='18' r='3'/%3E%3Ccircle cx='18' cy='16' r='3'/%3E%3C/svg%3E");
  -webkit-mask-repeat: no-repeat;
}

.anp-file-icons .nav-file .nav-file-title[data-path$=".webm"]::before {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'/%3E%3Cpath d='M10 8l6 4-6 4V8z'/%3E%3C/svg%3E");
  -webkit-mask-repeat: no-repeat;
}

.anp-file-icons .nav-file .nav-file-title[data-path$=".mp4"]::before,
.anp-file-icons .nav-file .nav-file-title[data-path$=".ogv"]::before,
.anp-file-icons .nav-file .nav-file-title[data-path$=".mov"]::before,
.anp-file-icons .nav-file .nav-file-title[data-path$=".mkv"]::before {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M4 11v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8H4Z'/%3E%3Cpath d='m4 11-.88-2.87a2 2 0 0 1 1.33-2.5l11.48-3.5a2 2 0 0 1 2.5 1.32l.87 2.87L4 11.01Z'/%3E%3Cpath d='M6.6 4.99l3.38 4.2m1.88-5.81l3.38 4.2'/%3E%3C/svg%3E");
  -webkit-mask-repeat: no-repeat;
}

.anp-file-icons .nav-folder.mod-root > .nav-folder-children > .nav-file .nav-file-title {
  padding-left: var(--size-4-2);
}

.anp-background-image-toggle:not(.anp-colorful-frame) {
  --card-background-color: transparent;
  --card-foreground-color: rgba(var(--ctp-base), var(--anp-custom-bg-card-fg-opacity, 0.4));
  --tab-inactive-color: rgba(var(--ctp-mantle), var(--anp-custom-bg-card-fg-opacity, 0.4));
  --code-background: var(--card-foreground-color);
}
.anp-background-image-toggle:not(.anp-colorful-frame) .app-container, .anp-background-image-toggle:not(.anp-colorful-frame).anp-bg-fix .app-container {
  background: var(--anp-background-image, url("https://i.redd.it/m23bwh4n0x151.png"));
  background-size: cover;
  background-color: transparent;
}
.anp-background-image-toggle:not(.anp-colorful-frame) .horizontal-main-container, .anp-background-image-toggle:not(.anp-colorful-frame).anp-bg-fix .horizontal-main-container {
  backdrop-filter: brightness(var(--anp-custom-bg-brightness, 0.5)) blur(var(--anp-custom-bg-blur, 5px));
  background-color: transparent;
}
.anp-background-image-toggle:not(.anp-colorful-frame) .workspace {
  background: transparent;
}
.anp-background-image-toggle:not(.anp-colorful-frame).anp-card-layout .workspace-split.mod-root .view-content {
  background: transparent;
}
.anp-background-image-toggle:not(.anp-colorful-frame).anp-alternate-tab-toggle .workspace-tabs:not(.mod-stacked), .anp-background-image-toggle:not(.anp-colorful-frame).anp-safari-tab-toggle .workspace-tabs:not(.mod-stacked) {
  --tab-background: var(--tab-inactive-color);
  --tab-background-active: var(--card-foreground-color);
  --tab-background-inactive: var(--tab-inactive-color);
}
.anp-background-image-toggle:not(.anp-colorful-frame).anp-floating-header .nav-folder.mod-root > .nav-folder-title {
  background-color: var(--tab-inactive-color);
}
.anp-background-image-toggle:not(.anp-colorful-frame).anp-canvas-dark-bg .canvas-wrapper,
.anp-background-image-toggle:not(.anp-colorful-frame) .canvas-wrapper,
.anp-background-image-toggle:not(.anp-colorful-frame) .mod-macos.is-hidden-frameless:not(.is-popout-window) .sidebar-toggle-button.mod-right, .anp-background-image-toggle:not(.anp-colorful-frame):not(.anp-card-layout).mod-macos.is-hidden-frameless:not(.is-popout-window) .sidebar-toggle-button.mod-right, .anp-background-image-toggle:not(.anp-colorful-frame).anp-card-layout .workspace-ribbon.mod-left:before,
.anp-background-image-toggle:not(.anp-colorful-frame) .anp-card-layout .workspace-tab-header-container,
.anp-background-image-toggle:not(.anp-colorful-frame) .workspace-split.mod-root {
  background-color: transparent;
}
.anp-background-image-toggle:not(.anp-colorful-frame):not(.anp-card-layout, .anp-border-layout) .workspace-tab-header-container, .anp-background-image-toggle:not(.anp-colorful-frame):not(.anp-card-layout) .workspace-tabs .workspace-leaf, .anp-background-image-toggle:not(.anp-colorful-frame):not(.anp-card-layout) .workspace-split.mod-root .view-content, .anp-background-image-toggle:not(.anp-colorful-frame):not(.anp-card-layout, .anp-border-layout) .workspace-ribbon, .anp-background-image-toggle:not(.anp-colorful-frame):not(.anp-card-layout, .anp-border-layout) .workspace-ribbon.mod-left:before, .anp-background-image-toggle:not(.anp-colorful-frame):not(.anp-card-layout) .view-header, .anp-background-image-toggle:not(.anp-colorful-frame).is-focused:not(.anp-card-layout) .workspace-leaf.mod-active .view-header,
.anp-background-image-toggle:not(.anp-colorful-frame) .kanban-plugin__lane,
.anp-background-image-toggle:not(.anp-colorful-frame) .kanban-plugin__item-content-wrapper {
  background-color: var(--card-foreground-color);
}
.anp-background-image-toggle:not(.anp-colorful-frame) .kanban-plugin__item-title-wrapper, .anp-background-image-toggle:not(.anp-colorful-frame).is-focused.anp-card-layout .workspace-leaf.mod-active .view-header, .anp-background-image-toggle:not(.anp-colorful-frame).anp-card-layout .workspace-ribbon {
  background: transparent;
}
.anp-background-image-toggle:not(.anp-colorful-frame) .workspace-fake-target-overlay:not(.is-in-sidebar) .workspace-tabs .workspace-leaf,
.anp-background-image-toggle:not(.anp-colorful-frame) .mod-root .workspace-tabs .workspace-leaf, .anp-background-image-toggle:not(.anp-colorful-frame).anp-card-layout .view-header,
.anp-background-image-toggle:not(.anp-colorful-frame) .is-focused .workspace-leaf.mod-active .view-header {
  background-color: transparent;
}
.anp-background-image-toggle:not(.anp-colorful-frame) .view-header-title-container:not(.mod-at-end):after {
  display: none;
}
.anp-background-image-toggle:not(.anp-colorful-frame) .kanban-plugin .kanban-plugin__lane button:hover {
  background-color: var(--card-foreground-color);
}
.anp-background-image-toggle:not(.anp-colorful-frame).anp-border-layout .workspace-tab-header-container,
.anp-background-image-toggle:not(.anp-colorful-frame).anp-border-layout .workspace-ribbon.mod-left:before {
  background: transparent;
}
.anp-background-image-toggle:not(.anp-colorful-frame):not(.anp-border-layout, .anp-card-layout).anp-fixed-status-bar .status-bar {
  background: var(--card-foreground-color);
  backdrop-filter: brightness(var(--anp-custom-bg-brightness, 0.5)) blur(var(--anp-custom-bg-blur, 5px));
}
.anp-background-image-toggle:not(.anp-colorful-frame).anp-border-layout.anp-fixed-status-bar .status-bar, .anp-background-image-toggle:not(.anp-colorful-frame).anp-card-layout.anp-fixed-status-bar .status-bar {
  background: transparent;
  backdrop-filter: brightness(var(--anp-custom-bg-brightness, 0.5)) blur(var(--anp-custom-bg-blur, 5px));
}

/*--------------DISABLING IN-FRONTMATTER---------------*/
.heading-normal-toggle {
  --h1-color: var(--text-normal) !important;
  --h2-color: var(--text-normal) !important;
  --h3-color: var(--text-normal) !important;
  --h4-color: var(--text-normal) !important;
  --h5-color: var(--text-normal) !important;
  --h6-color: var(--text-normal) !important;
}

.decorations-normal-toggle,
.decorations-normal-toggle.markdown-rendered {
  --bold-color: var(--text-normal) !important;
  --italic-color: var(--text-normal) !important;
}

.decorations-normal-toggle.markdown-rendered .table-view-table,
.decorations-normal-toggle.is-live-preview .table-view-table {
  --bold-color: var(--text-muted) !important;
  --italic-color: var(--text-muted) !important;
}

/*------------RAINBOW FOLDERS-------------*/
/* Rainbow colors */
.nav-folder-children > .nav-folder:nth-child(11n+2),
[data-type=bookmarks] .tree-item:nth-child(11n+2) {
  --rainbow-folder-color: var(--ctp-red);
}
.nav-folder-children > .nav-folder:nth-child(11n+3),
[data-type=bookmarks] .tree-item:nth-child(11n+3) {
  --rainbow-folder-color: var(--ctp-maroon);
}
.nav-folder-children > .nav-folder:nth-child(11n+4),
[data-type=bookmarks] .tree-item:nth-child(11n+4) {
  --rainbow-folder-color: var(--ctp-peach);
}
.nav-folder-children > .nav-folder:nth-child(11n+5),
[data-type=bookmarks] .tree-item:nth-child(11n+5) {
  --rainbow-folder-color: var(--ctp-yellow);
}
.nav-folder-children > .nav-folder:nth-child(11n+6),
[data-type=bookmarks] .tree-item:nth-child(11n+6) {
  --rainbow-folder-color: var(--ctp-green);
}
.nav-folder-children > .nav-folder:nth-child(11n+7),
[data-type=bookmarks] .tree-item:nth-child(11n+7) {
  --rainbow-folder-color: var(--ctp-teal);
}
.nav-folder-children > .nav-folder:nth-child(11n+8),
[data-type=bookmarks] .tree-item:nth-child(11n+8) {
  --rainbow-folder-color: var(--ctp-sky);
}
.nav-folder-children > .nav-folder:nth-child(11n+9),
[data-type=bookmarks] .tree-item:nth-child(11n+9) {
  --rainbow-folder-color: var(--ctp-sapphire);
}
.nav-folder-children > .nav-folder:nth-child(11n+10),
[data-type=bookmarks] .tree-item:nth-child(11n+10) {
  --rainbow-folder-color: var(--ctp-blue);
}
.nav-folder-children > .nav-folder:nth-child(11n+11),
[data-type=bookmarks] .tree-item:nth-child(11n+11) {
  --rainbow-folder-color: var(--ctp-lavender);
}
.nav-folder-children > .nav-folder:nth-child(11n+12),
[data-type=bookmarks] .tree-item:nth-child(11n+12) {
  --rainbow-folder-color: var(--ctp-mauve);
}

.anp-rainbow-subfolder-color-toggle .nav-folder.mod-root .nav-folder.nav-folder .nav-folder,
.anp-rainbow-subfolder-color-toggle [data-type=bookmarks] .tree-item .tree-item {
  --rainbow-folder-color: inherit;
}

/*-Full rainbow theme-*/
.anp-full-rainbow-color-toggle .nav-folder.mod-root > .nav-folder-children > .nav-folder .nav-folder-title,
.anp-full-rainbow-color-toggle .nav-folder.mod-root > .nav-folder-children > .nav-folder .nav-file-title,
.anp-full-rainbow-color-toggle .tree-item-self .tree-item-icon {
  color: var(--anp-full-rainbow-text-inverted, var(--background-primary));
}
.anp-full-rainbow-color-toggle.anp-rainbow-file-toggle .nav-folder.mod-root > .nav-folder-children > .nav-file .nav-file-title {
  color: var(--anp-full-rainbow-text-inverted, var(--background-primary));
}
.anp-full-rainbow-color-toggle.anp-rainbow-file-toggle.theme-dark .nav-folder.mod-root > .nav-folder-children > .nav-file {
  background-color: rgba(var(--ctp-text), var(--anp-rainbow-folder-bg-opacity));
}
.anp-full-rainbow-color-toggle.anp-rainbow-file-toggle.theme-light .nav-folder.mod-root > .nav-folder-children > .nav-file {
  background-color: rgba(var(--ctp-overlay1), var(--anp-rainbow-folder-bg-opacity));
}
.anp-full-rainbow-color-toggle.anp-rainbow-file-toggle .nav-folder.mod-root > .nav-folder-children > .nav-file {
  border-radius: 5px;
  margin-bottom: 2px;
}
.anp-full-rainbow-color-toggle:not(.is-grabbing) .nav-folder.mod-root > .nav-folder-children > .nav-folder .nav-folder-title:hover {
  background-color: rgba(var(--ctp-base), 0.1);
}
.anp-full-rainbow-color-toggle:not(.is-grabbing) .nav-folder.mod-root > .nav-folder-children .nav-file .nav-file-title:hover {
  background-color: rgba(var(--ctp-base), 0.1);
}
.anp-full-rainbow-color-toggle:not(.is-grabbing) .nav-folder.mod-root > .nav-folder-children > .nav-folder .nav-file-title.is-active {
  border-color: rgba(var(--ctp-base), 0.2);
  background-color: rgba(var(--ctp-base), 0.2);
}
.anp-full-rainbow-color-toggle .nav-file {
  overflow-y: hidden;
}
.anp-full-rainbow-color-toggle .nav-file-title-content.is-being-renamed, .anp-full-rainbow-color-toggle .nav-folder-title-content.is-being-renamed {
  cursor: text;
  border-color: var(--interactive-accent);
  background-color: rgba(var(--ctp-crust), 0.2);
}
.anp-full-rainbow-color-toggle .nav-file-title-content.is-being-renamed::selection, .anp-full-rainbow-color-toggle .nav-folder-title-content.is-being-renamed::selection {
  background-color: hsla(var(--color-accent-hsl), 0.2);
}
.anp-full-rainbow-color-toggle .nav-file-title-content.is-being-renamed::selection {
  background-color: rgba(var(--ctp-accent), 0.2);
}
.anp-full-rainbow-color-toggle .nav-folder.mod-root .nav-folder > .nav-folder-children {
  padding: 0 5px 0 5px;
}
.anp-full-rainbow-color-toggle .nav-folder.mod-root > .nav-folder-children > .nav-folder {
  transition: background-color 0.4s;
  background-color: rgba(var(--rainbow-folder-color), var(--anp-rainbow-folder-bg-opacity));
  margin-bottom: 2px;
  border-radius: 5px;
}
.anp-full-rainbow-color-toggle .nav-folder.mod-root > .nav-folder-children > .nav-folder .nav-folder-children {
  border-color: rgba(var(--ctp-crust), 0.4);
}
.anp-full-rainbow-color-toggle .nav-folder.mod-root > .nav-folder-children > .nav-file.has-focus {
  background-color: rgba(var(--ctp-text), var(--anp-rainbow-folder-bg-opacity));
  border-left: none;
  border-color: rgb(var(--ctp-accent));
}
.anp-full-rainbow-color-toggle .nav-folder.mod-root > .nav-folder-children > .nav-file.has-focus > .nav-file-title, .anp-full-rainbow-color-toggle .nav-folder.mod-root > .nav-folder-children > .nav-file > .nav-file-title {
  margin-bottom: 0;
}
.anp-full-rainbow-color-toggle .workspace-leaf.mod-active .nav-folder.has-focus > .nav-folder-title:focus-within, .anp-full-rainbow-color-toggle .workspace-leaf.mod-active .nav-file.has-focus > .nav-file-title:focus-within, .anp-full-rainbow-color-toggle .workspace-leaf.mod-active .nav-folder.has-focus > .nav-folder-title, .anp-full-rainbow-color-toggle .workspace-leaf.mod-active .nav-file.has-focus > .nav-file-title {
  box-shadow: none;
}

.anp-full-rainbow-text-color-toggle-dark.theme-dark {
  --anp-full-rainbow-text-inverted: rgb(var(--ctp-text));
}

.anp-full-rainbow-text-color-toggle-light.theme-light {
  --anp-full-rainbow-text-inverted: rgb(var(--ctp-text));
}

/*-Simple rainbow theme-*/
.anp-simple-rainbow-color-toggle.anp-simple-rainbow-title-toggle .nav-folder.mod-root > .nav-folder-children > .nav-folder .nav-folder-title,
.anp-simple-rainbow-color-toggle.anp-simple-rainbow-title-toggle [data-type=bookmarks] > .view-content > div > .tree-item .tree-item-inner {
  transition: color 0.4s;
  color: rgba(var(--rainbow-folder-color), var(--anp-simple-rainbow-opacity, 1));
  --nav-item-background-hover: rgba(var(--rainbow-folder-color), 0.1);
  --nav-item-background-active: rgba(var(--rainbow-folder-color), 0.1) ;
}
.anp-simple-rainbow-color-toggle.anp-simple-rainbow-icon-toggle .nav-folder.mod-root > .nav-folder-children > .nav-folder .nav-folder-title:after, .anp-simple-rainbow-color-toggle.anp-simple-rainbow-icon-toggle [data-type=bookmarks] > .view-content > div > .tree-item .tree-item-inner:after {
  transition: color 0.4s;
  color: rgba(var(--rainbow-folder-color), var(--anp-simple-rainbow-opacity, 1));
  content: "⬤";
  position: relative;
  margin-left: 4px;
  top: 1px;
  opacity: 0.5;
}
.anp-simple-rainbow-color-toggle.anp-simple-rainbow-indentation-toggle .nav-folder.mod-root .nav-folder > .nav-folder-children, .anp-simple-rainbow-color-toggle.anp-simple-rainbow-indentation-toggle [data-type=bookmarks] > .view-content > div > .tree-item .tree-item-children {
  transition: color 0.4s;
  border-color: rgba(var(--rainbow-folder-color), 0.5);
}
.anp-simple-rainbow-color-toggle.anp-simple-rainbow-collapse-toggle .tree-item-self .tree-item-icon {
  --icon-color: rgba(var(--rainbow-folder-color), var(--anp-simple-rainbow-opacity, 1));
  --nav-collapse-icon-color: rgba(var(--rainbow-folder-color), var(--anp-simple-rainbow-opacity, 1));
  --nav-collapse-icon-color-collapsed: rgba(var(--rainbow-folder-color), var(--anp-simple-rainbow-opacity, 1));
}

.modal {
  --h1-font: var(--interface-font);
}

.style-settings-container:empty {
  display: none;
}

.style-settings-heading[data-level] {
  padding-top: var(--size-4-2);
  padding-bottom: var(--size-4-2);
}

.style-settings-heading[data-level="0"]:not(.is-collapsed) {
  margin-bottom: var(--size-4-2);
}

.style-settings-heading:not([data-level="0"]) {
  border-top: 1px solid rgba(var(--ctp-text), 0.2);
  border-bottom: none;
}

.style-settings-heading:not([data-level="0"], .is-collapsed) {
  margin-bottom: 0;
}

.style-settings-heading:not([data-level="0"]) + .style-settings-container {
  border: 1px solid rgba(var(--ctp-text), 0.2);
  border-radius: var(--radius-s);
  padding: var(--size-4-2);
  padding-left: var(--size-4-6);
  background-color: rgba(var(--ctp-crust), 0.3);
  overflow-x: hidden;
}

.style-settings-heading[data-level="0"]:not(.is-collapsed) + .style-settings-container {
  border-bottom: 1px solid var(--background-modifier-border);
}

.style-settings-heading[data-level="1"] {
  border-top-color: var(--background-modifier-border);
}

.style-settings-heading:not([data-level="0"]):nth-child(-n+2),
.style-settings-heading:not([data-level="0"], .is-collapsed) + .style-settings-container + .style-settings-heading,
.style-settings-container .setting-item + .setting-item-heading {
  border-top-width: 0;
}

.style-settings-heading:is([data-id=anuppuccin-theme-settings],
[data-id=anuppuccin-theme-settings-extended]) {
  border-color: hsla(var(--color-accent-hsl), 0.2);
}
.style-settings-heading:is([data-id=anuppuccin-theme-settings],
[data-id=anuppuccin-theme-settings-extended]):not(.is-collapsed) + .style-settings-container {
  border-color: hsla(var(--color-accent-hsl), 0.2);
}
.style-settings-heading:is([data-id=anuppuccin-theme-settings],
[data-id=anuppuccin-theme-settings-extended]) .setting-item-name {
  color: var(--color-accent);
}

.anuppuccin-accent-toggle .style-settings-heading:is([data-id=anuppuccin-theme-settings],
[data-id=anuppuccin-theme-settings-extended]) {
  border-color: rgba(var(--ctp-accent), 0.2);
}
.anuppuccin-accent-toggle .style-settings-heading:is([data-id=anuppuccin-theme-settings],
[data-id=anuppuccin-theme-settings-extended]):not(.is-collapsed) + .style-settings-container {
  border-color: rgba(var(--ctp-accent), 0.2);
}

.style-settings-container .style-settings-heading[data-id=anuppuccin-support] .setting-item-name {
  color: var(--color-accent) !important;
}

.style-settings-container .themed-color-wrapper {
  display: flex;
  gap: var(--size-4-2);
}

.style-settings-container .themed-color-wrapper > div + div {
  margin-top: 0;
}

.style-settings-container .setting-item:is([data-id=anuppuccin-extended-colorschemes-links],
[data-id=anp-snippet-minimal-cards-disclaimer],
[data-id=anp-background-image-light],
[data-id=anp-background-image-dark],
[data-id=anuppuccin-theme-donate],
[data-id=anuppuccin-theme-source]) .setting-item-description > div {
  display: none;
}

.style-settings-container .setting-item-control:has(input[type=text]) {
  flex-grow: 0;
  position: relative;
}
.style-settings-container .setting-item-control:has(input[type=text])::after {
  align-items: center;
  background-color: var(--background-modifier-hover);
  border-radius: var(--radius-s);
  display: flex;
  font-size: 10px;
  font-weight: var(--font-semibold);
  height: calc(var(--input-height) - 12px);
  justify-content: center;
  left: calc(100% - 68px);
  letter-spacing: 0.05em;
  line-height: var(--line-height-normal);
  padding: 0;
  position: absolute;
  text-transform: uppercase;
  top: 6px;
  width: 28px;
}
.is-mobile .style-settings-container .setting-item-control:has(input[type=text])::after {
  top: 4px;
}

.style-settings-container .setting-item:is([data-id=tag-border-width],
[data-id=callout-radius],
[data-id=file-line-width],
[data-id=file-margins],
[data-id=anp-card-radius],
[data-id=anp-card-layout-padding],
[data-id=anp-card-header-left-padding],
[data-id=anp-table-thickness],
[data-id=anp-alt-tab-custom-height],
[data-id=anp-depth-tab-gap],
[data-id=anp-safari-tab-radius],
[data-id=anp-safari-tab-gap],
[data-id=anp-safari-border-width],
[data-id=anp-stacked-header-width],
[data-id=anp-border-radius],
[data-id=anp-border-padding]) .setting-item-control::after {
  content: "px";
}

.style-settings-container .setting-item:is([data-id=h1-size],
[data-id=h2-size],
[data-id=h3-size],
[data-id=h4-size],
[data-id=h5-size],
[data-id=h6-size],
[data-id=list-indent],
[data-id=list-spacing]) .setting-item-control::after {
  content: "em";
}

.style-settings-container .setting-item:is([data-id=anp-preview-width-pct],
[data-id=anp-table-width-pct]) .setting-item-control::after {
  content: "%";
}

.setting-item[data-id*=anp-kanban-]:is([data-id$=spacing], [data-id$=radius]) .setting-item-description small::after {
  content: "px";
}

.setting-item[data-id=anp-colors-section-header] > .setting-item-info > .setting-item-name {
  border-bottom: 2px solid;
  border-image: linear-gradient(to right, rgb(var(--ctp-rosewater)) 7%, rgb(var(--ctp-flamingo)) 7%, rgb(var(--ctp-flamingo)) 14%, rgb(var(--ctp-mauve)) 14%, rgb(var(--ctp-mauve)) 21%, rgb(var(--ctp-pink)) 21%, rgb(var(--ctp-pink)) 28%, rgb(var(--ctp-red)) 28%, rgb(var(--ctp-red)) 35%, rgb(var(--ctp-maroon)) 35%, rgb(var(--ctp-maroon)) 42%, rgb(var(--ctp-peach)) 42%, rgb(var(--ctp-peach)) 49%, rgb(var(--ctp-yellow)) 49%, rgb(var(--ctp-yellow)) 56%, rgb(var(--ctp-green)) 56%, rgb(var(--ctp-green)) 63%, rgb(var(--ctp-teal)) 63%, rgb(var(--ctp-teal)) 70%, rgb(var(--ctp-sky)) 70%, rgb(var(--ctp-sky)) 77%, rgb(var(--ctp-sapphire)) 77%, rgb(var(--ctp-sapphire)) 85%, rgb(var(--ctp-blue)) 85%, rgb(var(--ctp-blue)) 92%, rgb(var(--ctp-lavender))) 5;
}

.setting-item:is([data-id^=anuppuccin-url-]) .setting-item-control {
  display: none;
}
.setting-item:is([data-id^=anuppuccin-url-]) .setting-item-name::before {
  display: inline-flex;
  font-family: system-ui, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  height: fit-content;
  padding-right: var(--size-4-2);
  width: var(--size-4-4);
}

.setting-item-heading > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  font-family: system-ui, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  height: fit-content;
  margin-left: var(--size-4-3);
  width: var(--size-4-4);
}

.setting-item[data-id=anuppuccin-url-donate] .setting-item-name::before {
  content: "☕";
}

.setting-item[data-id=anuppuccin-url-star-repo] .setting-item-name::before {
  content: "⭐";
}

.setting-item[data-id=anuppuccin-url-submit-issue] .setting-item-name::before {
  content: "🐞";
}

.setting-item[data-id=anuppuccin-url-minimal-cards-snippet] .setting-item-name::before {
  content: "⬇";
}

.setting-item:is([data-id*=anuppuccin-theme-settings]) > .setting-item-info > .setting-item-name {
  margin-left: -3px;
}
.setting-item:is([data-id*=anuppuccin-theme-settings]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator {
  margin-right: 4px;
}
.setting-item:is([data-id*=anuppuccin-theme-settings]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-size: contain;
  background-color: var(--color-accent);
  content: " ";
  display: inline-flex;
  flex-shrink: 0;
  height: calc(var(--font-ui-medium) + 3px);
  width: calc(var(--font-ui-medium) + 3px);
  margin-left: calc(-0.5 * var(--size-4-1));
  margin-right: calc(var(--size-4-2) - 3px);
  transition: transform 0.1s;
  vertical-align: text-top;
  margin-right: 0;
}
.setting-item:is([data-id*=anuppuccin-theme-settings]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator > svg {
  display: none;
}

.setting-item:is([data-id*=anuppuccin-theme-settings-extended]) > .setting-item-info > .setting-item-name {
  margin-left: -3px;
}
.setting-item:is([data-id*=anuppuccin-theme-settings-extended]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator {
  margin-right: 4px;
}
.setting-item:is([data-id*=anuppuccin-theme-settings-extended]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-size: contain;
  background-color: var(--color-accent);
  content: " ";
  display: inline-flex;
  flex-shrink: 0;
  height: calc(var(--font-ui-medium) + 3px);
  width: calc(var(--font-ui-medium) + 3px);
  margin-left: calc(-0.5 * var(--size-4-1));
  margin-right: calc(var(--size-4-2) - 3px);
  transition: transform 0.1s;
  vertical-align: text-top;
  margin-right: 0;
}
.setting-item:is([data-id*=anuppuccin-theme-settings-extended]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator > svg {
  display: none;
}

.setting-item:is([data-id*=anp-colors-section-header]) > .setting-item-info > .setting-item-name {
  margin-left: -3px;
}
.setting-item:is([data-id*=anp-colors-section-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator {
  margin-right: 4px;
}
.setting-item:is([data-id*=anp-colors-section-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-size: contain;
  background-color: var(--color-accent);
  content: " ";
  display: inline-flex;
  flex-shrink: 0;
  height: calc(var(--font-ui-medium) + 3px);
  width: calc(var(--font-ui-medium) + 3px);
  margin-left: calc(-0.5 * var(--size-4-1));
  margin-right: calc(var(--size-4-2) - 3px);
  transition: transform 0.1s;
  vertical-align: text-top;
  margin-right: 0;
}
.setting-item:is([data-id*=anp-colors-section-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator > svg {
  display: none;
}

.setting-item:is([data-id*=anp-editor-header]) > .setting-item-info > .setting-item-name {
  margin-left: -3px;
}
.setting-item:is([data-id*=anp-editor-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator {
  margin-right: 4px;
}
.setting-item:is([data-id*=anp-editor-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-size: contain;
  background-color: var(--color-accent);
  content: " ";
  display: inline-flex;
  flex-shrink: 0;
  height: calc(var(--font-ui-medium) + 3px);
  width: calc(var(--font-ui-medium) + 3px);
  margin-left: calc(-0.5 * var(--size-4-1));
  margin-right: calc(var(--size-4-2) - 3px);
  transition: transform 0.1s;
  vertical-align: text-top;
  margin-right: 0;
}
.setting-item:is([data-id*=anp-editor-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator > svg {
  display: none;
}

.setting-item:is([data-id*=anp-preview-header]) > .setting-item-info > .setting-item-name {
  margin-left: -3px;
}
.setting-item:is([data-id*=anp-preview-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator {
  margin-right: 4px;
}
.setting-item:is([data-id*=anp-preview-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-size: contain;
  background-color: var(--color-accent);
  content: " ";
  display: inline-flex;
  flex-shrink: 0;
  height: calc(var(--font-ui-medium) + 3px);
  width: calc(var(--font-ui-medium) + 3px);
  margin-left: calc(-0.5 * var(--size-4-1));
  margin-right: calc(var(--size-4-2) - 3px);
  transition: transform 0.1s;
  vertical-align: text-top;
  margin-right: 0;
}
.setting-item:is([data-id*=anp-preview-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator > svg {
  display: none;
}

.setting-item:is([data-id*=anp-plugin-header]) > .setting-item-info > .setting-item-name {
  margin-left: -3px;
}
.setting-item:is([data-id*=anp-plugin-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator {
  margin-right: 4px;
}
.setting-item:is([data-id*=anp-plugin-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-size: contain;
  background-color: var(--color-accent);
  content: " ";
  display: inline-flex;
  flex-shrink: 0;
  height: calc(var(--font-ui-medium) + 3px);
  width: calc(var(--font-ui-medium) + 3px);
  margin-left: calc(-0.5 * var(--size-4-1));
  margin-right: calc(var(--size-4-2) - 3px);
  transition: transform 0.1s;
  vertical-align: text-top;
  margin-right: 0;
}
.setting-item:is([data-id*=anp-plugin-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator > svg {
  display: none;
}

.setting-item:is([data-id*=anp-misc-element-header]) > .setting-item-info > .setting-item-name {
  margin-left: -3px;
}
.setting-item:is([data-id*=anp-misc-element-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator {
  margin-right: 4px;
}
.setting-item:is([data-id*=anp-misc-element-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-size: contain;
  background-color: var(--color-accent);
  content: " ";
  display: inline-flex;
  flex-shrink: 0;
  height: calc(var(--font-ui-medium) + 3px);
  width: calc(var(--font-ui-medium) + 3px);
  margin-left: calc(-0.5 * var(--size-4-1));
  margin-right: calc(var(--size-4-2) - 3px);
  transition: transform 0.1s;
  vertical-align: text-top;
  margin-right: 0;
}
.setting-item:is([data-id*=anp-misc-element-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator > svg {
  display: none;
}

.setting-item:is([data-id*=anp-show-hide-elements-header]) > .setting-item-info > .setting-item-name {
  margin-left: -3px;
}
.setting-item:is([data-id*=anp-show-hide-elements-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator {
  margin-right: 4px;
}
.setting-item:is([data-id*=anp-show-hide-elements-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-size: contain;
  background-color: var(--color-accent);
  content: " ";
  display: inline-flex;
  flex-shrink: 0;
  height: calc(var(--font-ui-medium) + 3px);
  width: calc(var(--font-ui-medium) + 3px);
  margin-left: calc(-0.5 * var(--size-4-1));
  margin-right: calc(var(--size-4-2) - 3px);
  transition: transform 0.1s;
  vertical-align: text-top;
  margin-right: 0;
}
.setting-item:is([data-id*=anp-show-hide-elements-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator > svg {
  display: none;
}

.setting-item:is([data-id*=anp-typography-header]) > .setting-item-info > .setting-item-name {
  margin-left: -3px;
}
.setting-item:is([data-id*=anp-typography-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator {
  margin-right: 4px;
}
.setting-item:is([data-id*=anp-typography-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-size: contain;
  background-color: var(--color-accent);
  content: " ";
  display: inline-flex;
  flex-shrink: 0;
  height: calc(var(--font-ui-medium) + 3px);
  width: calc(var(--font-ui-medium) + 3px);
  margin-left: calc(-0.5 * var(--size-4-1));
  margin-right: calc(var(--size-4-2) - 3px);
  transition: transform 0.1s;
  vertical-align: text-top;
  margin-right: 0;
}
.setting-item:is([data-id*=anp-typography-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator > svg {
  display: none;
}

.setting-item:is([data-id*=anp-workspace-header]) > .setting-item-info > .setting-item-name {
  margin-left: -3px;
}
.setting-item:is([data-id*=anp-workspace-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator {
  margin-right: 4px;
}
.setting-item:is([data-id*=anp-workspace-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-size: contain;
  background-color: var(--color-accent);
  content: " ";
  display: inline-flex;
  flex-shrink: 0;
  height: calc(var(--font-ui-medium) + 3px);
  width: calc(var(--font-ui-medium) + 3px);
  margin-left: calc(-0.5 * var(--size-4-1));
  margin-right: calc(var(--size-4-2) - 3px);
  transition: transform 0.1s;
  vertical-align: text-top;
  margin-right: 0;
}
.setting-item:is([data-id*=anp-workspace-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator > svg {
  display: none;
}

.setting-item:is([data-id*=anuppuccin-support]) > .setting-item-info > .setting-item-name {
  margin-left: -3px;
}
.setting-item:is([data-id*=anuppuccin-support]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator {
  margin-right: 4px;
}
.setting-item:is([data-id*=anuppuccin-support]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-size: contain;
  background-color: var(--color-accent);
  content: " ";
  display: inline-flex;
  flex-shrink: 0;
  height: calc(var(--font-ui-medium) + 3px);
  width: calc(var(--font-ui-medium) + 3px);
  margin-left: calc(-0.5 * var(--size-4-1));
  margin-right: calc(var(--size-4-2) - 3px);
  transition: transform 0.1s;
  vertical-align: text-top;
  margin-right: 0;
}
.setting-item:is([data-id*=anuppuccin-support]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator > svg {
  display: none;
}

.setting-item:is([data-id*=anp-custom-rainbow-colors]) > .setting-item-info > .setting-item-name {
  margin-left: -3px;
}
.setting-item:is([data-id*=anp-custom-rainbow-colors]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator {
  margin-right: 4px;
}
.setting-item:is([data-id*=anp-custom-rainbow-colors]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-size: contain;
  background-color: var(--color-accent);
  content: " ";
  display: inline-flex;
  flex-shrink: 0;
  height: calc(var(--font-ui-medium) + 3px);
  width: calc(var(--font-ui-medium) + 3px);
  margin-left: calc(-0.5 * var(--size-4-1));
  margin-right: calc(var(--size-4-2) - 3px);
  transition: transform 0.1s;
  vertical-align: text-top;
  margin-right: 0;
}
.setting-item:is([data-id*=anp-custom-rainbow-colors]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator > svg {
  display: none;
}

.setting-item:is([data-id*=anp-custom-rainbow-colors]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  background: linear-gradient(to right, rgb(var(--ctp-rosewater)) 7%, rgb(var(--ctp-flamingo)) 7%, rgb(var(--ctp-flamingo)) 14%, rgb(var(--ctp-mauve)) 14%, rgb(var(--ctp-mauve)) 21%, rgb(var(--ctp-pink)) 21%, rgb(var(--ctp-pink)) 28%, rgb(var(--ctp-red)) 28%, rgb(var(--ctp-red)) 35%, rgb(var(--ctp-maroon)) 35%, rgb(var(--ctp-maroon)) 42%, rgb(var(--ctp-peach)) 42%, rgb(var(--ctp-peach)) 49%, rgb(var(--ctp-yellow)) 49%, rgb(var(--ctp-yellow)) 56%, rgb(var(--ctp-green)) 56%, rgb(var(--ctp-green)) 63%, rgb(var(--ctp-teal)) 63%, rgb(var(--ctp-teal)) 70%, rgb(var(--ctp-sky)) 70%, rgb(var(--ctp-sky)) 77%, rgb(var(--ctp-sapphire)) 77%, rgb(var(--ctp-sapphire)) 85%, rgb(var(--ctp-blue)) 85%, rgb(var(--ctp-blue)) 92%, rgb(var(--ctp-lavender)));
}

.setting-item:is([data-id=anuppuccin-theme-settings],
[data-id=anp-colors-section-header],
[data-id=anp-editor-header],
[data-id=anp-misc-element-header],
[data-id=anp-show-hide-elements-header],
[data-id=anp-typography-header],
[data-id=anp-plugin-header],
[data-id=anp-workspace-header],
[data-id=anuppuccin-support],
[data-id=anp-custom-rainbow-colors]):not(.is-collapsed) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  transform: rotate(90deg);
  transition: transform 0.1s;
}

.setting-item:is([data-id=anuppuccin-theme-settings-extended],
[data-id=anp-preview-header]):not(.is-collapsed) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  transform: rotate(-90deg);
  transition: transform 0.1s;
}

.setting-item:is([data-id=anuppuccin-theme-settings]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round' data-darkreader-inline-stroke='' style='--darkreader-inline-stroke:currentColor;'%3E%3Ccircle cx='13.5' cy='6.5' r='.5'%3E%3C/circle%3E%3Ccircle cx='17.5' cy='10.5' r='.5'%3E%3C/circle%3E%3Ccircle cx='8.5' cy='7.5' r='.5'%3E%3C/circle%3E%3Ccircle cx='6.5' cy='12.5' r='.5'%3E%3C/circle%3E%3Cpath d='M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z'%3E%3C/path%3E%3C/svg%3E");
}

.setting-item:is([data-id=anuppuccin-theme-settings-extended]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M18.37 2.63 14 7l-1.59-1.59a2 2 0 0 0-2.82 0L8 7l9 9 1.59-1.59a2 2 0 0 0 0-2.82L17 10l4.37-4.37a2.12 2.12 0 1 0-3-3Z'%3E%3C/path%3E%3Cpath d='M9 8c-2 3-4 3.5-7 4l8 10c2-1 6-5 6-7'%3E%3C/path%3E%3Cpath d='M14.5 17.5 4.5 15'%3E%3C/path%3E%3C/svg%3E");
}

.setting-item:is([data-id=anp-colors-section-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='m9.06 11.9 8.07-8.06a2.85 2.85 0 1 1 4.03 4.03l-8.06 8.08'%3E%3C/path%3E%3Cpath d='M7.07 14.94c-1.66 0-3 1.35-3 3.02 0 1.33-2.5 1.52-2 2.02 1.08 1.1 2.49 2.02 4 2.02 2.2 0 4-1.8 4-4.04a3.01 3.01 0 0 0-3-3.02z'%3E%3C/path%3E%3C/svg%3E");
}

.setting-item:is([data-id=anp-editor-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M15.5 3H5a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h14a2 2 0 0 0 2-2V8.5L15.5 3Z'%3E%3C/path%3E%3Cpath d='M15 3v6h6'%3E%3C/path%3E%3C/svg%3E");
}

.setting-item:is([data-id=anp-preview-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7'%3E%3C/path%3E%3Cpath d='M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z'%3E%3C/path%3E%3C/svg%3E");
}

.setting-item:is([data-id=anp-plugin-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 22v-5'%3E%3C/path%3E%3Cpath d='M9 7V2'%3E%3C/path%3E%3Cpath d='M15 7V2'%3E%3C/path%3E%3Cpath d='M6 13V8h12v5a4 4 0 0 1-4 4h-4a4 4 0 0 1-4-4Z'%3E%3C/path%3E%3C/svg%3E");
}

.setting-item:is([data-id=anp-misc-element-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='9' y1='18' x2='15' y2='18'%3E%3C/line%3E%3Cline x1='10' y1='22' x2='14' y2='22'%3E%3C/line%3E%3Cpath d='M15.09 14c.18-.98.65-1.74 1.41-2.5A4.65 4.65 0 0 0 18 8 6 6 0 0 0 6 8c0 1 .23 2.23 1.5 3.5A4.61 4.61 0 0 1 8.91 14'%3E%3C/path%3E%3C/svg%3E");
}

.setting-item:is([data-id=anp-show-hide-elements-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72Z'%3E%3C/path%3E%3Cpath d='m14 7 3 3'%3E%3C/path%3E%3Cpath d='M5 6v4'%3E%3C/path%3E%3Cpath d='M19 14v4'%3E%3C/path%3E%3Cpath d='M10 2v2'%3E%3C/path%3E%3Cpath d='M7 8H3'%3E%3C/path%3E%3Cpath d='M21 16h-4'%3E%3C/path%3E%3Cpath d='M11 3H9'%3E%3C/path%3E%3C/svg%3E");
}

.setting-item:is([data-id=anp-typography-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='4 7 4 4 20 4 20 7'%3E%3C/polyline%3E%3Cline x1='9' y1='20' x2='15' y2='20'%3E%3C/line%3E%3Cline x1='12' y1='4' x2='12' y2='20'%3E%3C/line%3E%3C/svg%3E");
}

.setting-item:is([data-id=anp-workspace-header]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='2' y='4' width='20' height='5' rx='2'%3E%3C/rect%3E%3Cpath d='M4 9v9a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9'%3E%3C/path%3E%3Cpath d='M10 13h4'%3E%3C/path%3E%3C/svg%3E");
}

.setting-item:is([data-id=anuppuccin-support]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20.42 4.58a5.4 5.4 0 0 0-7.65 0l-.77.78-.77-.78a5.4 5.4 0 0 0-7.65 0C1.46 6.7 1.33 10.28 4 13l8 8 8-8c2.67-2.72 2.54-6.3.42-8.42z'%3E%3C/path%3E%3C/svg%3E");
}

.setting-item:is([data-id=anp-custom-rainbow-colors]) > .setting-item-info > .setting-item-name > .style-settings-collapse-indicator::after {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M14 19.9V16h3a2 2 0 0 0 2-2v-2H5v2c0 1.1.9 2 2 2h3v3.9a2 2 0 1 0 4 0Z'%3E%3C/path%3E%3Cpath d='M6 12V2h12v10'%3E%3C/path%3E%3Cpath d='M14 2v4'%3E%3C/path%3E%3Cpath d='M10 2v2'%3E%3C/path%3E%3C/svg%3E");
}

.excalibrain-searchinput,
.multiselect-wrapper {
  --background-modifier-form-field: rgb(var(--ctp-crust));
}

.multiselect-wrapper {
  -webkit-app-region: no-drag;
  background: var(--background-modifier-form-field);
  border: var(--input-border-width) solid var(--background-modifier-border);
  color: var(--text-normal);
  font-family: inherit;
  padding: var(--size-4-1) var(--size-4-2);
  font-size: var(--font-ui-small);
  border-radius: var(--input-radius);
  outline: none;
}

.multiselect-container .multiselect-wrapper .options-wrapper {
  background: var(--background-modifier-form-field);
  margin-top: -4px;
  border-radius: 0 0 var(--input-radius) var(--input-radius);
  border-top: none;
  width: calc(100% + 2px);
  margin-left: -1px;
}

.excalidraw-dirty {
  color: rgb(var(--ctp-red));
}

.excalibrain-button.on {
  background-color: rgb(var(--ctp-base));
  border-color: var(--color-accent);
  --input-shadow: inset 0 0 0 2px var(--color-accent);
  --input-shadow-hover: inset 0 0 0 2px var(--color-accent);
}

.excalibrain-button.off {
  background-color: rgb(var(--ctp-crust));
}

/*-Fix dataview table header size-*/
.table-view-table > thead > tr > th {
  font-size: medium;
  color: var(--text-muted);
}

.dataview.inline-field-standalone-value, .dataview.inline-field-key, .dataview.inline-field-value {
  padding-left: 0px;
  padding-right: 0px;
  font-family: inherit;
  background-color: transparent;
  color: var(--text-normal);
}

.dataview.inline-field-standalone-value, .dataview.inline-field-value {
  text-decoration: underline;
  text-decoration-line: underline;
  text-decoration-color: rgba(var(--ctp-yellow), 0.4);
}

.dataview.inline-field-key {
  font-weight: bold;
  color: var(--bold-color);
  padding-right: 0;
}
.dataview.inline-field-key::after {
  content: ":";
  height: inherit;
  width: fit-content;
}

.dataview.inline-field-value {
  padding-left: 8px;
}

/* WIP, not implemented due to volatile excalidraw updates
.theme-light .excalidraw.excalidraw-container:not(.theme--dark),
.theme-dark .excalidraw.excalidraw-container.theme--dark {
  --button-destructive-bg-color: #ffe3e3;
  --button-destructive-color: #c92a2a;
  --button-gray-1: #e9ecef;
  --button-gray-2: #ced4da;
  --button-gray-3: #adb5bd;
  --button-special-active-bg-color: #ebfbee;
  --dialog-border-color: var(--color-gray-20);
  --dropdown-icon: url("data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"292.4\" height=\"292.4\" viewBox=\"0 0 292 292\"><path d=\"M287 197L159 69c-4-3-8-5-13-5s-9 2-13 5L5 197c-3 4-5 8-5 13s2 9 5 13c4 4 8 5 13 5h256c5 0 9-1 13-5s5-8 5-13-1-9-5-13z\"/></svg>");
  --focus-highlight-color: #a5d8ff;
  --icon-fill-color: var(--color-gray-80);
  --icon-green-fill-color: #2b8a3e;
  --default-bg-color: #ffffff;
  --input-bg-color: #ffffff;
  --input-border-color: #ced4da;
  --input-hover-bg-color: #f1f3f5;
  --input-label-color: #495057;
  --island-bg-color: rgba(255, 255, 255, 0.96);
  --keybinding-color: var(--color-gray-40);
  --link-color: #1c7ed6;
  --overlay-bg-color: rgba(255, 255, 255, 0.88);
  --popup-bg-color: #ffffff;
  --popup-secondary-bg-color: #f1f3f5;
  --popup-text-color: #000000;
  --popup-text-inverted-color: #ffffff;
  --select-highlight-color: #339af0;
  --shadow-island: 0px 7px 14px rgba(0, 0, 0, 0.05), 0px 0px 3.12708px rgba(0, 0, 0, 0.0798), 0px 0px 0.931014px rgba(0, 0, 0, 0.1702);
  --button-hover-bg: var(--color-gray-10);
  --default-border-color: var(--color-gray-30);
  --default-button-size: 2rem;
  --default-icon-size: 1rem;
  --lg-button-size: 2.25rem;
  --lg-icon-size: 1rem;
  --editor-container-padding: 1rem;
  --scrollbar-thumb: var(--button-gray-2);
  --scrollbar-thumb-hover: var(--button-gray-3);
  --modal-shadow: 0px 100px 80px rgba(0, 0, 0, 0.07), 0px 41.7776px 33.4221px rgba(0, 0, 0, 0.0503198), 0px 22.3363px 17.869px rgba(0, 0, 0, 0.0417275), 0px 12.5216px 10.0172px rgba(0, 0, 0, 0.035), 0px 6.6501px 5.32008px rgba(0, 0, 0, 0.0282725), 0px 2.76726px 2.21381px rgba(0, 0, 0, 0.0196802);
  --avatar-border-color: var(--color-gray-20);
  --sidebar-shadow: 0px 100px 80px rgba(0, 0, 0, 0.07), 0px 41.7776px 33.4221px rgba(0, 0, 0, 0.0503198), 0px 22.3363px 17.869px rgba(0, 0, 0, 0.0417275), 0px 12.5216px 10.0172px rgba(0, 0, 0, 0.035), 0px 6.6501px 5.32008px rgba(0, 0, 0, 0.0282725), 0px 2.76726px 2.21381px rgba(0, 0, 0, 0.0196802);
  --sidebar-border-color: var(--color-gray-20);
  --sidebar-bg-color: #fff;
  --library-dropdown-shadow: 0px 15px 6px rgba(0, 0, 0, 0.01), 0px 8px 5px rgba(0, 0, 0, 0.05), 0px 4px 4px rgba(0, 0, 0, 0.09), 0px 1px 2px rgba(0, 0, 0, 0.1), 0px 0px 0px rgba(0, 0, 0, 0.1);
  --space-factor: 0.25rem;
  --text-primary-color: var(--color-gray-80);
  --color-selection: var(--color-accent);
  --color-icon-white: #ffffff;
  --color-primary: var(--color-accent) !important;
  --color-primary-darker: color-mix(in hsl, var(--color-accent), #000000, 70%) !important;
  --color-primary-darkest: color-mix(in hsl, var(--color-accent), #000000) !important;
  --color-primary-light: color-mix(in hsl, var(--color-accent), #FFFFFF) !important;
  --color-primary-light-darker: color-mix(in hsl, var(--color-accent), var(--color-primary)) !important;
  --color-gray-10: #f5f5f5;
  --color-gray-20: #ebebeb;
  --color-gray-30: #d6d6d6;
  --color-gray-40: #b8b8b8;
  --color-gray-50: #999999;
  --color-gray-60: #7a7a7a;
  --color-gray-70: #5c5c5c;
  --color-gray-80: #3d3d3d;
  --color-gray-85: #242424;
  --color-gray-90: #1e1e1e;
  --color-gray-100: #121212;
  --color-warning: #fceeca;
  --color-warning-dark: #f5c354;
  --color-warning-darker: #f3ab2c;
  --color-warning-darkest: #ec8b14;
  --color-text-warning: var(--text-primary-color);
  --color-danger: #db6965;
  --color-danger-dark: #db6965;
  --color-danger-darker: #d65550;
  --color-danger-darkest: #d1413c;
  --color-danger-text: black;
  --color-danger-background: #fff0f0;
  --color-danger-icon-background: #ffdad6;
  --color-danger-color: #700000;
  --color-danger-icon-color: #700000;
  --color-warning-background: var(--color-warning);
  --color-warning-icon-background: var(--color-warning-dark);
  --color-warning-color: var(--text-primary-color);
  --color-warning-icon-color: var(--text-primary-color);
  --color-muted: var(--color-gray-30);
  --color-muted-darker: var(--color-gray-60);
  --color-muted-darkest: var(--color-gray-100);
  --color-muted-background: var(--color-gray-80);
  --color-muted-background-darker: var(--color-gray-100);
  --color-promo: #e70078;
  --color-success: #268029;
  --color-success-lighter: #cafccc;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
}
*/
.obsidian-icon-folder-icon {
  margin-top: -5px;
}

.anp-simple-rainbow-color-toggle.anp-simple-rainbow-icon-folder-toggle .obsidian-icon-folder-icon {
  color: rgb(var(--rainbow-folder-color));
}

.obsidian-icon-folder-modal.prompt-results .suggestion-item {
  color: var(--text-normal);
}

.prompt .obsidian-icon-folder-subheadline {
  color: var(--text-muted);
}

.workspace-leaf-content[data-type=advanced-tables-toolbar] .nav-buttons-container {
  border-style: dashed;
  border-color: var(--background-modifier-border);
  border-width: 0px 0px 1px 0px;
  margin-top: 10px;
  gap: 10px;
}

#cMenuModalBar {
  border: 2px solid rgba(var(--ctp-crust), 0.2);
}
#cMenuModalBar .cMenuCommandItem {
  background-color: rgba(var(--ctp-mantle), 0.4);
}
#cMenuModalBar .cMenuCommandItem:hover {
  background-color: rgba(var(--ctp-mantle), 0.6);
}

#cMenuModalBar button.cMenuCommandItem:hover {
  background-color: rgba(var(--ctp-mantle), 0.6);
}

.status-bar-item.plugin-obsidian-discordrpc {
  width: 0px;
  height: 0px;
  text-overflow: clip;
  overflow: hidden;
  margin-right: 20px;
  padding: 0px;
}

.status-bar-item.plugin-obsidian-discordrpc:after {
  position: absolute;
  margin-top: 18px;
  content: " ";
  height: 18px;
  width: 20px;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 640 512'%3E%3Cpath d='M524.531,69.836a1.5,1.5,0,0,0-.764-.7A485.065,485.065,0,0,0,404.081,32.03a1.816,1.816,0,0,0-1.923.91,337.461,337.461,0,0,0-14.9,30.6,447.848,447.848,0,0,0-134.426,0,309.541,309.541,0,0,0-15.135-30.6,1.89,1.89,0,0,0-1.924-.91A483.689,483.689,0,0,0,116.085,69.137a1.712,1.712,0,0,0-.788.676C39.068,183.651,18.186,294.69,28.43,404.354a2.016,2.016,0,0,0,.765,1.375A487.666,487.666,0,0,0,176.02,479.918a1.9,1.9,0,0,0,2.063-.676A348.2,348.2,0,0,0,208.12,430.4a1.86,1.86,0,0,0-1.019-2.588,321.173,321.173,0,0,1-45.868-21.853,1.885,1.885,0,0,1-.185-3.126c3.082-2.309,6.166-4.711,9.109-7.137a1.819,1.819,0,0,1,1.9-.256c96.229,43.917,200.41,43.917,295.5,0a1.812,1.812,0,0,1,1.924.233c2.944,2.426,6.027,4.851,9.132,7.16a1.884,1.884,0,0,1-.162,3.126,301.407,301.407,0,0,1-45.89,21.83,1.875,1.875,0,0,0-1,2.611,391.055,391.055,0,0,0,30.014,48.815,1.864,1.864,0,0,0,2.063.7A486.048,486.048,0,0,0,610.7,405.729a1.882,1.882,0,0,0,.765-1.352C623.729,277.594,590.933,167.465,524.531,69.836ZM222.491,337.58c-28.972,0-52.844-26.587-52.844-59.239S193.056,219.1,222.491,219.1c29.665,0,53.306,26.82,52.843,59.239C275.334,310.993,251.924,337.58,222.491,337.58Zm195.38,0c-28.971,0-52.843-26.587-52.843-59.239S388.437,219.1,417.871,219.1c29.667,0,53.307,26.82,52.844,59.239C470.715,310.993,447.538,337.58,417.871,337.58Z'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 640 512'%3E%3Cpath d='M524.531,69.836a1.5,1.5,0,0,0-.764-.7A485.065,485.065,0,0,0,404.081,32.03a1.816,1.816,0,0,0-1.923.91,337.461,337.461,0,0,0-14.9,30.6,447.848,447.848,0,0,0-134.426,0,309.541,309.541,0,0,0-15.135-30.6,1.89,1.89,0,0,0-1.924-.91A483.689,483.689,0,0,0,116.085,69.137a1.712,1.712,0,0,0-.788.676C39.068,183.651,18.186,294.69,28.43,404.354a2.016,2.016,0,0,0,.765,1.375A487.666,487.666,0,0,0,176.02,479.918a1.9,1.9,0,0,0,2.063-.676A348.2,348.2,0,0,0,208.12,430.4a1.86,1.86,0,0,0-1.019-2.588,321.173,321.173,0,0,1-45.868-21.853,1.885,1.885,0,0,1-.185-3.126c3.082-2.309,6.166-4.711,9.109-7.137a1.819,1.819,0,0,1,1.9-.256c96.229,43.917,200.41,43.917,295.5,0a1.812,1.812,0,0,1,1.924.233c2.944,2.426,6.027,4.851,9.132,7.16a1.884,1.884,0,0,1-.162,3.126,301.407,301.407,0,0,1-45.89,21.83,1.875,1.875,0,0,0-1,2.611,391.055,391.055,0,0,0,30.014,48.815,1.864,1.864,0,0,0,2.063.7A486.048,486.048,0,0,0,610.7,405.729a1.882,1.882,0,0,0,.765-1.352C623.729,277.594,590.933,167.465,524.531,69.836ZM222.491,337.58c-28.972,0-52.844-26.587-52.844-59.239S193.056,219.1,222.491,219.1c29.665,0,53.306,26.82,52.843,59.239C275.334,310.993,251.924,337.58,222.491,337.58Zm195.38,0c-28.971,0-52.843-26.587-52.843-59.239S388.437,219.1,417.871,219.1c29.667,0,53.307,26.82,52.844,59.239C470.715,310.993,447.538,337.58,417.871,337.58Z'/%3E%3C/svg%3E");
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repea;
  background-color: var(--status-bar-text-color);
}
.status-bar-item.plugin-obsidian-discordrpc:hover:after {
  filter: brightness(60%);
}

#calendar-container h3 {
  font-family: var(--font-interface);
  font-weight: bold;
}

#calendar-container .right-nav .reset-button {
  margin: auto;
}

.calendar .day.svelte-q3wqg9 {
  transition: background-color 0.1s;
}
.calendar .day.svelte-q3wqg9:hover {
  background-color: rgb(var(--ctp-crust));
}

.cMenuToolbarDefaultAesthetic {
  margin: 5px 10px 0 10px;
  box-shadow: 0 3px 4px 0px rgba(0, 0, 0, 0.05);
  background-color: var(--background-primary);
}

#cMenuToolbarModalBar.top button.cMenuToolbarCommandItem:hover {
  background-color: var(--background-secondary);
}

#cMenuToolbarModalBar.top :is(.cMenuToolbarCommandItem, button[class^=cMenuToolbarCommandsubItem]):not(.cMenuToolbar-Divider-Line) {
  transition: all 0s linear;
}

div[class*=recent-files-] {
  --anp-file-label-align: 1;
}

.gemmy-tooltip.tooltip {
  display: block;
  --background-modifier-message: rgb(var(--ctp-yellow));
  color: rgb(var(--ctp-base));
  font-size: var(--font-ui-medium);
  border-radius: var(--size-4-1);
  padding: var(--size-4-2) var(--size-4-3);
}

/* Recolor Colors */
div[class*=mk-] [style*="--label-color:#eb3b5a"] {
  --label-color: rgb(var(--ctp-red)) !important;
}
div[class*=mk-] [style*="--label-color:#fa8231"] {
  --label-color: rgb(var(--ctp-peach)) !important;
}
div[class*=mk-] [style*="--label-color:#f7b731"] {
  --label-color: rgb(var(--ctp-yellow)) !important;
}
div[class*=mk-] [style*="--label-color:#0fb9b1"] {
  --label-color: rgb(var(--ctp-teal)) !important;
}
div[class*=mk-] [style*="--label-color:#2d98da"] {
  --label-color: rgb(var(--ctp-blue)) !important;
}
div[class*=mk-] [style*="--icon-color:#ffffff"] {
  --icon-color: rgb(var(--ctp-base)) !important;
}

/* Context card view */
.mk-cards-grid .mk-list-group > ul {
  justify-content: center;
}
.mk-cards-grid .mk-list-group > ul > .mk-list-item {
  box-shadow: none;
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--radius-m);
}
.mk-cards-grid .mk-list-group > ul > .mk-list-item > .mk-file-preview {
  margin-bottom: 0px;
}
.mk-cards-grid .mk-list-group > ul > .mk-list-item > .mk-list-content {
  border-top: 1px dashed var(--background-modifier-border);
}

/* Folder Header */
.mk-folder-header .inline-title {
  --inline-title-color: var(--text-normal);
  --inline-title-font: var(--font-interface);
  display: block;
}

.mk-list-container .mk-list-view .mk-list-group .mk-list-item .mk-list-content {
  border-bottom: thin dashed var(--divider-color);
}

/* FIlter Button */
.mk-filter-bar .mk-filter {
  -webkit-app-region: no-drag;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: var(--text-normal);
  font-size: var(--font-ui-small);
  border-radius: var(--button-radius);
  border: 0;
  height: var(--input-height);
  font-weight: var(--input-font-weight);
  cursor: var(--cursor);
  font-family: inherit;
  outline: none;
  user-select: none;
  white-space: nowrap;
  background-color: var(--interactive-normal);
  box-shadow: var(--input-shadow);
}
.mk-filter-bar .mk-filter > span, .mk-filter-bar .mk-filter > div {
  background-color: transparent;
  border-right: 1px solid var(--background-modifier-border);
}
.mk-filter-bar .mk-filter > span:hover, .mk-filter-bar .mk-filter > div:hover {
  background-color: var(--interactive-hover);
}
.mk-filter-bar .mk-filter > div {
  height: 100%;
  width: 100%;
  padding: 0 7px;
}

/* Recolor Colors */
.mk-tree-item:has(.mk-file-icon > button[style*="--label-color:#eb3b5a"]) {
  --label-color: rgb(var(--ctp-red)) !important;
}
.mk-tree-item:has(.mk-file-icon > button[style*="--label-color:#fa8231"]) {
  --label-color: rgb(var(--ctp-peach)) !important;
}
.mk-tree-item:has(.mk-file-icon > button[style*="--label-color:#f7b731"]) {
  --label-color: rgb(var(--ctp-yellow)) !important;
}
.mk-tree-item:has(.mk-file-icon > button[style*="--label-color:#0fb9b1"]) {
  --label-color: rgb(var(--ctp-teal)) !important;
}
.mk-tree-item:has(.mk-file-icon > button[style*="--label-color:#2d98da"]) {
  --label-color: rgb(var(--ctp-blue)) !important;
}
.mk-tree-item:has(.mk-file-icon > button[style*="--icon-color:#ffffff"]) {
  --icon-color: rgb(var(--ctp-base)) !important;
}

.mk-tree-text.nav-file-title-content {
  color: var(--label-color);
}

.mk-table th:hover {
  background-color: var(--background-modifier-hover);
}

/* Fix bottom padding of kanban board on mobile */
body.is-mobile .kanban-plugin__board > div {
  padding-bottom: 5rem;
}

/* Fix bottom padding of kanban board when status bar is visible */
body:not(.is-mobile):not(.anp-hide-status-bar) .kanban-plugin__board > div {
  padding-bottom: 2.5rem;
}

/* Kanban background */
.kanban-plugin__board > div {
  --kanban-dot-color: var(--canvas-dot-pattern);
  --kanban-dot-offset: 7px;
  --kanban-dot-spacing: 20px;
  --kanban-dot-size: 0.7px;
  background-image: radial-gradient(circle, var(--kanban-dot-color) var(--kanban-dot-size), transparent var(--kanban-dot-size));
  background-position: var(--kanban-dot-offset) var(--kanban-dot-offset);
  background-size: var(--kanban-dot-spacing) var(--kanban-dot-spacing);
  flex-grow: 1;
}

/* Kanban lanes */
.kanban-plugin__lane {
  background-color: rgba(var(--ctp-mantle), var(--anp-kanban-lane-opacity, 1));
  border-radius: var(--anp-kanban-lane-radius, 6px);
}
.kanban-plugin__lane-wrapper {
  margin-right: var(--anp-kanban-lane-spacing, 10px);
}
.kanban-plugin__lane-form-wrapper {
  border-color: var(--interactive-accent);
}
.kanban-plugin__lane-grip {
  color: var(--background-modifier-border);
}
.kanban-plugin__lane-header-wrapper {
  padding-right: 8px;
}
.kanban-plugin__lane-title p {
  font-weight: 500;
}
.kanban-plugin__lane-title-count {
  margin-right: 5px;
}
.kanban-plugin__lane-setting-wrapper > div:last-child {
  margin-bottom: 0;
}
.kanban-plugin__lane-items {
  margin: 0;
  padding: 8px;
}
.kanban-plugin__action-confirm-wrapper {
  margin: 8px 8px 0;
}

.kanban-plugin button.kanban-plugin__new-item-button {
  font-size: 0.875rem;
  gap: 0.25em;
  height: auto;
  line-height: var(--line-height-tight);
  padding: 7px 10px;
}

/* Kanban lane card counts */
.kanban-plugin .kanban-plugin__lane-title-count {
  background-color: var(--background-modifier-hover);
  border-radius: 2em;
  flex-shrink: 0;
  font-size: 12px;
  font-weight: 600;
  line-height: 20px;
  min-width: 20px;
  padding: 0 6px;
  text-align: center;
}

/* Kanban card placeholder */
.kanban-plugin .kanban-plugin__lane-items > .kanban-plugin__placeholder,
.kanban-plugin__drag-container .kanban-plugin__lane-items > .kanban-plugin__placeholder {
  border: 2px dashed rgba(var(--text-muted-rgb), 0.1);
  margin-bottom: 0;
  width: auto;
}
.kanban-plugin .kanban-plugin__lane-items > .kanban-plugin__placeholder:not(:only-child),
.kanban-plugin__drag-container .kanban-plugin__lane-items > .kanban-plugin__placeholder:not(:only-child) {
  border-width: 0;
}

.is-sorting .kanban-plugin__lane-items > .kanban-plugin__placeholder:only-child {
  border-color: var(--background-modifier-border);
}

/* Kanban cards */
.kanban-plugin__lane-items > div {
  margin-top: 0;
  margin-bottom: var(--anp-kanban-card-spacing, 8px);
}
.kanban-plugin__item:hover {
  border-color: var(--background-modifier-border-hover);
}
.kanban-plugin__item.is-complete .kanban-plugin__item-markdown {
  color: var(--text-muted);
  text-decoration-line: line-through;
  opacity: 0.75;
}
.kanban-plugin__item .markdown-preview-view .tag {
  --tag-border-width: 0;
  --tag-padding-y: 0;
  --tag-padding-x: 0;
  --tag-background: transparent;
  --tag-color: var(--link-color);
  --tag-size: reset;
}
.kanban-plugin__item-content-wrapper {
  background-color: rgba(var(--ctp-base), var(--anp-kanban-card-opacity, 1));
}
.kanban-plugin__item-title-wrapper {
  background-color: transparent;
  padding: 8px;
}
.kanban-plugin__item-title-wrapper div:first-child > a.clickable-icon {
  position: relative;
  left: -2px;
}
.kanban-plugin__item-title-wrapper div:last-child > a.clickable-icon {
  position: relative;
  right: -2px;
}

.kanban-plugin__lane-items {
  position: relative;
}

.kanban-plugin__item-wrapper:hover {
  z-index: 1;
}

/* Kanban card content */
.kanban-plugin,
.kanban-plugin__drag-container {
  --checkbox-size: 16px;
  --input-radius: var(--anp-kanban-card-radius, 6px);
}
.kanban-plugin code[class*=language-],
.kanban-plugin__drag-container code[class*=language-] {
  font-size: 0.875em;
  padding: 0.75em;
  white-space: pre;
}
.kanban-plugin button.copy-code-button,
.kanban-plugin__drag-container button.copy-code-button {
  display: none;
}
.kanban-plugin textarea,
.kanban-plugin__drag-container textarea {
  background-color: transparent;
}

.kanban-plugin__drag-container p, .kanban-plugin__drag-container code {
  overflow: hidden;
}

/* Kanban card meta and tags */
.kanban-plugin,
.kanban-plugin__drag-container {
  --tag-color: var(--text-faint);
}
.kanban-plugin .kanban-plugin__item-metadata,
.kanban-plugin__drag-container .kanban-plugin__item-metadata {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
}
.kanban-plugin .kanban-plugin__item-metadata:not(:empty),
.kanban-plugin__drag-container .kanban-plugin__item-metadata:not(:empty) {
  padding-top: 8px;
}
.kanban-plugin .kanban-plugin__item-metadata-date-wrapper,
.kanban-plugin__drag-container .kanban-plugin__item-metadata-date-wrapper {
  line-height: var(--line-height-tight);
}
.kanban-plugin .kanban-plugin__item-tags,
.kanban-plugin__drag-container .kanban-plugin__item-tags {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  gap: 4px;
}
.kanban-plugin .kanban-plugin__item-tags:not(:empty),
.kanban-plugin__drag-container .kanban-plugin__item-tags:not(:empty) {
  padding-top: 0;
}
.kanban-plugin .kanban-plugin__item-tag,
.kanban-plugin__drag-container .kanban-plugin__item-tag {
  margin: 0;
}

/* Kanban date button and date picker */
.kanban-plugin__item-metadata-date.is-button {
  position: relative;
  display: inline-block;
}
.kanban-plugin__item-metadata-date.is-button:hover::before {
  position: absolute;
  display: inline-flex;
  top: -3px;
  left: -5px;
  right: -5px;
  bottom: -3px;
  content: "";
  z-index: 0;
  background-color: var(--background-modifier-hover);
  border-radius: var(--clickable-icon-radius);
}

.kanban-plugin__date-picker {
  --background-primary-alt: var(--background-modifier-hover);
  --input-shadow: none;
  --input-shadow-hover: none;
}
.kanban-plugin__date-picker .flatpickr-current-month input:hover, .kanban-plugin__date-picker .flatpickr-current-month input:focus,
.kanban-plugin__date-picker .flatpickr-monthDropdown-months:hover,
.kanban-plugin__date-picker .flatpickr-monthDropdown-months:focus {
  background-color: var(--background-modifier-hover);
  box-shadow: none;
}
.kanban-plugin__date-picker .flatpickr-day:not(.today):hover {
  border-width: 0;
}

/* Kanban buttons */
.kanban-plugin button,
.kanban-plugin__drag-container button {
  box-shadow: none;
}
.kanban-plugin button:hover,
.kanban-plugin__drag-container button:hover {
  background-color: var(--background-modifier-hover);
  color: var(--text-normal);
  transition: background-color 0.1s;
}

/* Kanban Autocomplete Fix */
.kanban-plugin__autocomplete-item-active em {
  color: var(--text-on-accent);
}

/* Notion-Styled Kanban Fix */
body.theme-dark.theme-dark {
  --notion-kanban-card: rgb(var(--ctp-surface0));
  --notion-kanban-card-hover: rgb(var(--ctp-surface1));
}

.anp-kanban-hide-card-menus:not(.is-mobile) .kanban-plugin__item-title + .kanban-plugin__item-postfix-button-wrapper {
  display: none;
}

.setting-item[data-id=anp-kanban-hide-card-menus]:not(:has(.is-enabled)) + [data-id=anp-kanban-hide-archive-btn] {
  display: none;
}

.anp-kanban-hide-card-menus.anp-kanban-hide-archive-btn:not(.is-mobile) .kanban-plugin__item-title-wrapper {
  padding: 8px;
  position: relative;
}
.anp-kanban-hide-card-menus.anp-kanban-hide-archive-btn:not(.is-mobile) .kanban-plugin__item-prefix-button-wrapper {
  background-color: var(--background-primary);
  display: none;
  padding: 5px;
  position: absolute;
  right: -2px;
  top: 0;
  z-index: 1;
}
.anp-kanban-hide-card-menus.anp-kanban-hide-archive-btn:not(.is-mobile) .kanban-plugin__item:hover .kanban-plugin__item-prefix-button-wrapper {
  display: flex;
}
.anp-kanban-hide-card-menus.anp-kanban-hide-archive-btn.is-mobile .kanban-plugin__item-prefix-button-wrapper {
  display: none;
}

.anp-kanban-hide-card-border .kanban-plugin .kanban-plugin__item {
  border-width: 0;
}
.anp-kanban-hide-card-border .kanban-plugin .kanban-plugin__item:hover .kanban-plugin__item-content-wrapper {
  border-radius: var(--anp-kanban-card-radius, 6px);
  box-shadow: inset 0 0 0 1px var(--background-modifier-border-hover);
}
.anp-kanban-hide-card-border .kanban-plugin__drag-container > .kanban-plugin__item-wrapper .kanban-plugin__item {
  border-width: 0;
  box-shadow: var(--shadow-s);
}
.anp-kanban-hide-card-border .kanban-plugin__drag-container > .kanban-plugin__item-wrapper .kanban-plugin__item-content-wrapper {
  border-radius: var(--anp-kanban-card-radius, 6px);
  box-shadow: inset 0 0 0 1px var(--interactive-accent);
}

.anp-kanban-lanes .kanban-plugin__lane-wrapper {
  height: 100%;
}

.anp-kanban-lanes .kanban-plugin__scroll-container.kanban-plugin__vertical {
  flex-grow: 1;
}

.anp-kanban-hide-lane-border .kanban-plugin .kanban-plugin__lane,
.anp-kanban-hide-lane-border .kanban-plugin .kanban-plugin__lane-header-wrapper,
.anp-kanban-hide-lane-border .kanban-plugin .kanban-plugin__item-button-wrapper,
.anp-kanban-hide-lane-border .kanban-plugin .kanban-plugin__item-form {
  border-width: 0;
}
.anp-kanban-hide-lane-border .kanban-plugin .kanban-plugin__lane-items {
  padding-bottom: 0;
  padding-top: 0;
}
