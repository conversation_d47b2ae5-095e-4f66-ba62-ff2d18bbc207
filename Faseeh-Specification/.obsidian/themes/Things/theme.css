/*───────────────────────────────────────────────────────
THINGS
Version 2.1.18
Created by @colineckert

Readme:
https://github.com/colineckert/obsidian-things

Support my work:
https://www.buymeacoffee.com/colineckert
────────────────────────────────────────────────────── */

/* ─────────────────────────────────────────────────── */
/* Main Theme Styling */
/* ─────────────────────────────────────────────────── */

@charset "UTF-8";
body {
  /* Colors */
  --base-h: 212; /* Base hue */
  --base-s: 15%; /* Base saturation */
  --base-d: 13%; /* Base lightness Dark Mode  - 0 is black */
  --base-l: 97%; /* Base lightness Light Mode  - 100 is white */
  --accent-h: 215; /* Accent hue */
  --accent-s: 75%; /* Accent saturation */
  --accent-d: 70%; /* Accent lightness Dark Mode */
  --accent-l: 60%; /* Accent lightness Light Mode */

  --blue: #2e80f2;
  --pink: #ff82b2;
  --green: #3eb4bf;
  --yellow: #e5b567;
  --orange: #e87d3e;
  --red: #e83e3e;
  --purple: #9e86c8;

  --h1-color: var(--text-normal);
  --h2-color: var(--text-normal);
  --h3-color: var(--blue);
  --h4-color: var(--yellow);
  --h5-color: var(--red);
  --h6-color: var(--text-muted);

  --strong-color: var(--pink);
  --em-color: var(--pink);
  --quote-color: var(--green);

  --tag-background-color-l: #bde1d3;
  --tag-font-color-l: #1d694b;
  --tag-background-color-d: #1d694b;
  --tag-font-color-d: #ffffff;

  --highlight-background-color--normal: hsl(50deg 100% 50% / 15%) !important;
  --highlight-background-color-underline: hsl(50deg 100% 50% / 100%) !important;
  --highlight-background-color--active: hsl(50deg 100% 50% / 20%) !important;

  /* Font families */
  --font-text-theme: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    Inter, Ubuntu, sans-serif;
  --font-editor-theme: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    Inter, Ubuntu, sans-serif;
  --font-monospace-theme: 'JetBrains Mono', 'Fira Code', Menlo, SFMono-Regular,
    Consolas, 'Roboto Mono', monospace;
  --font-interface-theme: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    Inter, Ubuntu, sans-serif;
  --font-editor: var(--font-editor-override), var(--font-text-override),
    var(--font-editor-theme);

  --h1-size: 1.7rem;
  --h2-size: 1.5rem;
  --h3-size: 1.2rem;
  --h4-size: 1.1rem;
  --h5-size: 1rem;
  --h6-size: 0.9rem;

  /* Misc */
  --checkbox-radius: 30%;
  --link-external-decoration: underline;
  --link-decoration: underline;

  /* -------- */
  --radius-s: 4px;
  --radius-m: 8px;
  --radius-l: 10px;
  --radius-xl: 16px;

  --line-width: 40rem;
  --line-height: 1.5;
  --max-width: 90%;
  --max-col-width: 18em;
  --icon-muted: 0.5;
  --nested-padding: 1.1em;
  --folding-offset: 10px;

  --line-width-adaptive: var(--line-width);
  --line-width-wide: calc(var(--line-width) + 12.5%);
}

/* COLOR SCHEMES
────────────────────────────────────────────────────── */

.theme-light,
body.theme-light.is-mobile {
  color-scheme: light;
  --highlight-mix-blend-mode: darken;
  --mono-rgb-0: 255, 255, 255;
  --mono-rgb-100: 0, 0, 0;
  --color-red-rgb: 228, 55, 75;
  --color-red: #e4374b;
  --color-green-rgb: 12, 181, 79;
  --color-green: #0cb54f;
  --color-orange: #d96c00;
  --color-yellow: #bd8e37;
  --color-cyan: #2db7b5;
  --color-blue: #086ddd;
  --color-purple: #876be0;
  --color-pink: #c32b74;
  --color-base-00: #ffffff;
  --color-base-05: #fcfcfc;
  --color-base-10: #f6f7f8; /* code blocks */
  --color-base-20: #f6f7f8;
  --color-base-25: #f0f0f0;
  --color-base-30: #ebedf0; /* soften dividing lines */
  --color-base-35: #d4d4d4;
  --color-base-40: #bdbdbd;
  --color-base-50: #ababab;
  --color-base-60: #707070;
  --color-base-70: #5a5a5a;
  --color-base-100: #222222;
  --color-accent-hsl: var(--accent-h), var(--accent-s), var(--accent-l);
  --color-accent: hsl(var(--accent-h), var(--accent-s), var(--accent-l));
  --color-accent-1: hsl(
    var(--accent-h),
    var(--accent-s),
    calc(var(--accent-l) + 2.5%)
  );
  --color-accent-2: hsl(
    var(--accent-h),
    var(--accent-s),
    calc(var(--accent-l) + 5%)
  );
  --background-modifier-hover: #e2e5e9;
  --background-secondary-alt: var(--color-base-05);
  --background-modifier-box-shadow: rgba(0, 0, 0, 0.1);
  --background-modifier-cover: rgba(220, 220, 220, 0.4);
  --text-highlight-bg-l: rgba(255, 208, 0, 0.4);
  --text-highlight-bg: var(--text-highlight-bg-l);
  --text-highlight-bg-active: rgba(255, 128, 0, 0.4);
  --input-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.12),
    0 2px 3px 0 rgba(0, 0, 0, 0.05), 0 1px 1.5px 0 rgba(0, 0, 0, 0.03),
    0 1px 2px 0 rgba(0, 0, 0, 0.04), 0 0 0 0 transparent;
  --input-shadow-hover: inset 0 0 0 1px rgba(0, 0, 0, 0.17),
    0 2px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 1.5px 0 rgba(0, 0, 0, 0.03),
    0 1px 2px 0 rgba(0, 0, 0, 0.04), 0 0 0 0 transparent;
  --shadow-s: 0px 1px 2px rgba(0, 0, 0, 0.028),
    0px 3.4px 6.7px rgba(0, 0, 0, 0.042), 0px 15px 30px rgba(0, 0, 0, 0.07);
  --shadow-l: 0px 1.8px 7.3px rgba(0, 0, 0, 0.071),
    0px 6.3px 24.7px rgba(0, 0, 0, 0.112), 0px 30px 90px rgba(0, 0, 0, 0.2);

  --tag-background: var(--tag-background-color-l);
  --tag-color: var(--tag-font-color-l);

  /* --text-normal: hsl(var(--base-h), var(--base-s), calc(var(--base-l) - 80%)); */
  --text-muted: hsl(
    var(--base-h),
    calc(var(--base-s) - 5%),
    calc(var(--base-l) - 60%)
  );
  --text-faint: hsl(
    var(--base-h),
    calc(var(--base-s) - 5%),
    calc(var(--base-l) - 30%)
  );
  --text-formatted: hsl(
    var(--base-h),
    calc(var(--base-s) - 5%),
    calc(var(--base-l) - 35%)
  );
  --text-accent: hsl(var(--accent-h), var(--accent-s), var(--accent-l));
  --text-accent-hover: hsl(
    var(--accent-h),
    var(--accent-s),
    calc(var(--accent-l) - 10%)
  );

  --atom-gray-1: #383a42;
  --atom-gray-2: #383a42;
  --atom-red: #e75545;
  --atom-green: #4ea24c;
  --atom-blue: #3d74f6;
  --atom-purple: #a625a4;
  --atom-aqua: #0084bc;
  --atom-yellow: #e35649;
  --atom-orange: #986800;
}

.theme-dark,
body.theme-dark.is-mobile {
  color-scheme: dark;
  --highlight-mix-blend-mode: lighten;
  --mono-rgb-0: 0, 0, 0;
  --mono-rgb-100: 255, 255, 255;
  --color-red-rgb: 251, 70, 76;
  --color-red: #fb464c;
  --color-green-rgb: 68, 207, 110;
  --color-green: #44cf6e;
  --color-orange: #e9973f;
  --color-yellow: #e0de71;
  --color-cyan: #53dfdd;
  --color-blue: #027aff;
  --color-purple: #a882ff;
  --color-pink: #fa99cd;
  --color-base-00: #1c2127; /* main editor window */
  --color-base-10: #282c34; /* code blocks */
  --color-base-20: #181c20; /* top unselected unfocused nav */
  --color-base-25: #2c313c;
  --color-base-30: #35393e;
  --color-base-35: #3f3f3f;
  --color-base-40: #555;
  --color-base-50: #666; /* unfocused text */
  --color-base-60: #999;
  --color-base-70: #bababa;
  --color-base-100: #dadada;
  --color-accent-hsl: var(--accent-h), var(--accent-s), var(--accent-l);
  --color-accent: hsl(var(--accent-h), var(--accent-s), var(--accent-l));
  --color-accent-1: hsl(
    var(--accent-h),
    var(--accent-s),
    calc(var(--accent-l) - 3.8%)
  );
  --color-accent-2: hsl(
    var(--accent-h),
    var(--accent-s),
    calc(var(--accent-l) + 3.8%)
  );
  --titlebar-background-focused: var(--color-base-10);
  --background-modifier-form-field: var(--color-base-25);
  --background-secondary-alt: var(--color-base-30);
  --interactive-normal: var(--color-base-30);
  --interactive-hover: var(--color-base-35);
  --text-highlight-bg-d: rgba(255, 208, 0, 0.4);
  --text-highlight-bg: var(--text-highlight-bg-d);
  --text-highlight-bg-active: rgba(255, 128, 0, 0.4);
  --text-selection: hsla(var(--interactive-accent-hsl), 0.25);
  --input-shadow: inset 0 0.5px 0.5px 0.5px rgba(255, 255, 255, 0.09),
    0 2px 4px 0 rgba(0, 0, 0, 0.15), 0 1px 1.5px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.2), 0 0 0 0 transparent;
  --input-shadow-hover: inset 0 0.5px 1px 0.5px rgba(255, 255, 255, 0.16),
    0 2px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 1.5px 0 rgba(0, 0, 0, 0.2),
    0 1px 2px 0 rgba(0, 0, 0, 0.4), 0 0 0 0 transparent;
  --shadow-s: 0px 1px 2px rgba(0, 0, 0, 0.121),
    0px 3.4px 6.7px rgba(0, 0, 0, 0.179), 0px 15px 30px rgba(0, 0, 0, 0.3);
  --shadow-l: 0px 1.8px 7.3px rgba(0, 0, 0, 0.071),
    0px 6.3px 24.7px rgba(0, 0, 0, 0.112), 0px 30px 90px rgba(0, 0, 0, 0.2);

  --tag-background: var(--tag-background-color-d);
  --tag-color: var(--tag-font-color-d);

  --text-muted: hsl(var(--base-h), var(--base-s), calc(var(--base-d) + 65%));
  --text-faint: hsl(var(--base-h), var(--base-s), calc(var(--base-d) + 30%));
  --text-formatted: hsl(
    var(--base-h),
    var(--base-s),
    calc(var(--base-d) + 50%)
  );

  --text-accent: hsl(var(--accent-h), var(--accent-s), var(--accent-d));
  --text-accent-hover: hsl(
    var(--accent-h),
    var(--accent-s),
    calc(var(--accent-d) + 12%)
  );

  --atom-gray-1: #5c6370;
  --atom-gray-2: #abb2bf;
  --atom-red: #e06c75;
  --atom-orange: #d19a66;
  --atom-green: #98c379;
  --atom-aqua: #56b6c2;
  --atom-purple: #c678dd;
  --atom-blue: #61afef;
  --atom-yellow: #e5c07b;
}

body.is-mobile.theme-dark.mobile-black-background {
  --color-base-00: black;
}

/* Make exported PDFs render correctly */
@media print {
  .theme-dark {
    --highlight-mix-blend-mode: darken;
    --color-base-30: #ebedf0;
    --h1-color: var(--color-base-00);
  }
}

/* Header title */
.view-header-title,
.view-header-title-parent {
  text-overflow: ellipsis;
}
.view-header-title-container:not(.mod-at-end):after {
  display: none;
}
body:not(.is-mobile) .view-actions .view-action:last-child {
  margin-left: -1px;
}
.workspace-ribbon:not(.is-collapsed)
  ~ .mod-root
  .view-header:hover
  .view-actions,
.mod-right.is-collapsed ~ .mod-root .view-header:hover .view-actions,
.view-action.is-active:hover,
.workspace-ribbon.mod-left.is-collapsed
  ~ .mod-root
  .view-header:hover
  .view-actions,
.workspace-ribbon:not(.is-collapsed) ~ .mod-root .view-actions {
  opacity: 1;
  transition: opacity 0.25s ease-in-out;
}
.view-header:hover .view-header-title-container,
.workspace-tab-header-container:hover
  + .workspace-tab-container
  .view-header-title-container {
  opacity: 1;
  transition: opacity 0.1s ease-in-out;
}
.is-phone .view-header-title-container,
.view-header-title-container {
  opacity: 1;
}
body:not(.no-minimal-title-bar) .view-header-title-container {
  opacity: 0;
  transition: opacity 0.1s ease-in-out;
}
.view-header-title-container:focus-within {
  opacity: 1;
  transition: opacity 0.1s ease-in-out;
}

/* H2 styling */
body.h2-underline h2,
body.h2-underline .HyperMD-header.HyperMD-header-2.cm-line {
  border-bottom: 2px solid var(--background-modifier-border);
  width: 100%;
  padding-bottom: 2px;
}

/* Hashtags font */
.markdown-source-view.mod-cm6.is-live-preview .cm-hashtag.cm-meta,
.markdown-source-view.mod-cm5 .cm-hashtag.cm-meta {
  font-family: var(--font-text-theme);
}

/* Blockquote */
body:not(.default-font-color) .markdown-preview-view blockquote,
body:not(.default-font-color) span.cm-quote.cm-quote-1 {
  font-style: italic;
  color: var(--quote-color);
}

/* Bold font */
body:not(.default-font-color) strong,
body:not(.default-font-color) span:not(.cm-highlight).cm-strong {
  color: var(--strong-color);
}

/* Italics */
body:not(.default-font-color) em,
body:not(.default-font-color) span:not(.cm-highlight).cm-em {
  color: var(--em-color);
}

/* Styled highlights */
body:not(.default-font-color) mark strong,
body:not(.default-font-color) mark em {
  color: var(--text-normal);
}

/* Highlight styling */
span.cm-highlight,
.markdown-preview-view mark,
span.search-result-file-matched-text {
  padding: 0.05em 0;
  -webkit-box-decoration-break: clone;
  box-decoration-break: clone;
}

/* Fancy highlight */
body.fancy-highlight span.cm-highlight,
body.fancy-highlight .markdown-preview-view mark,
body.fancy-highlight span.search-result-file-matched-text {
  background-image: linear-gradient(
    0deg,
    var(--highlight-background-color-underline) 0%,
    var(--highlight-background-color-underline) 2px,
    var(--highlight-background-color--active) 2px,
    var(--highlight-background-color--active) 100%
  ) !important;
  background-color: var(--highlight-text-color--active) !important;
}

/* Markdown formatting */
.cm-formatting-strong,
.cm-formatting-em,
.cm-formatting.cm-formatting-quote {
  color: var(--text-formatted) !important;
  font-weight: var(--normal-weight);
  letter-spacing: -0.02em;
}

/* Completed checkboxes */
.markdown-preview-view ul > li.task-list-item.is-checked,
.markdown-source-view.mod-cm6 .HyperMD-task-line[data-task='x'],
.markdown-source-view.mod-cm6 .HyperMD-task-line[data-task='X'],
.markdown-source-view.mod-cm6 .HyperMD-task-line[data-task='M'] {
  text-decoration: none;
  color: var(--text-faint);
}

/* Image card */
img {
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.theme-dark .modal-container .suggestion-item.is-selected {
  background: var(--color-accent);
}

/* Focus active line */
body.active-line .cm-active:not(.HyperMD-header, .HyperMD-codeblock)::before,
body.active-line .cm-active.HyperMD-quote::before {
  content: '';
  height: 100%;
  position: absolute;
  left: -1.125em;
  border-left: 3px solid var(--color-accent);
  border-radius: 10px 0 0 10px;
  opacity: 0.85;
}

/* Code blocks horizontal scroll */
.markdown-reading-view .markdown-preview-view pre:not(.frontmatter) code {
  white-space: pre;
}

/* Fancy code blocks */
.cm-inline-code,
.cm-s-obsidian .HyperMD-codeblock,
.markdown-preview-view.markdown-preview-view :is(pre, code) {
  --codeblock-border: var(--color-base-30);
  --codeblock-roundness: var(--radius-s);
  --code-block-alt-bg: var(--color-base-30);
  --slight-code-roundish: var(--radius-xs);
}

body.fancy-code .cm-s-obsidian .HyperMD-codeblock {
  line-height: 1.4em;
}

body.fancy-code .HyperMD-codeblock-begin {
  counter-reset: codeblock-line-numbers;
}

body.fancy-code
  .HyperMD-codeblock:not(
    .HyperMD-codeblock-begin,
    .HyperMD-codeblock-end
  )::before {
  counter-increment: codeblock-line-numbers;
  content: counter(codeblock-line-numbers);
  font-size: 0.75em;
  line-height: 2;
  text-align: right;
  height: 100%;
  width: 1.7em;
  color: var(--text-muted);
  /* background-color: #1e2029; */
  background-color: var(--code-background);
  position: absolute;
  left: 0;
  padding-right: 1.4em;
}

body.fancy-code
  .HyperMD-codeblock.cm-line:not(
    .HyperMD-codeblock-begin,
    .HyperMD-codeblock-end
  ) {
  padding-left: 2.8em;
}

body.fancy-code .cm-s-obsidian div.HyperMD-codeblock-begin-bg {
  background-color: var(--code-block-alt-bg);
  border: var(--codeblock-border);
  border-bottom: none;
  border-top-right-radius: var(--codeblock-roundness);
  border-top-left-radius: var(--codeblock-roundness);
}

body.fancy-code div.HyperMD-codeblock-bg:not(.HyperMD-codeblock-begin-bg) {
  border-right: var(--codeblock-border);
  border-left: var(--codeblock-border);
}

body.fancy-code .cm-line.HyperMD-codeblock .code-block-flair {
  font-size: calc(var(--code-size) * 0.9);
  color: var(--text-muted);
  padding: 0 1px;
  top: 0;
}

body.fancy-code .markdown-reading-view pre[class*='language-']::before {
  display: block;
  content: ' ';
  line-height: 1.5em;
  background-color: var(--code-block-alt-bg);
  border-top-right-radius: calc(var(--codeblock-roundness) * 0.8);
  border-top-left-radius: calc(var(--codeblock-roundness) * 0.8);
}

body.fancy-code pre[class*='language-']::after {
  content: attr(class);
  font-size: 0.9rem;
  text-shadow: none;
  color: var(--text-muted);
  position: absolute;
  top: 2px;
  right: 5px;
}

body.fancy-code .markdown-preview-view pre code {
  padding: var(--size-4-1) var(--size-4-2);
}

body.fancy-code .copy-code-button.copy-code-button.copy-code-button {
  background-color: var(--interactive-normal);
  top: unset;
  bottom: 0;
  padding: 0 var(--size-2-2);
}

body.fancy-code .markdown-preview-view.markdown-preview-view pre {
  padding: 0;
  margin-top: var(--size-4-2);
  border: var(--codeblock-border);
  border-radius: var(--codeblock-roundness);
}

body.fancy-code .markdown-reading-view .markdown-preview-view pre code {
  display: block;
}

body.fancy-code .markdown-preview-view pre code {
  padding: var(--size-4-1) var(--size-4-5);
}

/* ------------------- */
/* One Dark Syntax Coloring */
/* Source: https://github.com/AGMStudio/prism-theme-one-dark */
/* ------------------- */
.theme-light .token.comment .theme-light .cm-comment {
  color: #ababab;
}
.theme-dark .token.comment,
.theme-dark .cm-comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: var(--atom-gray-1);
}
.token.punctuation,
.token.plain-text,
.token.dom.variable,
.cm-hmd-codeblock.cm-bracket {
  color: var(--atom-gray-2);
}
code[class*='language-'],
.token.selector,
.cm-tag,
.token.dom.variable,
.token.tag,
.cm-def,
.token.parameter,
.cm-property,
.cm-qualifier {
  color: var(--atom-red);
}
.token.class-name,
.token.maybe-class-name,
.token.property-access,
.token.constant,
.token.builtin,
.cm-variable-2,
.cm-type,
.cm-atom,
code .cm-tag {
  color: var(--atom-yellow);
}
.token.property,
.token.boolean,
.token.number,
.token.symbol,
.token.attr-name,
.token.deleted,
.cm-attribute,
.cm-number,
.cm-property.cm-string {
  color: var(--atom-orange);
}
.token.string,
.token.char,
.token.attr-value,
.token.inserted,
.cm-hmd-codeblock.cm-string,
.cm-hmd-codeblock.cm-string-2 {
  color: var(--atom-green);
}
.token.operator,
.cm-operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string {
  color: var(--atom-aqua);
}
.token.atrule,
.token.keyword,
.cm-keyword {
  color: var(--atom-purple);
}
.cm-variable,
.token.function,
.token.method,
.token.macro.property {
  color: var(--atom-blue);
}
.token.regex,
.token.important,
.token.variable {
  color: var(--atom-purple);
}
.token.important,
.token.bold {
  font-weight: bold;
}
.token.italic {
  font-style: italic;
}
.token.entity {
  cursor: help;
}

/* --------------------- */
/* Mobile toolbar button */
/* --------------------- */

body.is-mobile:not(.floating-button-off):not(.advanced-toolbar)
  .view-action:nth-last-of-type(2),
body.is-mobile:not(.floating-button-off):not(.advanced-toolbar)
  .view-action:nth-last-of-type(2) {
  color: white;
  background-color: var(--blue);
  opacity: 1;
  top: calc(90vh - 110px);
  display: flex;
  padding: 5px;
  position: fixed;
  left: 86vw;
  transform: translate(-40%, 5%);
  justify-content: center;
  align-items: center;
  width: 53px;
  height: 53px;
  border-radius: 50% !important;
  box-shadow: 1.1px 0.3px 2.2px rgba(0, 0, 0, 0.02),
    2.7px 0.7px 5.3px rgba(0, 0, 0, 0.028), 5px 1.3px 10px rgba(0, 0, 0, 0.035),
    8.9px 2.2px 17.9px rgba(0, 0, 0, 0.042),
    16.7px 4.2px 33.4px rgba(0, 0, 0, 0.05), 40px 10px 80px rgba(0, 0, 0, 0.07);
}

body.is-mobile:not(.floating-button-off).advanced-toolbar
  .view-action:nth-last-of-type(2),
body.is-mobile:not(.floating-button-off).advanced-toolbar
  .view-action:nth-last-of-type(2) {
  color: white;
  background-color: var(--blue);
  opacity: 1;
  position: fixed;
  top: calc(100vh - 138px);
  display: flex;
  padding: 5px;
  left: 86vw;
  transform: translate(-40%, -115%);
  justify-content: center;
  align-items: center;
  width: 53px;
  height: 53px;
  border-radius: 50% !important;
  box-shadow: 1.1px 0.3px 2.2px rgba(0, 0, 0, 0.02),
    2.7px 0.7px 5.3px rgba(0, 0, 0, 0.028), 5px 1.3px 10px rgba(0, 0, 0, 0.035),
    8.9px 2.2px 17.9px rgba(0, 0, 0, 0.042),
    16.7px 4.2px 33.4px rgba(0, 0, 0, 0.05), 40px 10px 80px rgba(0, 0, 0, 0.07);
}

/* ------------------- */
/* Checkbox styling & icons. Credit Minimal theme: https://minimal.guide/Block+types/Checklists#Checkbox+styling */
/* Support @kepano - https://www.buymeacoffee.com/kepano */
/* ------------------- */

input[data-task='!']:checked,
input[data-task='*']:checked,
input[data-task='-']:checked,
input[data-task='<']:checked,
input[data-task='>']:checked,
input[data-task='I']:checked,
input[data-task='b']:checked,
input[data-task='c']:checked,
input[data-task='d']:checked,
input[data-task='f']:checked,
input[data-task='k']:checked,
input[data-task='l']:checked,
input[data-task='p']:checked,
input[data-task='u']:checked,
input[data-task='w']:checked,
input[data-task='P']:checked, /* Open PR */
input[data-task='M']:checked, /* Merged PR */
input[data-task='D']:checked, /* Draft PR */
li[data-task='!'] > input:checked,
li[data-task='!'] > p > input:checked,
li[data-task='*'] > input:checked,
li[data-task='*'] > p > input:checked,
li[data-task='-'] > input:checked,
li[data-task='-'] > p > input:checked,
li[data-task='<'] > input:checked,
li[data-task='<'] > p > input:checked,
li[data-task='>'] > input:checked,
li[data-task='>'] > p > input:checked,
li[data-task='I'] > input:checked,
li[data-task='I'] > p > input:checked,
li[data-task='b'] > input:checked,
li[data-task='b'] > p > input:checked,
li[data-task='c'] > input:checked,
li[data-task='c'] > p > input:checked,
li[data-task='d'] > input:checked,
li[data-task='d'] > p > input:checked,
li[data-task='f'] > input:checked,
li[data-task='f'] > p > input:checked,
li[data-task='k'] > input:checked,
li[data-task='k'] > p > input:checked,
li[data-task='l'] > input:checked,
li[data-task='l'] > p > input:checked,
li[data-task='p'] > input:checked,
li[data-task='p'] > p > input:checked,
li[data-task='u'] > input:checked,
li[data-task='u'] > p > input:checked,
li[data-task='w'] > input:checked,
li[data-task='w'] > p > input:checked,
li[data-task='P'] > input:checked,
li[data-task='P'] > p > input:checked,
li[data-task='M'] > input:checked,
li[data-task='M'] > p > input:checked,
li[data-task='D'] > input:checked,
li[data-task='D'] > p > input:checked {
  --checkbox-marker-color: transparent;
  border: none;
  border-radius: 0;
  background-image: none;
  background-color: currentColor;
  -webkit-mask-size: var(--checkbox-icon);
  -webkit-mask-position: 50% 50%;
}
input[data-task='>']:checked,
li[data-task='>'] > input:checked,
li[data-task='>'] > p > input:checked {
  color: var(--text-faint);
  transform: rotate(90deg);
  -webkit-mask-position: 50% 100%;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z' /%3E%3C/svg%3E");
}
input[data-task='<']:checked,
li[data-task='<'] > input:checked,
li[data-task='<'] > p > input:checked {
  color: var(--text-faint);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z' clip-rule='evenodd' /%3E%3C/svg%3E");
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z' clip-rule='evenodd' /%3E%3C/svg%3E");
}
input[data-task='?']:checked,
li[data-task='?'] > input:checked,
li[data-task='?'] > p > input:checked {
  --checkbox-marker-color: transparent;
  background-color: var(--color-yellow);
  border-color: var(--color-yellow);
  background-position: 50% 50%;
  background-size: 200% 90%;
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="20" height="20" preserveAspectRatio="xMidYMid meet" viewBox="0 0 16 16"%3E%3Cpath fill="white" fill-rule="evenodd" d="M4.475 5.458c-.284 0-.514-.237-.47-.517C4.28 3.24 5.576 2 7.825 2c2.25 0 3.767 1.36 3.767 3.215c0 1.344-.665 2.288-1.79 2.973c-1.1.659-1.414 1.118-1.414 2.01v.03a.5.5 0 0 1-.5.5h-.77a.5.5 0 0 1-.5-.495l-.003-.2c-.043-1.221.477-2.001 1.645-2.712c1.03-.632 1.397-1.135 1.397-2.028c0-.979-.758-1.698-1.926-1.698c-1.009 0-1.71.529-1.938 1.402c-.066.254-.278.461-.54.461h-.777ZM7.496 14c.622 0 1.095-.474 1.095-1.09c0-.618-.473-1.092-1.095-1.092c-.606 0-1.087.474-1.087 1.091S6.89 14 7.496 14Z"%2F%3E%3C%2Fsvg%3E');
}
.theme-dark input[data-task='?']:checked,
.theme-dark li[data-task='?'] > input:checked,
.theme-dark li[data-task='?'] > p > input:checked {
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="20" height="20" preserveAspectRatio="xMidYMid meet" viewBox="0 0 16 16"%3E%3Cpath fill="black" fill-opacity="0.8" fill-rule="evenodd" d="M4.475 5.458c-.284 0-.514-.237-.47-.517C4.28 3.24 5.576 2 7.825 2c2.25 0 3.767 1.36 3.767 3.215c0 1.344-.665 2.288-1.79 2.973c-1.1.659-1.414 1.118-1.414 2.01v.03a.5.5 0 0 1-.5.5h-.77a.5.5 0 0 1-.5-.495l-.003-.2c-.043-1.221.477-2.001 1.645-2.712c1.03-.632 1.397-1.135 1.397-2.028c0-.979-.758-1.698-1.926-1.698c-1.009 0-1.71.529-1.938 1.402c-.066.254-.278.461-.54.461h-.777ZM7.496 14c.622 0 1.095-.474 1.095-1.09c0-.618-.473-1.092-1.095-1.092c-.606 0-1.087.474-1.087 1.091S6.89 14 7.496 14Z"%2F%3E%3C%2Fsvg%3E');
}
input[data-task='/']:checked,
li[data-task='/'] > input:checked,
li[data-task='/'] > p > input:checked {
  background-image: none;
  background-color: transparent;
  position: relative;
  overflow: hidden;
}
input[data-task='/']:checked:after,
li[data-task='/'] > input:checked:after,
li[data-task='/'] > p > input:checked:after {
  top: 0;
  left: 0;
  content: ' ';
  display: block;
  position: absolute;
  background-color: var(--color-accent);
  width: calc(50% - 0.5px);
  height: 100%;
  -webkit-mask-image: none;
}
input[data-task='!']:checked,
li[data-task='!'] > input:checked,
li[data-task='!'] > p > input:checked {
  color: var(--color-orange);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z' clip-rule='evenodd' /%3E%3C/svg%3E");
}
input[data-task='"']:checked,
input[data-task='“']:checked,
li[data-task='"'] > input:checked,
li[data-task='"'] > p > input:checked,
li[data-task='“'] > input:checked,
li[data-task='“'] > p > input:checked {
  --checkbox-marker-color: transparent;
  background-position: 50% 50%;
  background-color: var(--color-cyan);
  border-color: var(--color-cyan);
  background-size: 75%;
  background-repeat: no-repeat;
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="20" height="20" preserveAspectRatio="xMidYMid meet" viewBox="0 0 24 24"%3E%3Cpath fill="white" d="M6.5 10c-.223 0-.437.034-.65.065c.069-.232.14-.468.254-.68c.114-.308.292-.575.469-.844c.148-.291.409-.488.601-.737c.201-.242.475-.403.692-.604c.213-.21.492-.315.714-.463c.232-.133.434-.28.65-.35l.539-.222l.474-.197l-.485-1.938l-.597.144c-.191.048-.424.104-.689.171c-.271.05-.56.187-.882.312c-.318.142-.686.238-1.028.466c-.344.218-.741.4-1.091.692c-.339.301-.748.562-1.05.945c-.33.358-.656.734-.909 1.162c-.293.408-.492.856-.702 1.299c-.19.443-.343.896-.468 1.336c-.237.882-.343 1.72-.384 2.437c-.034.718-.014 1.315.028 1.747c.015.204.043.402.063.539l.025.168l.026-.006A4.5 4.5 0 1 0 6.5 10zm11 0c-.223 0-.437.034-.65.065c.069-.232.14-.468.254-.68c.114-.308.292-.575.469-.844c.148-.291.409-.488.601-.737c.201-.242.475-.403.692-.604c.213-.21.492-.315.714-.463c.232-.133.434-.28.65-.35l.539-.222l.474-.197l-.485-1.938l-.597.144c-.191.048-.424.104-.689.171c-.271.05-.56.187-.882.312c-.317.143-.686.238-1.028.467c-.344.218-.741.4-1.091.692c-.339.301-.748.562-1.05.944c-.33.358-.656.734-.909 1.162c-.293.408-.492.856-.702 1.299c-.19.443-.343.896-.468 1.336c-.237.882-.343 1.72-.384 2.437c-.034.718-.014 1.315.028 1.747c.015.204.043.402.063.539l.025.168l.026-.006A4.5 4.5 0 1 0 17.5 10z"%2F%3E%3C%2Fsvg%3E');
}
.theme-dark input[data-task='"']:checked,
.theme-dark input[data-task='“']:checked,
.theme-dark li[data-task='"'] > input:checked,
.theme-dark li[data-task='"'] > p > input:checked,
.theme-dark li[data-task='“'] > input:checked,
.theme-dark li[data-task='“'] > p > input:checked {
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="20" height="20" preserveAspectRatio="xMidYMid meet" viewBox="0 0 24 24"%3E%3Cpath fill="black" fill-opacity="0.7" d="M6.5 10c-.223 0-.437.034-.65.065c.069-.232.14-.468.254-.68c.114-.308.292-.575.469-.844c.148-.291.409-.488.601-.737c.201-.242.475-.403.692-.604c.213-.21.492-.315.714-.463c.232-.133.434-.28.65-.35l.539-.222l.474-.197l-.485-1.938l-.597.144c-.191.048-.424.104-.689.171c-.271.05-.56.187-.882.312c-.318.142-.686.238-1.028.466c-.344.218-.741.4-1.091.692c-.339.301-.748.562-1.05.945c-.33.358-.656.734-.909 1.162c-.293.408-.492.856-.702 1.299c-.19.443-.343.896-.468 1.336c-.237.882-.343 1.72-.384 2.437c-.034.718-.014 1.315.028 1.747c.015.204.043.402.063.539l.025.168l.026-.006A4.5 4.5 0 1 0 6.5 10zm11 0c-.223 0-.437.034-.65.065c.069-.232.14-.468.254-.68c.114-.308.292-.575.469-.844c.148-.291.409-.488.601-.737c.201-.242.475-.403.692-.604c.213-.21.492-.315.714-.463c.232-.133.434-.28.65-.35l.539-.222l.474-.197l-.485-1.938l-.597.144c-.191.048-.424.104-.689.171c-.271.05-.56.187-.882.312c-.317.143-.686.238-1.028.467c-.344.218-.741.4-1.091.692c-.339.301-.748.562-1.05.944c-.33.358-.656.734-.909 1.162c-.293.408-.492.856-.702 1.299c-.19.443-.343.896-.468 1.336c-.237.882-.343 1.72-.384 2.437c-.034.718-.014 1.315.028 1.747c.015.204.043.402.063.539l.025.168l.026-.006A4.5 4.5 0 1 0 17.5 10z"%2F%3E%3C%2Fsvg%3E');
}
input[data-task='-']:checked,
li[data-task='-'] > input:checked,
li[data-task='-'] > p > input:checked {
  color: var(--text-faint);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z' clip-rule='evenodd' /%3E%3C/svg%3E");
}
body:not(.tasks)
  .markdown-preview-view
  ul
  li[data-task='-'].task-list-item.is-checked,
body:not(.tasks)
  .markdown-source-view.mod-cm6
  .HyperMD-task-line[data-task]:is([data-task='-']),
body:not(.tasks) li[data-task='-'].task-list-item.is-checked {
  color: var(--text-faint);
  text-decoration: line-through solid var(--text-faint) 1px;
}
input[data-task='*']:checked,
li[data-task='*'] > input:checked,
li[data-task='*'] > p > input:checked {
  color: var(--color-yellow);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z' /%3E%3C/svg%3E");
}
input[data-task='l']:checked,
li[data-task='l'] > input:checked,
li[data-task='l'] > p > input:checked {
  color: var(--color-red);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z' clip-rule='evenodd' /%3E%3C/svg%3E");
}
input[data-task='i']:checked,
li[data-task='i'] > input:checked,
li[data-task='i'] > p > input:checked {
  --checkbox-marker-color: transparent;
  background-color: var(--color-blue);
  border-color: var(--color-blue);
  background-position: 50%;
  background-size: 100%;
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="20" height="20" preserveAspectRatio="xMidYMid meet" viewBox="0 0 512 512"%3E%3Cpath fill="none" stroke="white" stroke-linecap="round" stroke-linejoin="round" stroke-width="40" d="M196 220h64v172"%2F%3E%3Cpath fill="none" stroke="white" stroke-linecap="round" stroke-miterlimit="10" stroke-width="40" d="M187 396h138"%2F%3E%3Cpath fill="white" d="M256 160a32 32 0 1 1 32-32a32 32 0 0 1-32 32Z"%2F%3E%3C%2Fsvg%3E');
}
.theme-dark input[data-task='i']:checked,
.theme-dark li[data-task='i'] > input:checked,
.theme-dark li[data-task='i'] > p > input:checked {
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="20" height="20" preserveAspectRatio="xMidYMid meet" viewBox="0 0 512 512"%3E%3Cpath fill="none" stroke="black" stroke-opacity="0.8" stroke-linecap="round" stroke-linejoin="round" stroke-width="40" d="M196 220h64v172"%2F%3E%3Cpath fill="none" stroke="black" stroke-opacity="0.8" stroke-linecap="round" stroke-miterlimit="10" stroke-width="40" d="M187 396h138"%2F%3E%3Cpath fill="black" fill-opacity="0.8" d="M256 160a32 32 0 1 1 32-32a32 32 0 0 1-32 32Z"%2F%3E%3C%2Fsvg%3E');
}
input[data-task='S']:checked,
li[data-task='S'] > input:checked,
li[data-task='S'] > p > input:checked {
  --checkbox-marker-color: transparent;
  border-color: var(--color-green);
  background-color: var(--color-green);
  background-size: 100%;
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="20" height="20" preserveAspectRatio="xMidYMid meet" viewBox="0 0 48 48"%3E%3Cpath fill="white" fill-rule="evenodd" d="M26 8a2 2 0 1 0-4 0v2a8 8 0 1 0 0 16v8a4.002 4.002 0 0 1-3.773-2.666a2 2 0 0 0-3.771 1.332A8.003 8.003 0 0 0 22 38v2a2 2 0 1 0 4 0v-2a8 8 0 1 0 0-16v-8a4.002 4.002 0 0 1 3.773 2.666a2 2 0 0 0 3.771-1.332A8.003 8.003 0 0 0 26 10V8Zm-4 6a4 4 0 0 0 0 8v-8Zm4 12v8a4 4 0 0 0 0-8Z" clip-rule="evenodd"%2F%3E%3C%2Fsvg%3E');
}
.theme-dark input[data-task='S']:checked,
.theme-dark li[data-task='S'] > input:checked,
.theme-dark li[data-task='S'] > p > input:checked {
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="20" height="20" preserveAspectRatio="xMidYMid meet" viewBox="0 0 48 48"%3E%3Cpath fill-opacity="0.8" fill="black" fill-rule="evenodd" d="M26 8a2 2 0 1 0-4 0v2a8 8 0 1 0 0 16v8a4.002 4.002 0 0 1-3.773-2.666a2 2 0 0 0-3.771 1.332A8.003 8.003 0 0 0 22 38v2a2 2 0 1 0 4 0v-2a8 8 0 1 0 0-16v-8a4.002 4.002 0 0 1 3.773 2.666a2 2 0 0 0 3.771-1.332A8.003 8.003 0 0 0 26 10V8Zm-4 6a4 4 0 0 0 0 8v-8Zm4 12v8a4 4 0 0 0 0-8Z" clip-rule="evenodd"%2F%3E%3C%2Fsvg%3E');
}
input[data-task='I']:checked,
li[data-task='I'] > input:checked,
li[data-task='I'] > p > input:checked {
  color: var(--color-yellow);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M11 3a1 1 0 10-2 0v1a1 1 0 102 0V3zM15.657 5.757a1 1 0 00-1.414-1.414l-.707.707a1 1 0 001.414 1.414l.707-.707zM18 10a1 1 0 01-1 1h-1a1 1 0 110-2h1a1 1 0 011 1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zM5 10a1 1 0 01-1 1H3a1 1 0 110-2h1a1 1 0 011 1zM8 16v-1h4v1a2 2 0 11-4 0zM12 14c.015-.34.208-.646.477-.859a4 4 0 10-4.954 0c.27.213.462.519.476.859h4.002z' /%3E%3C/svg%3E");
}
input[data-task='f']:checked,
li[data-task='f'] > input:checked,
li[data-task='f'] > p > input:checked {
  color: var(--color-red);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z' clip-rule='evenodd' /%3E%3C/svg%3E");
}
input[data-task='k']:checked,
li[data-task='k'] > input:checked,
li[data-task='k'] > p > input:checked {
  color: var(--color-yellow);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z' clip-rule='evenodd' /%3E%3C/svg%3E");
}
input[data-task='u']:checked,
li[data-task='u'] > input:checked,
li[data-task='u'] > p > input:checked {
  color: var(--color-green);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z' clip-rule='evenodd' /%3E%3C/svg%3E");
}
input[data-task='d']:checked,
li[data-task='d'] > input:checked,
li[data-task='d'] > p > input:checked {
  color: var(--color-red);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M12 13a1 1 0 100 2h5a1 1 0 001-1V9a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586 3.707 5.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z' clip-rule='evenodd' /%3E%3C/svg%3E");
}
input[data-task='w']:checked,
li[data-task='w'] > input:checked,
li[data-task='w'] > p > input:checked {
  color: var(--color-purple);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M6 3a1 1 0 011-1h.01a1 1 0 010 2H7a1 1 0 01-1-1zm2 3a1 1 0 00-2 0v1a2 2 0 00-2 2v1a2 2 0 00-2 2v.683a3.7 3.7 0 011.055.485 1.704 1.704 0 001.89 0 3.704 3.704 0 014.11 0 1.704 1.704 0 001.89 0 3.704 3.704 0 014.11 0 1.704 1.704 0 001.89 0A3.7 3.7 0 0118 12.683V12a2 2 0 00-2-2V9a2 2 0 00-2-2V6a1 1 0 10-2 0v1h-1V6a1 1 0 10-2 0v1H8V6zm10 8.868a3.704 3.704 0 01-4.055-.036 1.704 1.704 0 00-1.89 0 3.704 3.704 0 01-4.11 0 1.704 1.704 0 00-1.89 0A3.704 3.704 0 012 14.868V17a1 1 0 001 1h14a1 1 0 001-1v-2.132zM9 3a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zm3 0a1 1 0 011-1h.01a1 1 0 110 2H13a1 1 0 01-1-1z' clip-rule='evenodd' /%3E%3C/svg%3E");
}
input[data-task='p']:checked,
li[data-task='p'] > input:checked,
li[data-task='p'] > p > input:checked {
  color: var(--color-green);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z' /%3E%3C/svg%3E");
}
input[data-task='c']:checked,
li[data-task='c'] > input:checked,
li[data-task='c'] > p > input:checked {
  color: var(--color-orange);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M18 9.5a1.5 1.5 0 11-3 0v-6a1.5 1.5 0 013 0v6zM14 9.667v-5.43a2 2 0 00-1.105-1.79l-.05-.025A4 4 0 0011.055 2H5.64a2 2 0 00-1.962 1.608l-1.2 6A2 2 0 004.44 12H8v4a2 2 0 002 2 1 1 0 001-1v-.667a4 4 0 01.8-2.4l1.4-1.866a4 4 0 00.8-2.4z' /%3E%3C/svg%3E");
}
input[data-task='b']:checked,
li[data-task='b'] > input:checked,
li[data-task='b'] > p > input:checked {
  color: var(--color-orange);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z' /%3E%3C/svg%3E");
}
input[data-task='P']:checked,
li[data-task='P'] > input:checked,
li[data-task='P'] > p > input:checked {
  color: var(--color-green);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' width='16' height='16'%3E%3Cpath d='M1.5 3.25a2.25 2.25 0 1 1 3 2.122v5.256a2.251 2.251 0 1 1-1.5 0V5.372A2.25 2.25 0 0 1 1.5 3.25Zm5.677-.177L9.573.677A.25.25 0 0 1 10 .854V2.5h1A2.5 2.5 0 0 1 13.5 5v5.628a2.251 2.251 0 1 1-1.5 0V5a1 1 0 0 0-1-1h-1v1.646a.25.25 0 0 1-.427.177L7.177 3.427a.25.25 0 0 1 0-.354ZM3.75 2.5a.75.75 0 1 0 0 1.5.75.75 0 0 0 0-1.5Zm0 9.5a.75.75 0 1 0 0 1.5.75.75 0 0 0 0-1.5Zm8.25.75a.75.75 0 1 0 1.5 0 .75.75 0 0 0-1.5 0Z'%3E%3C/path%3E%3C/svg%3E");
}
input[data-task='M']:checked,
li[data-task='M'] > input:checked,
li[data-task='M'] > p > input:checked {
  color: var(--color-purple);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' width='16' height='16'%3E%3Cpath d='M5.45 5.154A4.25 4.25 0 0 0 9.25 7.5h1.378a2.251 2.251 0 1 1 0 1.5H9.25A5.734 5.734 0 0 1 5 7.123v3.505a2.25 2.25 0 1 1-1.5 0V5.372a2.25 2.25 0 1 1 1.95-.218ZM4.25 13.5a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm8.5-4.5a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5ZM5 3.25a.75.75 0 1 0 0 .005V3.25Z'%3E%3C/path%3E%3C/svg%3E");
}
input[data-task='D']:checked,
li[data-task='D'] > input:checked,
li[data-task='D'] > p > input:checked {
  color: var(--color-base-50);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' width='16' height='16'%3E%3Cpath d='M3.25 1A2.25 2.25 0 0 1 4 5.372v5.256a2.251 2.251 0 1 1-1.5 0V5.372A2.251 2.251 0 0 1 3.25 1Zm9.5 14a2.25 2.25 0 1 1 0-4.5 2.25 2.25 0 0 1 0 4.5ZM2.5 3.25a.75.75 0 1 0 1.5 0 .75.75 0 0 0-1.5 0ZM3.25 12a.75.75 0 1 0 0 1.5.75.75 0 0 0 0-1.5Zm9.5 0a.75.75 0 1 0 0 1.5.75.75 0 0 0 0-1.5ZM14 7.5a1.25 1.25 0 1 1-2.5 0 1.25 1.25 0 0 1 2.5 0Zm0-4.25a1.25 1.25 0 1 1-2.5 0 1.25 1.25 0 0 1 2.5 0Z'%3E%3C/path%3E%3C/svg%3E");
}

body:not(.tasks) li[data-task='>'].task-list-item.is-checked,
body:not(.tasks) li[data-task='<'].task-list-item.is-checked,
body:not(.tasks) li[data-task='b'].task-list-item.is-checked,
body:not(.tasks) li[data-task='i'].task-list-item.is-checked,
body:not(.tasks) li[data-task='*'].task-list-item.is-checked,
body:not(.tasks) li[data-task='!'].task-list-item.is-checked,
body:not(.tasks) li[data-task='S'].task-list-item.is-checked,
body:not(.tasks) li[data-task='?'].task-list-item.is-checked,
body:not(.tasks) li[data-task='/'].task-list-item.is-checked,
body:not(.tasks) li[data-task='"'].task-list-item.is-checked,
body:not(.tasks) li[data-task='l'].task-list-item.is-checked,
body:not(.tasks) li[data-task='I'].task-list-item.is-checked,
body:not(.tasks) li[data-task='p'].task-list-item.is-checked,
body:not(.tasks) li[data-task='c'].task-list-item.is-checked,
body:not(.tasks) li[data-task='f'].task-list-item.is-checked,
body:not(.tasks) li[data-task='k'].task-list-item.is-checked,
body:not(.tasks) li[data-task='w'].task-list-item.is-checked,
body:not(.tasks) li[data-task='u'].task-list-item.is-checked,
body:not(.tasks) li[data-task='d'].task-list-item.is-checked,
body:not(.tasks) li[data-task='P'].task-list-item.is-checked,
body:not(.tasks) li[data-task='D'].task-list-item.is-checked {
  color: var(--text-normal);
}

/* ─────────────────────────────────────────────────── */
/* Plugins */
/* ─────────────────────────────────────────────────── */

/* --------------- */
/* Todoist */
/* --------------- */

.todoist-p1 > input[type='checkbox'] {
  border: 1px solid #ff757f !important;
  background-color: rgba(255, 117, 127, 0.25) !important;
}
.todoist-p1 > input[type='checkbox']:hover {
  background-color: rgba(255, 117, 127, 0.5) !important;
}
.todoist-p2 > input[type='checkbox'] {
  border: 1px solid #ffc777 !important;
  background-color: rgba(255, 199, 119, 0.25) !important;
}
.todoist-p2 > input[type='checkbox']:hover {
  background-color: rgba(255, 199, 119, 0.5) !important;
}
.todoist-p3 > input[type='checkbox'] {
  border: 1px solid #65bcff !important;
  background-color: rgba(101, 188, 255, 0.25) !important;
}
.todoist-p3 > input[type='checkbox']:hover {
  background-color: rgba(101, 188, 255, 0.5) !important;
}
.todoist-p4 > input[type='checkbox'] {
  border: 1px solid #b4c2f0 !important;
  background-color: rgba(180, 194, 240, 0.25) !important;
}
.todoist-p4 > input[type='checkbox']:hover {
  background-color: rgba(180, 194, 240, 0.5) !important;
}
.task-metadata {
  font-size: var(--font-todoist-metadata-size);
  color: #7a88cf;
  margin-left: unset;
}
.task-metadata > * {
  margin-right: 30px;
}
.task-date.task-overdue {
  color: rgba(255, 152, 164, 0.75) !important;
}
.task-calendar-icon,
.task-project-icon,
.task-labels-icon {
  vertical-align: middle;
  height: 17px;
  width: 17px;
}
.todoist-project .todoist-project {
  margin-left: 20px;
}
.todoist-section {
  margin-left: 20px;
}
.todoist-project .todoist-project-title {
  font-weight: 700;
  margin-block-end: 0px;
}
.todoist-section .todoist-section-title {
  font-size: var(--font-todoist-title-size);
  color: #7a88cf;
  font-weight: 700;
  margin-block-end: 0px;
}
.todoist-error {
  border: 1px solid #ff98a4;
  background-color: rgba(255, 152, 164, 0.05);
  padding: 1em 1em;
  margin: 1em 0px;
}
.todoist-error p {
  margin: 0 0 1em 0;
  font-weight: 600;
}
.todoist-error code {
  background-color: unset !important;
  padding: unset !important;
  margin: unset !important;
}
.todoist-success {
  border: 1px solid #c3e88d !important;
  background-color: rgba(195, 232, 141, 0.05);
  padding: 1em 1em !important;
  margin: 1em 0px;
}
.todoist-success p {
  margin: 0;
  font-weight: 600;
}
/* .priority-container .priority-1 {
  color: #ff98a4;
}
.priority-container .priority-2 {
  color: #ffc777;
}
.priority-container .priority-3 {
  color: #65bcff;
}
.priority-container .priority-4 {
  color: #b4c2f0;
} */

/* ---------------  */
/* Checklist */
/* --------------- */

.checklist-plugin-main .title {
  font-size: var(--nav-item-size);
}

.checklist-plugin-main .container input.search {
  font-size: var(--font-ui-small);
}

.checklist-plugin-main .group-header button.collapse,
.checklist-plugin-main button.toggle {
  box-shadow: none;
  cursor: pointer;
}

.checklist-plugin-main .classic .content > p {
  font-size: var(--font-ui-smaller);
}

.checklist-plugin-main .toggle .checkbox {
  border-radius: var(--checkbox-radius) !important;
}

/* ---------------  */
/* Kanban */
/* --------------- */

body:not(.no-kanban-styles) .kanban-plugin {
  --dot-color: hsl(0 0% 40% / 10%);
  --dot-spacing: 9px;
  --dot-size: 1px;
}
body:not(.no-kanban-styles) .kanban-plugin__board {
  background-image: radial-gradient(
    circle,
    var(--dot-color) var(--dot-size),
    transparent var(--dot-size)
  );
  border-top: var(--thin-border);
  background-size: var(--dot-spacing) var(--dot-spacing);
  background-attachment: local;
}

body:not(.no-kanban-styles) .kanban-plugin__board > div {
  margin: 0 auto;
}
body:not(.no-kanban-styles) .kanban-plugin__item-form {
  border-top: none;
}
body:not(.no-kanban-styles) .kanban-plugin__lane-header-wrapper {
  border-bottom: 0;
}
body:not(.no-kanban-styles) .kanban-plugin button {
  box-shadow: none;
}
body:not(.no-kanban-styles) .kanban-plugin__item-button-wrapper > button:hover {
  color: var(--text-normal);
  background: var(--background-modifier-hover);
}
body:not(.no-kanban-styles) .kanban-plugin__item-button-wrapper {
  border-top: none;
}
body:not(.no-kanban-styles) .kanban-plugin__lane {
  border: 1px solid transparent;
}
body:not(.no-kanban-styles) .kanban-plugin__item-content-wrapper {
  box-shadow: none;
}

body:not(.no-kanban-styles):not(.is-mobile)
  .kanban-plugin__grow-wrap
  > textarea:focus {
  box-shadow: none;
}
body:not(.no-kanban-styles) .kanban-plugin__item-input-actions button,
body:not(.no-kanban-styles) .kanban-plugin__lane-input-actions button {
  font-size: var(--font-adaptive-small);
}
body:not(.no-kanban-styles)
  .kanban-plugin__lane-header-wrapper
  .kanban-plugin__grow-wrap
  > textarea,
body:not(.no-kanban-styles)
  .kanban-plugin__lane-input-wrapper
  .kanban-plugin__grow-wrap
  > textarea {
  background: transparent;
}

/* ─────────────────────────────────────────────────── */
/* Styles Settings */
/* ─────────────────────────────────────────────────── */

/* @settings
name: Things Theme
id: things-style
settings:
    -
        id: features
        title: Features
        type: heading
        level: 2
        collapsed: true
    -
        title: Black mobile background
        description: Change mobile editor background to default theme black
        id: mobile-black-background
        type: class-toggle
        default: false
    -
        title: Disable mobile floating-action button
        description: Revert placement of edit/preview button to default in header (mobile)
        id: floating-button-off
        type: class-toggle
        default: false
    -
        title: Highlight active line
        description: Change background color of current working line
        id: active-line
        type: class-toggle
        default: false
    -
        title: Fancy code blocks
        description: Enable fancy numbered code blocks
        id: fancy-code
        type: class-toggle
        default: false
    -
        title: Fancy highlighting
        description: Enable fancy highlight styles with highlight underlines
        id: fancy-highlight
        type: class-toggle
        default: false
    -
        title: Disable Kanban board styles
        description: Remove minimalist styling to the Kanban plugin
        id: no-kanban-styles
        type: class-toggle
        default: false
    -
        title: Always show title bar
        description: Disable hiding of title bar until hover
        id: no-minimal-title-bar
        type: class-toggle
        default: false
    -
        id: link-decoration
        title: Underline internal links
        description: Show underlines on internal links
        type: variable-select
        default: Underline
        options:
          - Underline
          - None
    -
        id: link-external-decoration
        title: Underline external links
        description: Show underlines on external links
        type: variable-select
        default: Underline
        options:
          - Underline
          - None
	-
        id: custom-fonts
        title: Typography
        type: heading
        level: 2
        collapsed: true
    - 
        id: default-font-color
        title: Default font colors
        description: Use the default font color styling for bold, italics, and quotes
        type: class-toggle
        default:  false
    - 
        id: text-highlight-bg-l
        title: Highlight color (light)
        type: variable-color
        format: rgb
        default: 'rgba(255, 208, 0, 0.4)'
    - 
        id: text-highlight-bg-d
        title: Highlight color (dark)
        type: variable-color
        format: rgb
        default: 'rgba(255, 208, 0, 0.4)'
    - 
        id: strong-color
        title: Bold font color
        type: variable-color
        format: hex
        default: '#FF82B2' 
    - 
        id: em-color
        title: Italics font color
        type: variable-color
        format: hex
        default: '#FF82B2' 
    - 
        id: quote-color
        title: Blockquotes font color
        type: variable-color
        format: hex
        default: '#3EB4BF' 
    -
        id: code-normal
        title: Inline code blocks font color (Light mode)
        type: variable-color
        format: hex
        default: '#BEC6CF'
    -
        id: code-color-d
        title: Inline code blocks font color (Dark mode)
        type: variable-color
        format: hex
        default: '#555E68'
    - 
        id: tag-background-color-l
        title: Tag background color (Light mode)
        type: variable-color
        format: hex
        default: '#BDE1D3' 
    - 
        id: tag-font-color-l
        title: Tag font color (Light mode)
        type: variable-color
        format: hex
        default: '#1D694B' 
    - 
        id: tag-background-color-d
        title: Tag background color (Dark mode)
        type: variable-color
        format: hex
        default: '#1D694B' 
    - 
        id: tag-font-color-d
        title: Tag font color (Dark mode)
        type: variable-color
        format: hex
        default: '#FFFFFF' 
    - 
        id: headings
        title: Headings
        type: heading
        level: 2
        collapsed: true
    - 
        id: level-1-headings
        title: Level 1 Headings
        type: heading
        level: 3
        collapsed: true
    - 
        id: h1-size
        title: H1 font size
        description: Accepts any CSS font-size value
        type: variable-text
        default: 1.7em
    - 
        id: h1-weight
        title: H1 font weight
        description: Accepts numbers representing the CSS font-weight
        type: variable-number
        default: 700
    -
        id: h1-color
        title: H1 color
        type: variable-color
        format: hex
        default: '#'
    - 
        id: level-2-headings
        title: Level 2 Headings
        type: heading
        level: 3
        collapsed: true
    - 
        id: h2-size
        title: H2 font size
        description: Accepts any CSS font-size value
        type: variable-text
        default: 1.5em
    - 
        id: h2-weight
        title: H2 font weight
        description: Accepts numbers representing the CSS font-weight
        type: variable-number
        default: 700
    - 
        id: h2-color
        title: H2 color
        type: variable-color
        format: hex
        default: '#2E80F2'
    - 
        id: h2-underline
        title: H2 underline
        description: Toggle H2 underline (border-bottom)
        type: class-toggle
        default: true
    -
        id: level-3-headings
        title: Level 3 Headings
        type: heading
        level: 3
        collapsed: true
    - 
        id: h3-size
        title: H3 font size
        description: Accepts any CSS font-size value
        type: variable-text
        default: 1.2em
    - 
        id: h3-weight
        title: H3 font weight
        description: Accepts numbers representing the CSS font-weight
        type: variable-number
        default: 600
    -
        id: h3-color
        title: H3 color
        type: variable-color
        format: hex
        default: '#2E80F2'
    - 
        id: level-4-headings
        title: Level 4 Headings
        type: heading
        level: 3
        collapsed: true
    - 
        id: h4-size
        title: H4 font size
        description: Accepts any CSS font-size value
        type: variable-text
        default: 1.1em
    - 
        id: h4-weight
        title: H4 font weight
        description: Accepts numbers representing the CSS font-weight
        type: variable-number
        default: 500
    - 
        id: h4-color
        title: H4 color
        type: variable-color
        format: hex
        default: '#E5B567'
    -
        id: h4-transform
        title: H4 transform
        description: Transform the H4 heading text
        type: variable-select
        default: uppercase
        options:
            -
                label: Uppercase
                value: uppercase
            -
                label: None
                value: none
    - 
        id: level-5-headings
        title: Level 5 Headings
        type: heading
        level: 3
        collapsed: true
    - 
        id: h5-size
        title: H5 font size
        description: Accepts any CSS font-size value
        type: variable-text
        default: 1em
    - 
        id: h5-weight
        title: H5 font weight
        description: Accepts numbers representing the CSS font-weight
        type: variable-number
        default: 500
    - 
        id: h5-color
        title: H5 color
        type: variable-color
        format: hex
        default: '#E83E3E'
    - 
        id: level-6-headings
        title: Level 6 Headings
        type: heading
        level: 3
        collapsed: true
    - 
        id: h6-size
        title: H6 font size
        description: Accepts any CSS font-size value
        type: variable-text
        default: 0.9em
    - 
        id: h6-weight
        title: H6 font weight
        description: Accepts numbers representing the CSS font-weight
        type: variable-number
        default: 400
    -
        id: h6-color
        title: H6 color
        type: variable-color
        format: hex
        default: '#'
    -
        id: credits
        title: Credits
        type: heading
        description: Created with ❤︎ by @colineckert. Support @colineckert at buymeacoffee.com/colineckert
        level: 2
        collapsed: true

*/

/* ─────────────────────────────────────────────────── */
/* Plugin Compatibility info for the Obsidian Hub */
/* ─────────────────────────────────────────────────── */

/* @plugins
core:
- backlink
- command-palette
- file-explorer
- global-search
- graph
- outgoing-link
- outline
- page-preview
- starred
- switcher
- tag-pane
- file-recovery
- daily-notes
- random-note
- publish
- sync
- word-count
community:
- sliding-panes-obsidian
- obsidian-codemirror-options
- obsidian-kanban
- dataview
- obsidian-hider
- calendar
- mysnippets-plugin
- cmenu-plugin
- obsidian-outliner
- readwise-official
- tag-wrangler
- todoist-sync-plugin
- templater-obsidian
- obsidian-system-dark-mode
- obsidian-style-settings
*/
