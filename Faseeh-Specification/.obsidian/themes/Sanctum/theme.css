/* ─────────────────────────────────────────────────────────────────────────
Sanctum for Obsidian v0.16
created by @j<PERSON><PERSON><PERSON><PERSON> (Github)

Sponsor my work:
https://ko-fi.com/jdanielmourao
https://paypal.me/jdanielmourao

Readme:
https://github.com/jdanielmourao/obsidian-sanctum

Forum Thread:
https://forum.obsidian.md/t/sanctum-theme/25455

Description:
A minimalist theme for creating a serene space of retreat, for thought and uninterrupted work.

Disclaimer:
This theme is designed to be used with the Style Settings plugin.

——————————————————————————————————————————————————————————————————————

MIT License

Copyright (c) 2021-2022 <PERSON> (jdanielmourao)

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

───────────────────────────────────────────────────────────────────────── */
@charset "UTF-8";

/* src/scss/index.scss */
body {
  --font-editor-theme: ??;
  --font-editor: var(--font-editor-theme), var(--font-text);
  --font-text-theme: Inter,sans-serif;
  --font-monospace-theme:
    Menlo,SFMono-Regular,Consolas,"Roboto Mono",monospace;
  --font-interface-theme: Inter,sans-serif;
}
:root {
  --leading-0: 1em;
  --leading-1: 1.125em;
  --leading-2: 1.25em;
  --leading-3: 1.375em;
  --leading-4: 1.5em;
  --leading-5: 1.625em;
  --leading-6: 1.75em;
  --leading-7: 1.875em;
  --leading-8: 2.25em;
  --leading-9: 2.5em;
  --leading-10: 2.75em;
  --tracking-0: 0px;
  --tracking-1: .16px;
  --tracking-2: .32px;
  --tracking-3: .64px;
  --tracking-4: .96px;
  --size-0: 0px;
  --size-1: 2px;
  --size-2: 4px;
  --size-3: 8px;
  --size-4: 12px;
  --size-5: 16px;
  --size-6: 24px;
  --size-7: 32px;
  --size-8: 40px;
  --size-9: 48px;
  --size-10: 64px;
  --size-11: 80px;
  --size-12: 96px;
  --size-13: 160px;
  --elevation-1: 0 2px 6px 0 rgba(0,0,0,.2);
  --elevation-2: 0 2px 6px 0 rgba(0,0,0,.3);
  --motion-entrance-productive: cubic-bezier(0, 0, 0.38, 0.9);
  --motion-entrance-expressive: cubic-bezier(0, 0, 0.3, 1);
  --motion-standard-productive: cubic-bezier(0.2, 0, 0.38, 0.9);
  --motion-standard-expressive: cubic-bezier(0.4, 0.14, 0.3, 1);
  --motion-exit-productive: cubic-bezier(0.2, 0, 1, 0.9);
  --motion-exit-expressive: cubic-bezier(0.4, 0.14, 1, 1);
  --duration-fast-1: 70ms;
  --duration-fast-2: 110ms;
  --duration-moderate-1: 150ms;
  --duration-moderate-2: 240ms;
  --duration-slow-1: 400ms;
  --duration-slow-2: 700ms;
  --font-scale-000: .75rem;
  --font-scale-00: .875rem;
  --font-scale-0: 1rem;
  --font-scale-1: 1.125rem;
  --font-scale-2: 1.25rem;
  --font-scale-3: 1.5rem;
  --font-scale-4: 1.75rem;
  --font-scale-5: 2rem;
  --font-scale-6: 2.25rem;
  --font-scale-7: 2.625rem;
  --font-scale-8: 3rem;
  --font-scale-9: 3.375rem;
  --font-scale-10: 3.75rem;
}
body {
  --white:
    253,
    254,
    254;
  --gray-10:
    244,
    244,
    240;
  --gray-20:
    226,
    224,
    220;
  --gray-30:
    199,
    197,
    194;
  --gray-40:
    169,
    168,
    165;
  --gray-50:
    142,
    140,
    139;
  --gray-60:
    112,
    110,
    109;
  --gray-70:
    84,
    81,
    81;
  --gray-80:
    58,
    56,
    56;
  --gray-90:
    38,
    38,
    37;
  --gray-100:
    22,
    22,
    22;
  --black:
    0,
    0,
    0;
  --cyan-10: hsl(90, 15.4%, 94.9%);
  --cyan-20: hsl(98.2, 15.9%, 86.5%);
  --cyan-30: hsl(155, 18.7%, 74.9%);
  --cyan-40: hsl(182.2, 26.2%, 59.6%);
  --cyan-50: hsl(187.4, 22.4%, 50%);
  --cyan-60: hsl(199.5, 38.8%, 40.4%);
  --cyan-70: hsl(202.8, 57.6%, 29.6%);
  --cyan-80: hsl(200.9, 100%, 18%);
  --cyan-90: hsl(206.1, 100%, 13.5%);
  --cyan-100:hsl(214.8, 100%, 9.8%);
  --cyan: var(--cyan-50);
  --red-10: hsl(12.6, 100%, 96.3%);
  --red-20: hsl(12.9, 100%, 90%);
  --red-30: hsl(12, 100%, 81.4%);
  --red-40: hsl(11.7, 94.7%, 70.4%);
  --red-50: hsl(11.2, 88.7%, 58.4%);
  --red-60: hsl(9.6, 66.2%, 46.5%);
  --red-70: hsl(358, 63.4%, 37.5%);
  --red-80: hsl(342.1, 82.5%, 24.7%);
  --red-90: hsl(346.4, 100%, 16.5%);
  --red-100:hsl(357.8, 100%, 10.6%);
  --red: var(--red-60);
  --orange-10: hsl(28.7, 85.2%, 94.7%);
  --orange-20: hsl(27.8, 79.4%, 86.7%);
  --orange-30: hsl(30.2, 98.6%, 72%);
  --orange-40: hsl(24.4, 90.8%, 61.8%);
  --orange-50: hsl(18.3, 75%, 54.5%);
  --orange-60: hsl(12.8, 60.5%, 45.7%);
  --orange-70: hsl(358, 63.4%, 37.5%);
  --orange-80: hsl(342.1, 82.5%, 24.7%);
  --orange-90: hsl(346.4, 100%, 16.5%);
  --orange-100:hsl(357.8, 100%, 10.6%);
  --orange: var(--orange-40);
  --yellow-10: hsl(44.3, 95.5%, 91.4%);
  --yellow-20: hsl(41.1, 88.1%, 80.2%);
  --yellow-30: hsl(40.2, 87.2%, 63.1%);
  --yellow-40: hsl(41.6, 70.4%, 49%);
  --yellow-50: hsl(42.7, 100%, 36.1%);
  --yellow-60: hsl(39.5, 100%, 29.8%);
  --yellow-70: hsl(35, 100%, 23.5%);
  --yellow-80: hsl(28.4, 100%, 18.2%);
  --yellow-90: hsl(19.4, 100%, 13.9%);
  --yellow-100:hsl(3.5, 100%, 10.2%);
  --yellow: var(--yellow-30);
  --green-10: hsl(77.6, 48.6%, 93.1%);
  --green-20: hsl(76.6, 48.5%, 81%);
  --green-30: hsl(83.5, 44%, 67.1%);
  --green-40: hsl(96.9, 31.8%, 56.9%);
  --green-50: hsl(114.6, 22.4%, 49%);
  --green-60: hsl(139, 32.3%, 36.5%);
  --green-70: hsl(144.9, 52.8%, 24.1%);
  --green-80: hsl(148.2, 100%, 12.9%);
  --green-90: hsl(134.7, 100%, 8.8%);
  --green-100:hsl(120, 100%, 5.3%);
  --green: var(--green-50);
  --viridian-10: hsl(96, 18.5%, 94.7%);
  --viridian-20: hsl(100, 21.1%, 86.1%);
  --viridian-30: hsl(111.8, 17.5%, 75.3%);
  --viridian-40: hsl(144.3, 19%, 61.8%);
  --viridian-50: hsl(151.5, 24.9%, 48%);
  --viridian-60: hsl(161, 34.8%, 35.5%);
  --viridian-70: hsl(165.7, 52.1%, 23.7%);
  --viridian-80: hsl(168, 100%, 12.7%);
  --viridian-90: hsl(163.6, 100%, 8.6%);
  --viridian-100:hsl(156.9, 100%, 5.1%);
  --viridian: var(--viridian-50);
  --blue-10: hsl(195, 16.7%, 95.3%);
  --blue-20: hsl(190.9, 16.4%, 86.9%);
  --blue-30: hsl(190.4, 18.7%, 75.9%);
  --blue-40: hsl(203.1, 36.3%, 64.9%);
  --blue-50: hsl(208.1, 34.2%, 55.9%);
  --blue-60: hsl(217.1, 31.4%, 47.5%);
  --blue-70: hsl(215.9, 45.1%, 35.7%);
  --blue-80: hsl(216.3, 59.4%, 25.1%);
  --blue-90: hsl(215, 83.7%, 16.9%);
  --blue-100:hsl(221, 100%, 11.8%);
  --blue: var(--blue-60);
  --pink-10: hsl(352.5, 100%, 96.9%);
  --pink-20: hsl(355.6, 100%, 92%);
  --pink-30: hsl(352.8, 94.9%, 84.5%);
  --pink-40: hsl(351.4, 86.2%, 74.5%);
  --pink-50: hsl(348.5, 79.3%, 63.9%);
  --pink-60: hsl(343.4, 62.8%, 49.6%);
  --pink-70: hsl(339.4, 71.6%, 35.9%);
  --pink-80: hsl(342.1, 82.5%, 24.7%);
  --pink-90: hsl(346.4, 100%, 16.5%);
  --pink-100:hsl(357.8, 100%, 10.6%);
  --pink: var(--pink-50);
  --pistachio-10: hsl(69.2, 37.1%, 93.1%);
  --pistachio-20: hsl(67.1, 37.8%, 82.4%);
  --pistachio-30: hsl(64.8, 36.9%, 67.1%);
  --pistachio-40: hsl(63.5, 42.6%, 47.8%);
  --pistachio-50: hsl(63, 70.9%, 33.7%);
  --pistachio-60: hsl(62.1, 100%, 22.7%);
  --pistachio-70: hsl(61.4, 100%, 16.7%);
  --pistachio-80: hsl(60, 100%, 11.6%);
  --pistachio-90: hsl(58.5, 100%, 7.8%);
  --pistachio-100:hsl(52.8, 100%, 4.9%);
  --pistachio: var(--pistachio-50);
  --lavender-10: hsl(264deg, 63%, 97%);
  --lavender-20: hsl(264deg, 64%, 91%);
  --lavender-30: hsl(264deg, 61%, 84%);
  --lavender-40: hsl(266deg, 58%, 75%);
  --lavender-50: hsl(268deg, 57%, 66%);
  --lavender-60: hsl(270deg, 55%, 56%);
  --lavender-70: hsl(271deg, 57%, 43%);
  --lavender-80: hsl(275deg, 89%, 29%);
  --lavender-90: hsl(273deg, 100%, 21%);
  --lavender-100: hsl(266deg, 100%, 15%);
  --lavender: var(--lavender-50);
}
.theme-light {
  --shadow-color: 0deg 5% 59%;
  --input-shadow:
    inset 0 0 0 1px rgba(0, 0, 0, 0.12),
    0px 0.5px 0.4px hsl(var(--shadow-color) / 0.47),
    0px 0.8px 0.7px -1px hsl(var(--shadow-color) / 0.4),
    0px 1.9px 1.7px -2px hsl(var(--shadow-color) / 0.32),
    0px 4.5px 4px -3px hsl(var(--shadow-color) / 0.25),
    0.1px 9.4px 8.4px -4.1px hsl(var(--shadow-color) / 0.18);
  --input-shadow-hover:inset 0 0 0 1px rgba(0, 0, 0, 0.17),
    0px 0.5px 0.5px hsl(var(--shadow-color) / 0.47),
    0px 0.9px 0.8px -1px hsl(var(--shadow-color) / 0.4),
    0px 1.9px 1.7px -2px hsl(var(--shadow-color) / 0.33),
    0px 4.5px 4.1px -3px hsl(var(--shadow-color) / 0.26),
    0.1px 9.4px 8.5px -4px hsl(var(--shadow-color) / 0.19);
}
.theme-dark {
  --shadow-color: 0deg 0% 1%;
  --input-shadow:
    inset 0 0.5px 0.5px 0.5px rgba(255, 255, 255, 0.09),
    0px 0.5px 0.4px hsl(var(--shadow-color) / 0.47),
    0px 0.8px 0.7px -1px hsl(var(--shadow-color) / 0.4),
    0px 1.9px 1.7px -2px hsl(var(--shadow-color) / 0.32),
    0px 4.5px 4px -3px hsl(var(--shadow-color) / 0.25),
    0.1px 9.4px 8.4px -4.1px hsl(var(--shadow-color) / 0.18);
  --input-shadow-hover:inset 0 0.5px 0.5px 0.5px rgba(255, 255, 255, 0.09),
    0px 0.5px 0.5px hsl(var(--shadow-color) / 0.47),
    0px 0.9px 0.8px -1px hsl(var(--shadow-color) / 0.4),
    0px 1.9px 1.7px -2px hsl(var(--shadow-color) / 0.33),
    0px 4.5px 4.1px -3px hsl(var(--shadow-color) / 0.26),
    0.1px 9.4px 8.5px -4px hsl(var(--shadow-color) / 0.19);
}
body {
  --color-accent-rgb-l:
    246,
    141,
    69;
  --color-accent-rgb-d:
    102,
    153,
    97;
}
.theme-light,
.theme-light.sanctum-default-light,
.theme-light.sanctum-contrast-light,
.theme-light.sanctum-white {
  --color-accent-rgb: var(--color-accent-rgb-l);
  --background: rgb(var(--gray-10));
  --background-hover: rgba(var(--gray-50), .12);
  --background-active: rgba(var(--gray-50), .32);
  --background-selected: rgba(var(--gray-50), .2);
  --background-selected-hover: rgba(var(--gray-50), .32);
  --background-inverse: rgb(var(--gray-80));
  --background-inverse-hover: rgba(var(--gray-70), .5);
  --layer-1: rgb(var(--white));
  --layer-2: rgb(var(--gray-10));
  --layer-hover-1: rgb(var(--gray-10));
  --layer-hover-2: rgba(var(--gray-20), .5);
  --layer-active-1: rgb(var(--gray-30));
  --layer-active-2: rgb(var(--gray-30));
  --layer-selected-1: rgb(var(--gray-20));
  --layer-selected-2: rgb(var(--gray-20));
  --layer-selected-hover-1: rgba(var(--gray-30), .5);
  --layer-selected-hover-2: rgba(var(--gray-30), .5);
  --field-1: rgb(var(--white));
  --field-hover-1: rgb(var(--gray-20));
  --border-subtle-0: rgb(var(--gray-20));
  --border-subtle-1: rgb(var(--gray-20));
  --border-subtle-2: rgb(var(--gray-20));
  --border-subtle-selected-1: rgb(var(--gray-30));
  --border-subtle-selected-2: rgb(var(--gray-30));
  --border-strong-1: rgb(var(--gray-50));
  --border-strong-2: rgb(var(--gray-50));
  --border-inverse: rgb(var(--gray-100));
  --text-code: var(--pink-60);
  --text-primary: rgb(var(--gray-100));
  --text-secondary: rgb(var(--gray-70));
  --text-placeholder: rgb(var(--gray-40));
  --icon-primary: rgb(var(--gray-100));
  --icon-secondary: rgb(var(--gray-70));
  --icon-on-color: rgb(var(--white));
  --icon-inverse: rgb(var(--white));
  --focus: var(--cyan-60);
  --focus-inset: rgb(var(--white));
  --focus-inverse: rgb(var(--white));
  --interactive: rgb(var(--color-accent-rgb));
  --highlight: rgba(var(--color-accent-rgb), .7);
  --overlay: rgba(var(--gray-100), .5);
}
.theme-light.sanctum-white {
  --background: rgb(var(--white));
  --layer-1: rgb(var(--gray-10));
  --layer-2: rgb(var(--white));
  --layer-hover-1: rgba(var(--gray-20), .5);
  --highlight: rgba(var(--color-accent-rgb), .7);
  --field-1: rgb(var(--gray-10));
  --field-hover-1: rgb(var(--white));
}
.theme-light.sanctum-contrast-light {
  --background-primary: rgb(var(--white));
}
.theme-dark,
.theme-dark.sanctum-default-dark,
.theme-dark.sanctum-contrast-dark,
.theme-dark.sanctum-black {
  --color-accent-rgb: var(--color-accent-rgb-d);
  --background: rgb(var(--gray-100));
  --background-hover: rgba(var(--gray-50), .16);
  --background-active: rgba(var(--gray-50), .40);
  --background-selected: rgba(var(--gray-50), .24);
  --background-selected-hover: rgba(var(--gray-50), .32);
  --background-inverse: rgb(var(--gray-10));
  --background-inverse-hover: rgba(var(--gray-20), .5);
  --layer-1: rgb(var(--gray-90));
  --layer-2: rgb(var(--gray-80));
  --layer-hover-1: rgba(var(--gray-80), .5);
  --layer-hover-2: rgba(var(--gray-70), .5);
  --layer-active-1: rgb(var(--gray-70));
  --layer-active-2: rgb(var(--gray-60));
  --layer-selected-1: rgb(var(--gray-80));
  --layer-selected-2: rgb(var(--gray-70));
  --layer-selected-hover-1: rgba(var(--gray-70), .5);
  --layer-selected-hover-2: rgba(var(--gray-60), .5);
  --field-1: rgb(var(--gray-90));
  --field-hover-1: rgb(var(--gray-80));
  --border-subtle-0: rgb(var(--gray-80));
  --border-subtle-1: rgb(var(--gray-80));
  --border-subtle-2: rgb(var(--gray-70));
  --border-subtle-selected-1: rgb(var(--gray-70));
  --border-subtle-selected-2: rgb(var(--gray-60));
  --border-strong-1: rgb(var(--gray-60));
  --border-strong-2: rgb(var(--gray-50));
  --border-inverse: rgb(var(--gray-10));
  --text-code: var(--pink-50);
  --text-primary: rgb(var(--gray-10));
  --text-secondary: rgb(var(--gray-30));
  --text-placeholder: rgb(var(--gray-60));
  --icon-primary: rgb(var(--gray-10));
  --icon-secondary: rgb(var(--gray-30));
  --icon-on-color: rgb(var(--white));
  --icon-inverse: rgb(var(--gray-100));
  --focus: var(--cyan-60);
  --focus-inset: rgb(var(--gray-100));
  --focus-inverse: rgb(var(--white));
  --interactive: rgb(var(--color-accent-rgb));
  --highlight: rgba(var(--color-accent-rgb), .7);
  --overlay: rgba(var(--gray-100), .7);
}
.theme-dark.sanctum-contrast-dark {
  --background: rgb(var(--gray-100));
  --background-secondary: rgb(var(--gray-90));
  --background-secondary-alt: rgb(var(--gray-90));
}
.theme-dark.sanctum-black {
  --background: rgb(var(--black));
  --layer-1: rgb(var(--gray-100));
  --layer-2: rgb(var(--gray-80));
  --layer-hover-1: rgba(var(--gray-90), .5);
  --layer-hover-2: rgba(var(--gray-80), .5);
  --layer-active-1: rgb(var(--gray-80));
  --layer-active-2: rgb(var(--gray-70));
  --layer-selected-1: rgb(var(--gray-90));
  --layer-selected-2: rgb(var(--gray-80));
  --layer-selected-hover-1: rgba(var(--gray-80), .5);
  --layer-selected-hover-2: rgba(var(--gray-70), .5);
  --field-1: rgb(var(--gray-100));
  --field-hover-1: rgb(var(--gray-90));
  --border-subtle-0: rgb(var(--gray-90));
  --border-subtle-1: rgb(var(--gray-90));
  --border-subtle-2: rgb(var(--gray-80));
  --border-subtle-selected-1: rgb(var(--gray-80));
  --border-subtle-selected-2: rgb(var(--gray-70));
  --border-strong-1: rgb(var(--gray-70));
  --border-strong-2: rgb(var(--gray-60));
  --text-code: var(--pink-60);
  --text-primary: rgb(var(--gray-30));
  --text-secondary: rgb(var(--gray-50));
  --text-placeholder: rgb(var(--gray-80));
  --highlight: rgba(var(--color-accent-rgb), .7);
}
.theme-light,
.theme-dark {
  --color-red-rgb:
    197,
    65,
    40;
  --color-green-rgb:
    102,
    153,
    97;
  --color-yellow-rgb:
    243,
    189,
    79;
  --text-highlight-bg: var(--highlight);
  --text-highlight-bg-active: var(--highlight);
  --color-accent: var(--interactive);
  --color-accent-1: var(--interactive);
  --color-accent-2: var(--interactive);
  --background-primary: var(--background);
  --background-primary-alt: var(--layer-1);
  --background-secondary: var(--background);
  --background-secondary-alt: var(--background);
  --background-modifier-hover: var(--background-hover);
  --background-modifier-active-hover: rgba(var(--color-accent-rgb), 0.15);
  --background-modifier-border: var(--border-subtle-0);
  --background-modifier-border-hover: var(--border-subtle-1);
  --background-modifier-border-focus: var(--focus);
  --background-modifier-error-rgb: var(--color-red-rgb);
  --background-modifier-error: var(--red);
  --background-modifier-error-hover: var(--red);
  --background-modifier-success-rgb: var(--color-green-rgb);
  --background-modifier-success: var(--green);
  --background-modifier-message: var(--layer-1);
  --background-modifier-form-field: var(--field-1);
  --text-normal: var(--text-primary);
  --text-muted: var(--text-secondary);
  --text-faint: var(--text-placeholder);
  --text-on-accent: rgb(var(--white));
  --text-error: var(--red);
  --text-success: var(--green);
  --text-selection: rgba(var(--color-accent-rgb), .5);
}
body {
  --radius-s: 4px;
  --radius-m: 8px;
  --radius-l: 10px;
  --accent-h: 24.4;
  --accent-s: 90.8%;
  --accent-l: 61.8%;
}
body {
  --blockquote-border-thickness: 0px;
  --blockquote-size: .9rem;
  --blockquote-font-style: inherit;
  --blockquote-color: inherit;
  --blockquote-background-color: inherit;
  --blockquote-border-color: var(--background-modifier-border);
}
.markdown-rendered blockquote {
  font-size: var(--blockquote-size);
  color: var(--blockquote-color);
  font-style: var(--blockquote-font-style);
  background-color: var(--blockquote-background-color);
  border-left: var(--blockquote-border-thickness) solid var(--blockquote-border-color);
  padding: 0 0 0 var(--size-7);
  margin-inline-start: 0;
  margin-inline-end: 0;
  quotes: "\201c" "\201d" "\2018" "\2019";
}
.blockquote-marker .markdown-rendered blockquote {
  border-left: none;
}
.blockquote-marker .markdown-rendered blockquote:before {
  content: "";
  display: block;
  margin-bottom: 4px;
  height: var(--blockquote-border-thickness);
  width: 32px;
  background: var(--blockquote-border-color);
  background-size: 16px 2px;
}
.blockquote-border .markdown-rendered blockquote {
  padding-top: 16px;
  padding-bottom: 16px;
  border-top: var(--blockquote-border-thickness) solid var(--blockquote-border-color);
  border-bottom: var(--blockquote-border-thickness) solid var(--blockquote-border-color);
  border-left: none;
}
.blockquote-border .markdown-rendered blockquote::before {
  content: none;
  width: 0;
  background: none;
}
.cm-s-obsidian span.cm-quote {
  color: var(--blockquote-color);
}
.markdown-source-view.mod-cm6.is-live-preview .HyperMD-quote {
  font-style: var(--blockquote-style);
  border: 0 solid var(--blockquote-border-color);
  border-left-width: var(--blockquote-border-thickness);
  background-color: var(--blockquote-background-color);
}
.blockquote-marker .markdown-source-view.mod-cm6.is-live-preview .HyperMD-quote {
  border-left: none;
}
.blockquote-marker .markdown-source-view.mod-cm6 .cm-blockquote-border {
  border-left: none;
}
body {
  --callout-radius: var(--radius-s);
  --callout-border-opacity: 30%;
  --callout-border-width: 0px;
  --callout-padding: var(--size-4-4) var(--size-4-3) var(--size-4-3) var(--size-4-6);
  --callout-radius: var(--radius-s);
  --callout-title-padding: 0;
  --callout-title-background: transparent;
  --callout-title-size: inherit;
  --callout-content-padding: 0;
}
.callout {
  border: var(--callout-border-width) solid rgba(var(--callout-color), var(--callout-border-opacity));
  background-color: rgba(var(--callout-color), 0.2);
  padding: 16px 12px 16px 12px;
}
.callout .callout-icon {
  align-self: baseline;
}
.callout .callout-icon svg {
  height: var(--icon-s);
  width: var(--icon-s);
}
.callout .callout-content {
  padding: 0 12px 0 24px;
}
.callout[data-callout=annotation],
.callout[data-callout=note] {
  --callout-color:
    104,
    145,
    181;
}
.callout[data-callout=annotation] .callout-icon,
.callout[data-callout=note] .callout-icon {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28.828 3.172a4.094 4.094 0 0 0-5.656 0L4.05 22.292A6.954 6.954 0 0 0 2 27.242V30h2.756a6.952 6.952 0 0 0 4.95-2.05L28.828 8.829a3.999 3.999 0 0 0 0-5.657ZM10.91 18.26l2.829 2.829-2.122 2.121-2.828-2.828Zm-2.619 8.276A4.966 4.966 0 0 1 4.756 28H4v-.759a4.967 4.967 0 0 1 1.464-3.535l1.91-1.91 2.829 2.828ZM27.415 7.414l-12.261 12.26-2.829-2.828 12.262-12.26a2.047 2.047 0 0 1 2.828 0 2 2 0 0 1 0 2.828Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
.callout[data-callout=abstract],
.callout[data-callout=summary],
.callout[data-callout=tldr] {
  --callout-color:
    104,
    145,
    181;
}
.callout[data-callout=abstract] .callout-icon,
.callout[data-callout=summary] .callout-icon,
.callout[data-callout=tldr] .callout-icon {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M14 23h8v2h-8zm-4 0h2v2h-2zm4-5h8v2h-8zm-4 0h2v2h-2zm4-5h8v2h-8zm-4 0h2v2h-2z'/%3E%3Cpath d='M25 5h-3V4a2 2 0 0 0-2-2h-8a2 2 0 0 0-2 2v1H7a2 2 0 0 0-2 2v21a2 2 0 0 0 2 2h18a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2ZM12 4h8v4h-8Zm13 24H7V7h3v3h12V7h3Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
.callout[data-callout=info],
.callout[data-callout=todo] {
  --callout-color:
    99,
    149,
    156;
}
.callout[data-callout=info] .callout-icon {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E .cls-1 %7B fill: none; %7D %3C/style%3E%3C/defs%3E%3Cpolygon points='17 22 17 14 13 14 13 16 15 16 15 22 12 22 12 24 20 24 20 22 17 22'/%3E%3Cpath d='M16,8a1.5,1.5,0,1,0,1.5,1.5A1.5,1.5,0,0,0,16,8Z'/%3E%3Cpath d='M16,30A14,14,0,1,1,30,16,14,14,0,0,1,16,30ZM16,4A12,12,0,1,0,28,16,12,12,0,0,0,16,4Z'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E");
}
.callout[data-callout=todo] .callout-icon {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='m14 21.414-5-5.001L10.413 15 14 18.586 21.585 11 23 12.415l-9 8.999z'/%3E%3Cpath d='M16 2a14 14 0 1 0 14 14A14 14 0 0 0 16 2Zm0 26a12 12 0 1 1 12-12 12 12 0 0 1-12 12Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
.callout[data-callout=tip],
.callout[data-callout=hint],
.callout[data-callout=important] {
  --callout-color:
    92,
    153,
    124;
}
.callout:where([data-callout=tip], [data-callout=hint], [data-callout=idea]) .callout-icon {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M11 24h10v2H11zm2 4h6v2h-6zm3-26A10 10 0 0 0 6 12a9.19 9.19 0 0 0 3.46 7.62c1 .93 1.54 1.46 1.54 2.38h2c0-1.84-1.11-2.87-2.19-3.86A7.2 7.2 0 0 1 8 12a8 8 0 0 1 16 0 7.2 7.2 0 0 1-2.82 6.14c-1.07 1-2.18 2-2.18 3.86h2c0-.92.53-1.45 1.54-2.39A9.18 9.18 0 0 0 26 12 10 10 0 0 0 16 2Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
.callout[data-callout=important] .callout-icon {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:none;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3Ewarning%3C/title%3E%3Cpath d='M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z' transform='translate(0 0)'/%3E%3Crect x='15' y='8' width='2' height='11'/%3E%3Cpath d='M16,22a1.5,1.5,0,1,0,1.5,1.5A1.5,1.5,0,0,0,16,22Z' transform='translate(0 0)'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E");
}
.callout[data-callout=location],
.callout[data-callout=site],
.callout[data-callout=pros],
.callout[data-callout=positive],
.callout[data-callout=success],
.callout[data-callout=check],
.callout[data-callout=done] {
  --callout-color:
    102,
    153,
    97;
}
.callout:where([data-callout=success], [data-callout=check], [data-callout=done]) .callout-icon {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='Layer_1' data-name='Layer 1' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E .cls-1 %7B fill: none; %7D %3C/style%3E%3C/defs%3E%3Cpolygon points='13 24 4 15 5.414 13.586 13 21.171 26.586 7.586 28 9 13 24'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E");
}
.callout:where([data-callout=pros], [data-callout=positive]) .callout-icon {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E .cls-1 %7B fill: none; %7D %3C/style%3E%3C/defs%3E%3Cpath d='M26,12H20V6a3.0033,3.0033,0,0,0-3-3H14.8672a2.0094,2.0094,0,0,0-1.98,1.7173l-.8453,5.9165L8.4648,16H2V30H23a7.0078,7.0078,0,0,0,7-7V16A4.0045,4.0045,0,0,0,26,12ZM8,28H4V18H8Zm20-5a5.0057,5.0057,0,0,1-5,5H10V17.3027l3.9578-5.9365L14.8672,5H17a1.0008,1.0008,0,0,1,1,1v8h8a2.0025,2.0025,0,0,1,2,2Z' transform='translate(0 0)'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E");
}
.callout:where([data-callout=site], [data-callout=location]) .callout-icon {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E .cls-1 %7B fill: none; %7D %3C/style%3E%3C/defs%3E%3Ctitle%3Elocation%3C/title%3E%3Cpath d='M16,18a5,5,0,1,1,5-5A5.0057,5.0057,0,0,1,16,18Zm0-8a3,3,0,1,0,3,3A3.0033,3.0033,0,0,0,16,10Z'/%3E%3Cpath d='M16,30,7.5645,20.0513c-.0479-.0571-.3482-.4515-.3482-.4515A10.8888,10.8888,0,0,1,5,13a11,11,0,0,1,22,0,10.8844,10.8844,0,0,1-2.2148,6.5973l-.0015.0025s-.3.3944-.3447.4474ZM8.8125,18.395c.001.0007.2334.3082.2866.3744L16,26.9079l6.91-8.15c.0439-.0552.2783-.3649.2788-.3657A8.901,8.901,0,0,0,25,13,9,9,0,1,0,7,13a8.9054,8.9054,0,0,0,1.8125,5.395Z'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32' transform='translate(0 32) rotate(-90)'/%3E%3C/svg%3E");
}
.callout[data-callout=cue],
.callout[data-callout=question],
.callout[data-callout=help],
.callout[data-callout=faq] {
  --callout-color:
    141,
    147,
    25;
}
.callout[data-callout=cue] .callout-icon,
.callout[data-callout=question] .callout-icon,
.callout[data-callout=help] .callout-icon,
.callout[data-callout=faq] .callout-icon {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:none;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3Ehelp%3C/title%3E%3Cpath d='M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z'/%3E%3Ccircle cx='16' cy='23.5' r='1.5'/%3E%3Cpath d='M17,8H15.5A4.49,4.49,0,0,0,11,12.5V13h2v-.5A2.5,2.5,0,0,1,15.5,10H17a2.5,2.5,0,0,1,0,5H15v4.5h2V17a4.5,4.5,0,0,0,0-9Z'/%3E%3Crect class='cls-1' width='32' height='32'/%3E%3C/svg%3E");
}
.callout[data-callout=alarm],
.callout[data-callout=reminder],
.callout[data-callout=notification],
.callout[data-callout=idea],
.callout[data-callout=win],
.callout[data-callout=reward],
.callout[data-callout=warning],
.callout[data-callout=caution],
.callout[data-callout=attention] {
  --callout-color:
    184,
    131,
    0;
}
.callout:where([data-callout=warning], [data-callout=caution], [data-callout=attention]) .callout-icon {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E .cls-1 %7B fill: none; %7D %3C/style%3E%3C/defs%3E%3Cpath d='M16,23a1.5,1.5,0,1,0,1.5,1.5A1.5,1.5,0,0,0,16,23Z'/%3E%3Crect x='15' y='12' width='2' height='9'/%3E%3Cpath d='M29,30H3a1,1,0,0,1-.8872-1.4614l13-25a1,1,0,0,1,1.7744,0l13,25A1,1,0,0,1,29,30ZM4.6507,28H27.3493l.002-.0033L16.002,6.1714h-.004L4.6487,27.9967Z'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E");
}
.callout:where([data-callout=win], [data-callout=reward]) .callout-icon {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:none;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3Etrophy%3C/title%3E%3Cpath d='M26,7H24V6a2.0023,2.0023,0,0,0-2-2H10A2.0023,2.0023,0,0,0,8,6V7H6A2.0023,2.0023,0,0,0,4,9v3a4.0045,4.0045,0,0,0,4,4h.322A8.1689,8.1689,0,0,0,15,21.9341V26H10v2H22V26H17V21.9311A7.9661,7.9661,0,0,0,23.74,16H24a4.0045,4.0045,0,0,0,4-4V9A2.0023,2.0023,0,0,0,26,7ZM8,14a2.0023,2.0023,0,0,1-2-2V9H8Zm14,0a6,6,0,0,1-6.1855,5.9971A6.1991,6.1991,0,0,1,10,13.7065V6H22Zm4-2a2.0023,2.0023,0,0,1-2,2V9h2Z'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E");
}
.callout[data-callout=alarm] .callout-icon {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23000%7D%3C/style%3E%3C/defs%3E%3Cpath d='M16 28a11 11 0 1 1 11-11 11 11 0 0 1-11 11Zm0-20a9 9 0 1 0 9 9 9 9 0 0 0-9-9Z'/%3E%3Cpath d='M18.59 21 15 17.41V11h2v5.58l3 3.01L18.59 21z'/%3E%3Cpath d='M3.96 5.5h5.07v2H3.96z' class='cls-1' transform='rotate(-45.06 6.502 6.497)'/%3E%3Cpath d='M24.5 3.96h2v5.07h-2z' class='cls-1' transform='rotate(-44.94 25.5 6.498)'/%3E%3Cpath id='_Transparent_Rectangle_' fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
.callout:where([data-callout=alarm], [data-callout=reminder], [data-callout=notification]) .callout-icon {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E .cls-1 %7B fill: none; %7D %3C/style%3E%3C/defs%3E%3Cpath d='M28.7071,19.293,26,16.5859V13a10.0136,10.0136,0,0,0-9-9.9492V1H15V3.0508A10.0136,10.0136,0,0,0,6,13v3.5859L3.2929,19.293A1,1,0,0,0,3,20v3a1,1,0,0,0,1,1h7v.7768a5.152,5.152,0,0,0,4.5,5.1987A5.0057,5.0057,0,0,0,21,25V24h7a1,1,0,0,0,1-1V20A1,1,0,0,0,28.7071,19.293ZM19,25a3,3,0,0,1-6,0V24h6Zm8-3H5V20.4141L7.707,17.707A1,1,0,0,0,8,17V13a8,8,0,0,1,16,0v4a1,1,0,0,0,.293.707L27,20.4141Z'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E");
}
.callout[data-callout=failure],
.callout[data-callout=fail],
.callout[data-callout=missing] {
  --callout-color:
    226,
    105,
    52;
}
.callout[data-callout=failure] .callout-icon,
.callout[data-callout=fail] .callout-icon,
.callout[data-callout=missing] .callout-icon {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23000000;%7D.cls-2%7Bfill:none;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3Eclose%3C/title%3E%3Cpolygon class='cls-1' points='24 9.4 22.6 8 16 14.6 9.4 8 8 9.4 14.6 16 8 22.6 9.4 24 16 17.4 22.6 24 24 22.6 17.4 16 24 9.4'/%3E%3Crect class='cls-2' width='32' height='32'/%3E%3C/svg%3E");
  transform: scale(1.2);
}
.callout[data-callout=favourite],
.callout[data-callout=favorite],
.callout[data-callout=bookmark],
.callout[data-callout=cons],
.callout[data-callout=negative],
.callout[data-callout=danger],
.callout[data-callout=error] {
  --callout-color:
    243,
    90,
    55;
}
.callout:where([data-callout=danger], [data-callout=error]) .callout-icon {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:none;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3Ebolt%3C/title%3E%3Cpath d='M11.61,29.92a1,1,0,0,1-.6-1.07L12.83,17H8a1,1,0,0,1-1-1.23l3-13A1,1,0,0,1,11,2H21a1,1,0,0,1,.78.37,1,1,0,0,1,.2.85L20.25,11H25a1,1,0,0,1,.9.56,1,1,0,0,1-.11,1l-13,17A1,1,0,0,1,12,30,1.09,1.09,0,0,1,11.61,29.92ZM17.75,13l2-9H11.8L9.26,15h5.91L13.58,25.28,23,13Z'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32' transform='translate(32 32) rotate(-180)'/%3E%3C/svg%3E");
}
.callout:where([data-callout=cons], [data-callout=negative]) .callout-icon {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E .cls-1 %7B fill: none; %7D %3C/style%3E%3C/defs%3E%3Cpath d='M30,16V9a7.0078,7.0078,0,0,0-7-7H2V16H8.4648l3.5774,5.3662.8453,5.9165A2.0094,2.0094,0,0,0,14.8672,29H17a3.0033,3.0033,0,0,0,3-3V20h6A4.0045,4.0045,0,0,0,30,16ZM8,14H4V4H8Zm20,2a2.0025,2.0025,0,0,1-2,2H18v8a1.0008,1.0008,0,0,1-1,1H14.8672l-.9094-6.3662L10,14.6973V4H23a5.0057,5.0057,0,0,1,5,5Z'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E");
}
.callout[data-callout=bookmark] .callout-icon {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M24 4v22.75l-7.1-3.59-.9-.45-.9.45L8 26.75V4h16m0-2H8a2 2 0 0 0-2 2v26l10-5 10 5V4a2 2 0 0 0-2-2Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
.callout:where([data-callout=favorite], [data-callout=favourite]) .callout-icon {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:none;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3Efavorite%3C/title%3E%3Cpath d='M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E");
}
.callout[data-callout=debug],
.callout[data-callout=bug] {
  --callout-color:
    236,
    90,
    118;
}
.callout[data-callout=debug] .callout-icon,
.callout[data-callout=bug] .callout-icon {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:none;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3Edebug%3C/title%3E%3Cpath d='M29.83,20l.34-2L25,17.15V13c0-.08,0-.15,0-.23l5.06-1.36-.51-1.93-4.83,1.29A9,9,0,0,0,20,5V2H18V4.23a8.81,8.81,0,0,0-4,0V2H12V5a9,9,0,0,0-4.71,5.82L2.46,9.48,2,11.41,7,12.77c0,.08,0,.15,0,.23v4.15L1.84,18l.32,2L7,19.18a8.9,8.9,0,0,0,.82,3.57L3.29,27.29l1.42,1.42,4.19-4.2a9,9,0,0,0,14.2,0l4.19,4.2,1.42-1.42-4.54-4.54A8.9,8.9,0,0,0,25,19.18ZM15,25.92A7,7,0,0,1,9,19V13h6ZM9.29,11a7,7,0,0,1,13.42,0ZM23,19a7,7,0,0,1-6,6.92V13h6Z'/%3E%3Crect class='cls-1' width='32' height='32'/%3E%3C/svg%3E");
}
.callout[data-callout=event],
.callout[data-callout=reference],
.callout[data-callout=example] {
  --callout-color:
    165,
    119,
    218;
}
.callout[data-callout=example] .callout-icon {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:none;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3Elist%3C/title%3E%3Crect x='10' y='6' width='18' height='2'/%3E%3Crect x='10' y='24' width='18' height='2'/%3E%3Crect x='10' y='15' width='18' height='2'/%3E%3Crect x='4' y='15' width='2' height='2'/%3E%3Crect x='4' y='6' width='2' height='2'/%3E%3Crect x='4' y='24' width='2' height='2'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E");
}
.callout[data-callout=reference] .callout-icon {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M4 20v2h3.586L2 27.586 3.414 29 9 23.414V27h2v-7H4zm15-10h7v2h-7zm0 5h7v2h-7zm0 5h7v2h-7z'/%3E%3Cpath d='M28 5H4a2.002 2.002 0 0 0-2 2v10h2V7h11v20h13a2.002 2.002 0 0 0 2-2V7a2.002 2.002 0 0 0-2-2ZM17 25V7h11l.002 18Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
.callout[data-callout=event] .callout-icon {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E .cls-1 %7B fill: none; %7D %3C/style%3E%3C/defs%3E%3Cpath d='M26,4H22V2H20V4H12V2H10V4H6A2.0025,2.0025,0,0,0,4,6V26a2.0025,2.0025,0,0,0,2,2H26a2.0025,2.0025,0,0,0,2-2V6A2.0025,2.0025,0,0,0,26,4ZM6,6h4V8h2V6h8V8h2V6h4l0,4H6Zm0,6h5v6H6ZM19,26H13V20h6Zm0-8H13V12h6Zm2,8V20h5l.0012,6Z'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E");
}
.callout[data-callout=quote],
.callout[data-callout=cite] {
  --callout-color: var(--gray-50);
}
.callout[data-callout=quote] .callout-icon,
.callout[data-callout=cite] .callout-icon {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:none;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3Equotes%3C/title%3E%3Cpath d='M12,15H6.11A9,9,0,0,1,10,8.86l1.79-1.2L10.69,6,8.9,7.2A11,11,0,0,0,4,16.35V23a2,2,0,0,0,2,2h6a2,2,0,0,0,2-2V17A2,2,0,0,0,12,15Z'/%3E%3Cpath d='M26,15H20.11A9,9,0,0,1,24,8.86l1.79-1.2L24.7,6,22.9,7.2A11,11,0,0,0,18,16.35V23a2,2,0,0,0,2,2h6a2,2,0,0,0,2-2V17A2,2,0,0,0,26,15Z'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E");
}
.callout.is-collapsible .callout-fold svg.lucide-chevron-down {
  transform: scaleY(-1);
}
.callout.is-collapsed .callout-fold svg.lucide-chevron-down {
  transform: scaleY(1);
}
.callout-title .callout-title-inner {
  flex-grow: 1;
}
.code-border .markdown-rendered pre,
.code-border .markdown-rendered p > code {
  outline: 1px solid var(--border-subtle-0);
}
.theme-light {
  --code-background: var(--layer-hover-2);
}
.theme-light .markdown-rendered pre code {
  background-color: transparent;
}
body {
  --code-normal-inline: var(--pink);
}
.markdown-rendered code:not(pre code) {
  color: var(--code-normal-inline);
}
@keyframes outdent {
  from {
    margin-right: -26px;
    clip-path: polygon(0 0, 24px 0, 24px 36px, 0 36px);
  }
  to {
    margin-right: 4px;
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
  }
}
.markdown-rendered button.copy-code-button {
  margin: 4px;
  margin-right: -26px;
  padding-top: 8px;
  line-height: 1.35;
  will-change: margin-right, clip-path;
  clip-path: polygon(0 0, 24px 0, 24px 36px, 0 36px);
}
.markdown-rendered button.copy-code-button:hover {
  background-color: var(--background-secondary);
  animation: outdent var(--duration-moderate-2);
  margin-right: 4px;
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
}
.markdown-rendered button.copy-code-button:active {
  transform: scale(0.95);
  transition: scale var(--duration-moderate-2) var(--motion-expressive-standard);
}
.markdown-rendered button.copy-code-button::before {
  content: "\200b";
  display: inline-block;
  width: 14px;
  height: 14px;
  padding-right: 4px;
  background-color: var(--text-muted);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28 10v18H10V10h18m0-2H10a2 2 0 0 0-2 2v18a2 2 0 0 0 2 2h18a2 2 0 0 0 2-2V10a2 2 0 0 0-2-2Z'/%3E%3Cpath d='M4 18H2V4a2 2 0 0 1 2-2h14v2H4Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
  -webkit-mask-repeat: no-repeat;
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28 10v18H10V10h18m0-2H10a2 2 0 0 0-2 2v18a2 2 0 0 0 2 2h18a2 2 0 0 0 2-2V10a2 2 0 0 0-2-2Z'/%3E%3Cpath d='M4 18H2V4a2 2 0 0 1 2-2h14v2H4Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
  mask-repeat: no-repeat;
  vertical-align: -1px;
}
.markdown-source-view.mod-cm6 .code-block-flair:hover,
.markdown-source-view.mod-cm6 .edit-block-button:hover {
  background-color: var(--background-hover);
}
.theme-light {
  --code-normal: var(--text-muted);
  --code-comment: var(--text-faint);
  --code-punctuation: var(--text-muted);
  --code-tag: var(--red);
  --code-value: var(--lavender);
  --code-string: var(--green);
  --code-property: var(--cyan);
  --code-function: var(--orange);
  --code-keyword: var(--pink);
  --code-important: var(--lavender);
}
.theme-dark {
  --code-normal: var(--text-muted);
  --code-comment: var(--text-faint);
  --code-punctuation: var(--text-muted);
  --code-tag: var(--red-50);
  --code-value: var(--lavender);
  --code-string: var(--green-40);
  --code-property: var(--cyan);
  --code-function: var(--orange-30);
  --code-keyword: var(--pink);
  --code-important: var(--lavender);
}
body:not(.is-grabbing) .nav-file-title.is-being-dragged,
body:not(.is-grabbing) .nav-folder-title.is-being-dragged,
.nav-file-title.is-being-dragged,
.nav-folder-title.is-being-dragged {
  background-color: rgba(var(--color-accent-rgb), 0.5);
}
.workspace-drop-overlay:before {
  background-color: rgba(var(--color-accent-rgb), 0.5);
  opacity: 1;
}
.markdown-source-view.mod-cm6 .cm-gutters {
  padding-right: 0;
}
.markdown-source-view.mod-cm6 .cm-gutters .cm-lineNumbers .cm-gutterElement {
  min-width: 24px;
}
.markdown-source-view.mod-cm6 .cm-gutters .cm-lineNumbers .cm-gutterElement:not(.cm-active) {
  padding-right: 24px;
  opacity: 0.5;
}
.markdown-source-view.mod-cm6 .cm-gutters .cm-lineNumbers .cm-gutterElement.cm-active {
  padding-right: 24px;
  color: var(--text-muted);
  border-radius: var(--radius-s) 0 0 var(--radius-s);
}
body {
  --active-line-accent: transparent;
}
.markdown-source-view.mod-cm6 .cm-active:not(.HyperMD-codeblock) {
  background-color: var(--active-line-accent);
  border-radius: 0 var(--radius-s) var(--radius-s) 0;
}
body:not(.is-mobile) :is(.markdown-source-view.mod-cm6 .cm-scroller, .markdown-reading-view .markdown-preview-view) {
  padding-left: var(--size-8);
}
body {
  --bold-weight: var(--font-semibold);
  --bold-color: inherit;
  --italic-color: inherit;
}
.search-result-file-matched-text,
.markdown-rendered mark,
.cm-s-obsidian span.cm-highlight {
  padding: 2px 4px;
  border-radius: var(--radius-s);
  -webkit-box-decoration-break: clone;
  box-decoration-break: clone;
}
body {
  --hr-color: var(--background-modifier-border);
  --hr-thickness: 1.5px;
}
body {
  --file-line-width: 40rem;
}
body {
  --embed-background: inherit;
  --embed-border-left: var(--embed-border-width) solid var(--embed-border-color);
  --embed-border-right: none;
  --embed-border-top: none;
  --embed-border-bottom: none;
  --embed-padding: 0 0 0 24px;
  --embed-font-style: inherit;
  --embed-border-width: 1px;
  --embed-border-color: var(--interactive-accent);
}
body:not(.is-mobile) .markdown-rendered .markdown-embed {
  border-radius: var(--radius-s);
}
body:not(.is-mobile) .markdown-rendered .markdown-embed .markdown-rendered {
  padding-left: 24px;
}
body.clean-embeds {
  --embed-background: inherit;
  --embed-border-left: 0px solid var(--interactive-accent);
  --embed-border-right: none;
  --embed-border-top: none;
  --embed-border-bottom: none;
  --embed-padding: 0 0 0 0;
  --embed-font-style: inherit;
}
body.clean-embeds .markdown-rendered .markdown-embed {
  max-width: 100%;
}
body.clean-embeds .markdown-rendered .markdown-embed .markdown-rendered {
  padding-left: 0;
}
body {
  --vault-name-font-size: var(--font-ui-medium);
  --vault-name-font-weight: var(--font-medium);
  --vault-name-color: var(--text-normal);
  --nav-item-size: var(--font-ui-small);
  --nav-item-color: var(--text-muted);
  --nav-item-color-hover: var(--text-normal);
  --nav-item-color-active: var(--text-normal);
  --nav-item-color-selected: var(--text-normal);
  --nav-item-color-highlighted: var(--text-accent-hover);
  --nav-item-background-hover: var(--background-modifier-hover);
  --nav-item-background-active: var(--background-selected);
  --nav-item-background-selected: hsla(var(--color-accent-hsl), 0.2);
  --nav-item-weight: inherit;
  --nav-item-weight-hover: inherit;
  --nav-item-weight-active: inherit;
  --nav-item-white-space: nowrap;
}
.hide-vault-title .nav-folder.mod-root > .nav-folder-title {
  display: none;
}
.wrap-nav-titles {
  --nav-item-white-space: wrap;
}
body:not(.nav-folder-indicators) .nav-folder.is-collapsed .nav-folder-title-content::before,
body:not(.nav-folder-indicators) .nav-folder .nav-folder-title-content::before {
  display: inline-block;
  content: "";
  margin-right: 8px;
  vertical-align: text-bottom;
  height: 16px;
  width: 16px;
  background-color: currentColor;
}
body:not(.nav-folder-indicators) .nav-folder.is-collapsed .nav-folder-title .nav-folder-title-content::before {
  -webkit-mask: url("data:image/svg+xml;utf8,%3Csvg id='icon' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:none;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3Efolder%3C/title%3E%3Cpath d='M11.17,6l3.42,3.41.58.59H28V26H4V6h7.17m0-2H4A2,2,0,0,0,2,6V26a2,2,0,0,0,2,2H28a2,2,0,0,0,2-2V10a2,2,0,0,0-2-2H16L12.59,4.59A2,2,0,0,0,11.17,4Z' transform='translate(0)'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E") no-repeat;
}
body:not(.nav-folder-indicators) .nav-folder .nav-folder-title .nav-folder-title-content::before {
  -webkit-mask: url("data:image/svg+xml,%3Csvg id='icon' xmlns='http://www.w3.org/2000/svg' width='100%' height='100%' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E .cls-1 %7B fill: none; %7D %3C/style%3E%3C/defs%3E%3Cpath d='M28,8H20.8284L17.4143,4.5859A2,2,0,0,0,16,4H4A2,2,0,0,0,2,6V26a2,2,0,0,0,2,2H28a2,2,0,0,0,2-2V10A2,2,0,0,0,28,8ZM8,26V14h8v6.17l-2.59-2.58L12,19l5,5,5-5-1.41-1.41L18,20.17V14a2.0025,2.0025,0,0,0-2-2H8a2.0025,2.0025,0,0,0-2,2V26H4V6H16l4,4h8v2H22v2h6V26Z'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E") no-repeat;
}
body:not(.nav-folder-indicators) .nav-folder-collapse-indicator {
  display: none;
}
body:not(.nav-folder-indicators) .nav-folder-title {
  padding-left: 4px;
}
.markdown-source-view.mod-cm6 .cm-fold-indicator .collapse-indicator {
  right: -4px;
}
.markdown-source-view.mod-cm6 .cm-line:not(.cm-active):not(.HyperMD-task-line):not(.HyperMD-header):not(.HyperMD-list-line-1) .cm-fold-indicator .collapse-indicator {
  padding-right: 14px;
}
.markdown-source-view.mod-cm6 .cm-line.cm-active:not(.HyperMD-list-line) .cm-fold-indicator .collapse-indicator {
  right: 0px;
}
.markdown-source-view.mod-cm6 .cm-line.cm-active.HyperMD-list-line-1 .cm-fold-indicator .collapse-indicator {
  right: 2px;
}
.markdown-source-view.mod-cm6 .cm-line.HyperMD-list-line-1:not(.cm-active):not(.HyperMD-task-line) .cm-fold-indicator .collapse-indicator {
  right: -8px;
}
.markdown-source-view.mod-cm6 .cm-line.HyperMD-task-line:not(.HyperMD-list-line-1):not(.cm-active) .cm-fold-indicator .collapse-indicator {
  right: 0px;
}
.markdown-source-view.mod-cm6 .cm-line.HyperMD-task-line.HyperMD-list-line-1 .cm-fold-indicator .collapse-indicator {
  right: 4px;
}
.markdown-rendered li:not(.task-list-item) .list-collapse-indicator {
  margin-left: -40px;
}
.markdown-rendered li.task-list-item .list-collapse-indicator {
  margin-left: -42px;
}
.collapse-icon svg.svg-icon {
  transform: scale(1.4) scaleY(-1);
}
.is-collapsed .collapse-icon svg.svg-icon {
  transform: scale(1.4);
}
body svg * {
  stroke-linecap: auto;
  stroke-linejoin: auto;
  rx: 1px;
  ry: 1px;
}
body {
  --icon-xs: 16px;
  --icon-s: 16px;
  --icon-m: 18px;
  --icon-l: 18px;
  --icon-xs-stroke-width: 1px;
  --icon-s-stroke-width: 1px;
  --icon-m-stroke-width: 1.25px;
  --icon-l-stroke-width: 1.25px;
}
.theme-dark .markdown-rendered img:not(:hover),
.theme-dark .workspace-leaf-content img:not(:hover) {
  opacity: 70%;
  transition: opacity var(--duration-slow-2) var(--motion-entrance-expressive);
}
.theme-dark .markdown-rendered img:hover,
.theme-dark .workspace-leaf-content img:hover {
  transition: opacity var(--duration-slow-2) var(--motion-entrance-expressive);
}
.markdown-rendered img,
.workspace-leaf-content img {
  --xs: 30%;
  --s: 50%;
  --m: 80%;
  --l: 100%;
  --xl: 120%;
  --xxl: 150%;
  --width: 100%;
  --move: 0%;
}
.markdown-rendered img[alt~=-xs],
.workspace-leaf-content img[alt~=-xs] {
  --width: var(--xs);
  width: var(--width);
}
.markdown-rendered img[alt~=-s],
.workspace-leaf-content img[alt~=-s] {
  --width: var(--s);
  width: var(--width);
}
.markdown-rendered img[alt~=-m],
.workspace-leaf-content img[alt~=-m] {
  --width: var(--m);
  width: var(--width);
}
.markdown-rendered img[alt~=-l],
.workspace-leaf-content img[alt~=-l] {
  --width: var(--l);
  width: var(--width);
}
.markdown-rendered img[alt~=-xl],
.workspace-leaf-content img[alt~=-xl] {
  --width: var(--xl);
  min-width: var(--width);
}
.markdown-rendered img[alt~=-xxl],
.workspace-leaf-content img[alt~=-xxl] {
  --width: var(--xxl);
  min-width: var(--width);
}
.markdown-rendered img[alt~="+"],
.workspace-leaf-content img[alt~="+"] {
  --move: -10%;
}
.markdown-rendered img[alt~="++"],
.workspace-leaf-content img[alt~="++"] {
  --move: -20%;
}
.markdown-rendered img[alt~="+++"],
.workspace-leaf-content img[alt~="+++"] {
  --move: -30%;
}
.markdown-rendered img[alt~="++++"],
.workspace-leaf-content img[alt~="++++"] {
  --move: -40%;
}
.markdown-rendered img[alt~="+++++"],
.workspace-leaf-content img[alt~="+++++"] {
  --move: -50%;
}
.markdown-rendered img[alt~="-"],
.workspace-leaf-content img[alt~="-"] {
  --move: 10%;
}
.markdown-rendered img[alt~=--],
.workspace-leaf-content img[alt~=--] {
  --move: 20%;
}
.markdown-rendered img[alt~=---],
.workspace-leaf-content img[alt~=---] {
  --move: 30%;
}
.markdown-rendered img[alt~=----],
.workspace-leaf-content img[alt~=----] {
  --move: 40%;
}
.markdown-rendered img[alt~=-----],
.workspace-leaf-content img[alt~=-----] {
  --move: 50%;
}
.markdown-rendered img[alt*=left],
.workspace-leaf-content img[alt*=left] {
  float: left;
  clear: left;
  margin-right: 1rem;
  margin-left: calc(-50% + var(--move));
  margin-bottom: 4px;
  margin-top: 16px;
}
.markdown-rendered img[alt*=right],
.workspace-leaf-content img[alt*=right] {
  float: right;
  clear: right;
  margin-left: 1rem;
  margin-right: calc(-50% + var(--move));
  margin-bottom: 4px;
  margin-top: 16px;
}
.markdown-rendered img[alt*=center],
.markdown-rendered img[alt*=centre],
.workspace-leaf-content img[alt*=center],
.workspace-leaf-content img[alt*=centre] {
  display: block;
  margin-right: auto;
  margin-left: auto;
  margin-bottom: 4px;
  margin-top: 16px;
}
.theme-light,
.theme-dark {
  --indentation-guide: rgba(var(--mono-rgb-100), .15);
  --indentation-guide-active: rgba(var(--mono-rgb-100), 0.3);
}
.markdown-rendered.show-indentation-guide li > ul::before,
.markdown-rendered.show-indentation-guide li > ol::before {
  left: -13px;
  top: 0;
  bottom: 0;
  border-right: 1px solid var(--indentation-guide);
}
body {
  --link-color: var(--interactive);
  --link-color-hover: var(--interactive);
  --link-decoration: underline;
  --link-decoration-hover: underline;
  --link-external-color: var(--text-accent);
  --link-external-color-hover: var(--text-accent-hover);
  --link-external-decoration: underline;
  --link-external-decoration-hover: underline;
  --link-external-filter: none;
  --link-unresolved-color: var(--text-accent);
  --link-unresolved-opacity: 0.6;
  --link-unresolved-filter: none;
  --link-unresolved-decoration-style: unset;
  --link-unresolved-decoration-color: rgba(var(--interactive-accent-rgb), 0.6);
  --link-style: normal;
  --link-text-transform: none;
  --link-external-style: normal;
}
.markdown-rendered .internal-link.is-unresolved:hover,
.markdown-source-view.mod-cm6 .is-unresolved:hover {
  color: var(--link-color-hover);
  text-transform: var(--link-text-transform);
}
.markdown-rendered .internal-link,
.markdown-source-view .cm-hmd-internal-link {
  font-style: var(--link-style);
  text-transform: var(--link-text-transform);
}
.markdown-rendered .external-link,
.markdown-source-view .cm-link,
.markdown-source-view .cm-url .cm-underline {
  font-style: var(--link-external-style);
}
body {
  --list-indent: 1.5em;
  --list-spacing: 0.075em;
  --list-marker-color: var(--text-faint);
  --list-marker-color-hover: var(--text-muted);
  --list-marker-color-collapsed: var(--interactive);
  --list-bullet-border: none;
  --list-bullet-radius: 0;
  --list-bullet-size: 4px;
  --list-bullet-transform: none;
}
body {
  --list-bullet-width-em-dash: 10px;
  --list-bullet-height-em-dash: 1.5px;
  --list-bullet-width-hyphen: 5px;
  --list-bullet-height-hyphen: 1.5px;
  --list-bullet-width-bullet-operator: 1px;
  --list-bullet-height-bullet-operator: 1px;
  --list-bullet-size-ring: 3px;
  --list-bullet-width-triangle: 0px;
  --list-bullet-height-triangle: 0px;
  --list-bullet-border-triangle: 3px;
  --list-bullet-width-triangle: 6px;
  --checklist-done-decoration: line-through;
  --checklist-done-color: var(--text-faint);
  --checklist-done-strike-color: var(--text-faint);
}
ul > li.task-list-item[data-task=x] {
  text-decoration-color: var(--checklist-done-strike-color);
}
ul > li.task-list-item[data-task=X] {
  text-decoration: unset;
  color: unset;
}
:is(body, .default-marker-odd) ul > li .list-bullet:after,
:is(body, .default-marker-odd) ol > li > ul > li .list-bullet:after,
:is(body, .default-marker-odd) ul > li > ul > li > ul > li .list-bullet:after,
:is(body, .default-marker-odd) ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet:after,
:is(body, .default-marker-odd) ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet:after,
:is(body, .default-marker-odd) ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet:after {
  --list-bullet-radius: 0;
  border: none;
  background-color: var(--list-marker-color);
  width: var(--list-bullet-width-em-dash);
  height: var(--list-bullet-height-em-dash);
  transform: var(--list-bullet-transform);
}
:is(body, .default-marker-even) ul > li > ul > li .list-bullet::after,
:is(body, .default-marker-even) ol > li > ul > li > ul > li .list-bullet::after,
:is(body, .default-marker-even) ul > li > ul > li > ul > li > ul > li .list-bullet::after,
:is(body, .default-marker-even) ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet::after,
:is(body, .default-marker-even) ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet::after,
:is(body, .default-marker-even) ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet::after {
  --list-bullet-radius: 0;
  border: none;
  background-color: var(--list-marker-color);
  width: var(--list-bullet-size);
  height: var(--list-bullet-size);
  transform: var(--list-bullet-transform);
}
ol,
ol ol ol,
ol ol ol ol ol,
ol ol ol ol ol ol ol,
ol ol ol ol ol ol ol ol ol {
  list-style-type: decimal;
}
ol ol,
ol ol ol ol,
ol ol ol ol ol ol,
ol ol ol ol ol ol ol ol,
ol ol ol ol ol ol ol ol ol ol {
  list-style-type: lower-alpha;
}
.markdown-source-view.mod-cm6 .list-bullet:after {
  --list-bullet-radius: 50%;
  border-radius: var(--list-bullet-radius);
  width: var(--list-bullet-size);
  height: var(--list-bullet-size);
  border: var(--list-bullet-border);
}
.step-list-0 ol {
  list-style-type: none;
}
.step-list-0 ol {
  counter-reset: decimal;
}
.step-list-0 ol > li::before {
  content: "Step " counters(decimal, ".") ": ";
  counter-increment: decimal;
  color: var(--text-faint);
  margin-left: -1.1em;
}
.step-list-1 ol {
  list-style-type: none;
}
.step-list-1 ol {
  counter-reset: decimal;
}
.step-list-1 ol > li::before {
  content: counters(decimal, ".") ". ";
  counter-increment: decimal;
  color: var(--text-faint);
  margin-left: -1.1em;
}
.alternate-marker-odd-1 ul > li .list-bullet:after,
.alternate-marker-odd-1 ol > li > ul > li .list-bullet:after,
.alternate-marker-odd-1 ul > li > ul > li > ul > li .list-bullet:after,
.alternate-marker-odd-1 ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet:after,
.alternate-marker-odd-1 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet:after,
.alternate-marker-odd-1 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet:after,
.alternate-marker-even-2 ul > li > ul > li .list-bullet::after,
.alternate-marker-even-2 ol > li > ul > li > ul > li .list-bullet::after,
.alternate-marker-even-2 ul > li > ul > li > ul > li > ul > li .list-bullet::after,
.alternate-marker-even-2 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet::after,
.alternate-marker-even-2 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet::after,
.alternate-marker-even-2 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet::after {
  --list-bullet-radius: 0;
  width: var(--list-bullet-width-hyphen);
  height: var(--list-bullet-height-hyphen);
}
.alternate-marker-odd-2 ul > li .list-bullet:after,
.alternate-marker-odd-2 ol > li > ul > li .list-bullet:after,
.alternate-marker-odd-2 ul > li > ul > li > ul > li .list-bullet:after,
.alternate-marker-odd-2 ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet:after,
.alternate-marker-odd-2 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet:after,
.alternate-marker-odd-2 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet:after,
.alternate-marker-even-3 ul > li > ul > li .list-bullet::after,
.alternate-marker-even-3 ol > li > ul > li > ul > li .list-bullet::after,
.alternate-marker-even-3 ul > li > ul > li > ul > li > ul > li .list-bullet::after,
.alternate-marker-even-3 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet::after,
.alternate-marker-even-3 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet::after,
.alternate-marker-even-3 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet::after {
  --list-bullet-radius: 0;
  --list-bullet-width: 2px;
  --list-bullet-height: 2px;
  width: var(--list-bullet-width);
  height: var(--list-bullet-height);
}
.alternate-marker-odd-3 ul > li .list-bullet:after,
.alternate-marker-odd-3 ol > li > ul > li .list-bullet:after,
.alternate-marker-odd-3 ul > li > ul > li > ul > li .list-bullet:after,
.alternate-marker-odd-3 ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet:after,
.alternate-marker-odd-3 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet:after,
.alternate-marker-odd-3 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet:after {
  width: var(--list-bullet-size);
  height: var(--list-bullet-size);
}
.alternate-marker-odd-4 ul > li .list-bullet:after,
.alternate-marker-odd-4 ol > li > ul > li .list-bullet:after,
.alternate-marker-odd-4 ul > li > ul > li > ul > li .list-bullet:after,
.alternate-marker-odd-4 ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet:after,
.alternate-marker-odd-4 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet:after,
.alternate-marker-odd-4 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet:after,
.alternate-marker-even-4 ul > li > ul > li .list-bullet::after,
.alternate-marker-even-4 ol > li > ul > li > ul > li .list-bullet::after,
.alternate-marker-even-4 ul > li > ul > li > ul > li > ul > li .list-bullet::after,
.alternate-marker-even-4 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet::after,
.alternate-marker-even-4 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet::after,
.alternate-marker-even-4 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet::after {
  --list-bullet-radius: 50%;
  width: var(--list-bullet-size);
  height: var(--list-bullet-size);
}
.alternate-marker-odd-5 ul > li .list-bullet:after,
.alternate-marker-odd-5 ol > li > ul > li .list-bullet:after,
.alternate-marker-odd-5 ul > li > ul > li > ul > li .list-bullet:after,
.alternate-marker-odd-5 ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet:after,
.alternate-marker-odd-5 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet:after,
.alternate-marker-odd-5 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet:after,
.alternate-marker-even-5 ul > li > ul > li .list-bullet::after,
.alternate-marker-even-5 ol > li > ul > li > ul > li .list-bullet::after,
.alternate-marker-even-5 ul > li > ul > li > ul > li > ul > li .list-bullet::after,
.alternate-marker-even-5 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet::after,
.alternate-marker-even-5 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet::after,
.alternate-marker-even-5 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet::after {
  --list-bullet-radius: 50%;
  width: var(--list-bullet-size-ring);
  height: var(--list-bullet-size-ring);
  border: 1.5px solid var(--list-marker-color);
  background-color: transparent;
}
.alternate-marker-odd-6 ul > li .list-bullet:after,
.alternate-marker-odd-6 ol > li > ul > li .list-bullet:after,
.alternate-marker-odd-6 ul > li > ul > li > ul > li .list-bullet:after,
.alternate-marker-odd-6 ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet:after,
.alternate-marker-odd-6 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet:after,
.alternate-marker-odd-6 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet:after,
.alternate-marker-even-6 ul > li > ul > li .list-bullet::after,
.alternate-marker-even-6 ol > li > ul > li > ul > li .list-bullet::after,
.alternate-marker-even-6 ul > li > ul > li > ul > li > ul > li .list-bullet::after,
.alternate-marker-even-6 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet::after,
.alternate-marker-even-6 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet::after,
.alternate-marker-even-6 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet::after {
  --list-bullet-radius: 0;
  width: var(--list-bullet-width-triangle);
  height: var(--list-bullet-height-triangle);
  border-top: var(--list-bullet-border-triangle) solid transparent;
  border-left: var(--list-bullet-width-triangle) solid var(--list-marker-color);
  border-bottom: var(--list-bullet-border-triangle) solid transparent;
  background-color: transparent;
  transform: translateX(2px);
}
.alternate-marker-even-1 ul > li > ul > li .list-bullet::after,
.alternate-marker-even-1 ol > li > ul > li > ul > li .list-bullet::after,
.alternate-marker-even-1 ul > li > ul > li > ul > li > ul > li .list-bullet::after,
.alternate-marker-even-1 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet::after,
.alternate-marker-even-1 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet::after,
.alternate-marker-even-1 ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li .list-bullet::after {
  --list-bullet-radius: 0;
  background-color: var(--list-marker-color);
  width: var(--list-bullet-width-em-dash);
  height: var(--list-bullet-height-em-dash);
}
:is(body, .default-marker-even, .alternate-marker-even-1, .alternate-marker-even-2, .alternate-marker-even-3, .alternate-marker-even-4, .alternate-marker-even-5, .alternate-marker-even-6) ul > li > ul > li.is-collapsed .list-bullet:after,
:is(body, .default-marker-even, .alternate-marker-even-1, .alternate-marker-even-2, .alternate-marker-even-3, .alternate-marker-even-4, .alternate-marker-even-5, .alternate-marker-even-6) ol > li > ul > li > ul > li.is-collapsed .list-bullet:after,
:is(body, .default-marker-even, .alternate-marker-even-1, .alternate-marker-even-2, .alternate-marker-even-3, .alternate-marker-even-4, .alternate-marker-even-5, .alternate-marker-even-6) ul > li > ul > li > ul > li > ul > li.is-collapsed .list-bullet:after,
:is(body, .default-marker-even, .alternate-marker-even-1, .alternate-marker-even-2, .alternate-marker-even-3, .alternate-marker-even-4, .alternate-marker-even-5, .alternate-marker-even-6) ul > li > ul > li > ul > li > ul > li > ul > li > ul > li.is-collapsed .list-bullet:after,
:is(body, .default-marker-even, .alternate-marker-even-1, .alternate-marker-even-2, .alternate-marker-even-3, .alternate-marker-even-4, .alternate-marker-even-5, .alternate-marker-even-6) ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li.is-collapsed .list-bullet:after,
:is(body, .default-marker-even, .alternate-marker-even-1, .alternate-marker-even-2, .alternate-marker-even-3, .alternate-marker-even-4, .alternate-marker-even-5, .alternate-marker-even-6) ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li.is-collapsed .list-bullet:after,
:is(body, .default-marker-odd, .alternate-marker-odd-1, .alternate-marker-odd-2, .alternate-marker-odd-3, .alternate-marker-odd-4, .alternate-marker-odd-5, .alternate-marker-odd-6) ul > li.is-collapsed .list-bullet:after,
:is(body, .default-marker-odd, .alternate-marker-odd-1, .alternate-marker-odd-2, .alternate-marker-odd-3, .alternate-marker-odd-4, .alternate-marker-odd-5, .alternate-marker-odd-6) ol > li > ul > li.is-collapsed .list-bullet:after,
:is(body, .default-marker-odd, .alternate-marker-odd-1, .alternate-marker-odd-2, .alternate-marker-odd-3, .alternate-marker-odd-4, .alternate-marker-odd-5, .alternate-marker-odd-6) ul > li > ul > li > ul > li.is-collapsed .list-bullet:after,
:is(body, .default-marker-odd, .alternate-marker-odd-1, .alternate-marker-odd-2, .alternate-marker-odd-3, .alternate-marker-odd-4, .alternate-marker-odd-5, .alternate-marker-odd-6) ul > li > ul > li > ul > li > ul > li > ul > li.is-collapsed .list-bullet:after,
:is(body, .default-marker-odd, .alternate-marker-odd-1, .alternate-marker-odd-2, .alternate-marker-odd-3, .alternate-marker-odd-4, .alternate-marker-odd-5, .alternate-marker-odd-6) ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li.is-collapsed .list-bullet:after,
:is(body, .default-marker-odd, .alternate-marker-odd-1, .alternate-marker-odd-2, .alternate-marker-odd-3, .alternate-marker-odd-4, .alternate-marker-odd-5, .alternate-marker-odd-6) ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li > ul > li.is-collapsed .list-bullet:after {
  background-color: var(--list-marker-color-collapsed);
  box-shadow: 0 0 0 4px rgba(var(--color-accent-rgb), 0.2);
}
.theme-light .menu {
  --shadow-color: 60deg 5% 59%;
}
.theme-dark .menu {
  --shadow-color: 0deg 0% 1%;
}
.menu {
  padding: 8px;
  border: 1px solid var(--background-modifier-border-hover);
  background-color: var(--layer-1);
  border-radius: var(--radius-m);
  box-shadow:
    0px 0.5px 0.5px hsl(var(--shadow-color)/0.43),
    0px 1.5px 1.6px -1px hsl(var(--shadow-color)/0.4),
    0px 4px 4.2px -2px hsl(var(--shadow-color)/0.36),
    0px 10.1px 10.6px -3px hsl(var(--shadow-color)/0.32);
}
body {
  --modal-width: 90vw;
  --modal-height: 85vh;
  --modal-max-width: 1100px;
  --modal-max-height: 1000px;
  --modal-max-width-narrow: 800px;
  --modal-border-width: var(--border-width);
  --modal-border-color: var(--background-modifier-border);
  --modal-radius: var(--radius-l);
  --modal-community-sidebar-width: 280px;
}
.modal-bg {
  backdrop-filter: brightness(0.5);
}
.theme-light,
.theme-dark {
  --background-modifier-cover: var(--background-hover);
}
.colorful-active-nav .horizontal-tab-nav-item.is-active,
.colorful-active-nav .vertical-tab-nav-item.is-active {
  background-color: var(--background-selected);
  color: var(--text-normal);
}
.colorful-active-nav .horizontal-tab-nav-item.is-active:hover,
.colorful-active-nav .vertical-tab-nav-item.is-active:hover {
  background-color: var(--background-selected-hover);
}
.theme-light .modal {
  --shadow-color: 60deg 5% 59%;
}
.theme-dark .modal {
  --shadow-color: 0deg 0% 1%;
}
.modal {
  box-shadow:
    0px 0.5px 0.5px hsl(var(--shadow-color)/0.43),
    0px 1.5px 1.6px -1px hsl(var(--shadow-color)/0.4),
    0px 4px 4.2px -2px hsl(var(--shadow-color)/0.36),
    0px 10.1px 10.6px -3px hsl(var(--shadow-color)/0.32);
}
.notice {
  color: var(--text-normal);
  padding: 0.875em 1em 0.875em 1em;
  max-width: 288px;
  margin-bottom: 16px;
}
.theme-light .notice {
  --shadow-color: 60deg 5% 59%;
  box-shadow:
    inset 0 0 0 1px rgba(0, 0, 0, 0.12),
    0px 0.5px 0.5px hsl(var(--shadow-color)/0.43),
    0px 1.5px 1.6px -1px hsl(var(--shadow-color)/0.4),
    0px 4px 4.2px -2px hsl(var(--shadow-color)/0.36),
    0px 10.1px 10.6px -3px hsl(var(--shadow-color)/0.32);
}
.theme-dark .notice {
  --shadow-color: 0deg 0% 1%;
  box-shadow:
    inset 0 0.5px 0.5px 0.5px rgba(255, 255, 255, 0.09),
    0px 0.5px 0.5px hsl(var(--shadow-color)/0.43),
    0px 1.5px 1.6px -1px hsl(var(--shadow-color)/0.4),
    0px 4px 4.2px -2px hsl(var(--shadow-color)/0.36),
    0px 10.1px 10.6px -3px hsl(var(--shadow-color)/0.32);
}
body {
  --popover-width: 450px;
  --popover-height: 400px;
  --popover-max-height: 70vh;
  --popover-pdf-width: 600px;
  --popover-pdf-height: 800px;
  --popover-font-size: var(--font-text-size);
}
.theme-light .popover {
  --shadow-color: 60deg 5% 59%;
}
.theme-dark .popover {
  --shadow-color: 0deg 0% 1%;
}
.popover {
  box-shadow:
    0px 0.5px 0.5px hsl(var(--shadow-color)/0.43),
    0px 1.5px 1.6px -1px hsl(var(--shadow-color)/0.4),
    0px 4px 4.2px -2px hsl(var(--shadow-color)/0.36),
    0px 10.1px 10.6px -3px hsl(var(--shadow-color)/0.32);
}
body {
  --divider-color: var(--background-modifier-border);
  --divider-color-hover: var(--interactive-accent);
  --divider-width: 1px;
  --divider-width-hover: 3px;
  --divider-vertical-height: calc(100% - var(--header-height));
}
.is-hidden-frameless .workspace-split.mod-left-split > .workspace-leaf-resize-handle,
.is-hidden-frameless .workspace-split.mod-right-split > .workspace-leaf-resize-handle {
  height: calc(100% - var(--header-height));
}
.workspace-split.mod-vertical > * > .workspace-leaf-resize-handle,
.workspace-split.mod-left-split > .workspace-leaf-resize-handle {
  height: calc(100% - var(--header-height));
}
body {
  --ribbon-background: var(--background-secondary);
  --ribbon-background-collapsed: var(--background-primary);
  --ribbon-width: 44px;
  --ribbon-padding: var(--size-4-2) var(--size-4-1) var(--size-4-3);
}
.side-dock-settings .side-dock-ribbon-action:first-child:hover {
  color: var(--pink);
}
.side-dock-settings .side-dock-ribbon-action:nth-child(2):hover {
  color: var(--orange);
}
body {
  --scrollbar-active-thumb-bg: rgba(var(--mono-rgb-100), 0.2);
  --scrollbar-bg: rgba(var(--mono-rgb-100), 0.05);
  --scrollbar-thumb-bg: rgba(var(--mono-rgb-100), 0.1);
}
.mod-settings .vertical-tab-content > .setting-item:nth-child(2) > .setting-item-control > input[type=color] {
  visibility: hidden;
  width: 300px;
}
.mod-settings .vertical-tab-content > .setting-item:nth-child(2) > .setting-item-control .lucide-rotate-ccw {
  display: none;
}
.mod-settings .vertical-tab-content > .setting-item:nth-child(2) > .setting-item-control > input[type=color]::before {
  visibility: visible;
  content: "The accent color is overridden by the theme. Use the Style Settings Plugin to customize theme colors.";
  color: var(--text-error);
  font-family: var(--font-interface);
  font-size: var(--font-ui-smaller);
  line-height: 1.3;
  position: relative;
}
.view-content .style-settings-container .setting-item:not(.setting-item-heading) {
  flex-direction: row;
}
.community-item.is-selected {
  border-color: var(--interactive);
  background-color: var(--background-primary);
  color: var(--text-normal);
}
.community-item.is-selected .community-item-author,
.community-item.is-selected .community-item-downloads {
  color: var(--text-muted);
  opacity: 1;
}
.community-item.is-selected:hover,
.community-item:hover {
  background-color: var(--background-modifier-hover);
  color: var(--text-normal);
}
.community-item.is-selected:hover .community-item-author,
.community-item.is-selected:hover .community-item-downloads,
.community-item:hover .community-item-author,
.community-item:hover .community-item-downloads {
  color: var(--text-muted);
  opacity: 1;
}
.style-settings-heading {
  border-bottom: none;
  border-top: 1px solid var(--background-modifier-border);
  margin-bottom: 0;
  min-height: 40px;
  padding: 8px 0;
}
.style-settings-container {
  margin-bottom: 24px;
  padding-left: 16px;
  padding-top: 8px;
}
.themed-color-wrapper > div {
  padding: 4px;
}
.style-settings-heading.is-collapsed .style-settings-collapse-indicator > svg.right-triangle {
  transform: scale(1.5);
}
.style-settings-collapse-indicator > svg.right-triangle {
  transform: scale(1.5) scaleY(-1);
}
body {
  --header-height: 40px;
  --sidebar-markdown-font-size: var(--font-small);
  --sidebar-tab-text-display: none;
  --icon-size: var(--icon-s);
  --icon-stroke: var(--icon-m-stroke-width);
  --icon-color: var(--text-faint);
  --icon-color-hover: var(--text-muted);
  --icon-color-active: var(--interactive);
  --icon-color-focused: var(--text-normal);
  --icon-opacity: 1;
  --icon-opacity-hover: 1;
  --icon-opacity-active: 1;
  --clickable-icon-radius: var(--radius-s);
}
body.theme-light.is-translucent {
  --icon-color: var(--text-muted);
}
.workspace-tab-header-container {
  padding: 0 8px;
}
.mod-left-split .workspace-tab-header-container-inner,
.mod-right-split .workspace-tab-header-container-inner {
  margin: 0;
  padding: 4px;
  gap: 4px;
}
.mod-left-split .workspace-tab-header-container-inner .workspace-tab-header.is-active,
.mod-right-split .workspace-tab-header-container-inner .workspace-tab-header.is-active {
  background-color: transparent;
}
.mod-left-split .workspace-tab-header-container-inner .workspace-tab-header.is-active:hover,
.mod-right-split .workspace-tab-header-container-inner .workspace-tab-header.is-active:hover {
  background-color: var(--background-selected-hover);
}
.mod-left-split .workspace-tab-header-container-inner .workspace-tab-header:not(.is-active):hover .workspace-tab-header-inner,
.mod-right-split .workspace-tab-header-container-inner .workspace-tab-header:not(.is-active):hover .workspace-tab-header-inner {
  background-color: var(--background-hover);
}
.workspace-tab-header-inner {
  padding: 8px;
}
.sidebar-toggle-button {
  padding: 4px 0;
}
.clickable-icon {
  padding: 8px;
}
.nav-header {
  padding: 8px 16px;
}
.nav-buttons-container {
  justify-content: flex-end;
}
body {
  --tab-background-active: var(--background-primary);
  --tab-text-color: var(--text-faint);
  --tab-text-color-focused: var(--text-faint);
  --tab-text-color-focused-active: var(--text-normal);
  --tab-font-size: var(--font-ui-small);
  --tab-font-weight: inherit;
  --tab-container-background: var(--background-secondary);
  --tab-divider-color: var(--background-modifier-border);
  --tab-outline-color: var(--divider-color);
  --tab-outline-width: 1px;
  --tab-curve: 0px;
  --tab-radius: var(--radius-s);
  --tab-radius-active: 6px 6px 0 0;
  --titlebar-background: var(--background-secondary);
  --titlebar-background-focused: var(--background-secondary-alt);
  --titlebar-border-width: 0px;
  --titlebar-border-color: var(--background-modifier-border);
  --titlebar-text-color: var(--text-muted);
  --titlebar-text-color-focused: var(--text-normal);
  --titlebar-text-color-highlighted: var(--text-accent-hover);
  --titlebar-text-weight: var(--font-bold);
}
.workspace-tabs .view-header {
  margin: 0;
  padding: 0 16px;
}
.workspace-tab-header.is-active .workspace-tab-header-inner-close-button:hover {
  --tab-text-color-focused: var(--text-muted);
}
.workspace-tab-header:not(.is-active):hover .workspace-tab-header-inner-title {
  color: var(--text-muted);
}
.workspace .mod-root .workspace-tabs.mod-stacked .workspace-tab-container .workspace-tab-header {
  box-shadow:
    -0.5px 0 0.4px hsl(var(--shadow-color)/0.47),
    -0.8px 0 0.7px -1px hsl(var(--shadow-color)/0.4),
    -1.9px 0px 1.7px -2px hsl(var(--shadow-color)/0.32),
    -4.5px 0px 4px -3px hsl(var(--shadow-color)/0.25),
    -9.4px 0 8.4px -4.1px hsl(var(--shadow-color)/0.18);
}
.theme-light .workspace .mod-root .workspace-tabs.mod-stacked .workspace-tab-container .workspace-tab-header {
  --shadow-color: 60deg 5% 59%;
}
.theme-dark .workspace .mod-root .workspace-tabs.mod-stacked .workspace-tab-container .workspace-tab-header {
  --shadow-color: 0deg 0% 1%;
}
.mod-stacked .workspace-tab-header {
  border-right: var(--divider-width) solid var(--divider-color);
}
body.is-focused .workspace-tab-header:not(.mod-active) .workspace-tab-header-inner-close-button {
  color: var(--tab-text-color-focused);
}
body {
  --table-header-background: transparent;
  --table-header-size: var(--font-smaller);
  --table-header-weight: var(--font-semibold);
  --table-header-color: var(--text-muted);
  --table-sticky-height: 600px;
  --table-hover-highlight-color: rgba(var(--color-accent-rgb), .15);
}
.cm-embed-block.markdown-rendered .block-language-dataview,
.markdown-rendered table,
.markdown-rendered .table-view-table {
  margin-bottom: 24px;
  border-collapse: collapse;
  table-layout: auto;
  width: 100%;
}
.cm-embed-block.markdown-rendered .block-language-dataview th,
.cm-embed-block.markdown-rendered .block-language-dataview td,
.markdown-rendered table th,
.markdown-rendered table td,
.markdown-rendered .table-view-table th,
.markdown-rendered .table-view-table td {
  padding: 4px 8px 4px 16px;
  border: none;
}
.table-single-rows .cm-embed-block.markdown-rendered .block-language-dataview th,
.table-single-rows .cm-embed-block.markdown-rendered .block-language-dataview td,
.table-single-rows .markdown-rendered table th,
.table-single-rows .markdown-rendered table td,
.table-single-rows .markdown-rendered .table-view-table th,
.table-single-rows .markdown-rendered .table-view-table td {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.cm-embed-block.markdown-rendered .block-language-dataview th,
.markdown-rendered table th,
.markdown-rendered .table-view-table th {
  font-size: var(--table-header-size);
  font-weight: var(--table-header-weight);
  color: var(--table-header-color);
  background-color: var(--table-header-background);
  border-bottom: 1px solid var(--border-subtle-2);
}
.table-cell-border .cm-embed-block.markdown-rendered .block-language-dataview th,
.table-cell-border .markdown-rendered table th,
.table-cell-border .markdown-rendered .table-view-table th {
  border: 1px solid var(--border-subtle-1);
  border-bottom: 1px solid var(--border-strong-1);
}
.table-sticky-headers .cm-embed-block.markdown-rendered .block-language-dataview thead,
.table-sticky-headers .cm-embed-block.markdown-rendered .block-language-dataview tbody tr,
.table-sticky-headers .markdown-rendered table thead,
.table-sticky-headers .markdown-rendered table tbody tr,
.table-sticky-headers .markdown-rendered .table-view-table thead,
.table-sticky-headers .markdown-rendered .table-view-table tbody tr {
  border-collapse: collapse;
  display: table;
  width: 100%;
  table-layout: fixed;
}
.table-sticky-headers .cm-embed-block.markdown-rendered .block-language-dataview tbody,
.table-sticky-headers .markdown-rendered table tbody,
.table-sticky-headers .markdown-rendered .table-view-table tbody {
  display: block;
  max-height: var(--table-sticky-height);
  overflow: auto;
}
.cm-embed-block.markdown-rendered .block-language-dataview td,
.markdown-rendered table td,
.markdown-rendered .table-view-table td {
  font-size: var(--font-smaller);
  font-weight: var(--font-normal);
}
.table-cell-border .cm-embed-block.markdown-rendered .block-language-dataview td,
.table-cell-border .markdown-rendered table td,
.table-cell-border .markdown-rendered .table-view-table td {
  border: 1px solid var(--border-subtle-1);
}
.table-column-border .cm-embed-block.markdown-rendered .block-language-dataview td:not(:last-child),
.table-column-border .markdown-rendered table td:not(:last-child),
.table-column-border .markdown-rendered .table-view-table td:not(:last-child) {
  border-right: 1px solid var(--border-subtle-1);
}
.cm-embed-block.markdown-rendered .block-language-dataview tbody tr,
.markdown-rendered table tbody tr,
.markdown-rendered .table-view-table tbody tr {
  margin: 0;
}
.table-alternate-row .cm-embed-block.markdown-rendered .block-language-dataview tbody tr:nth-child(odd),
.table-alternate-row .markdown-rendered table tbody tr:nth-child(odd),
.table-alternate-row .markdown-rendered .table-view-table tbody tr:nth-child(odd) {
  background-color: var(--background-hover);
}
.cm-embed-block.markdown-rendered .block-language-dataview tbody tr:nth-child(odd):hover,
.cm-embed-block.markdown-rendered .block-language-dataview tbody tr:nth-child(even):hover,
.markdown-rendered table tbody tr:nth-child(odd):hover,
.markdown-rendered table tbody tr:nth-child(even):hover,
.markdown-rendered .table-view-table tbody tr:nth-child(odd):hover,
.markdown-rendered .table-view-table tbody tr:nth-child(even):hover {
  background-color: var(--table-hover-highlight-color);
}
.table-hover-row .cm-embed-block.markdown-rendered .block-language-dataview tbody tr:hover,
.table-hover-row .markdown-rendered table tbody tr:hover,
.table-hover-row .markdown-rendered .table-view-table tbody tr:hover {
  background-color: transparent;
}
.table-alternate-column .cm-embed-block.markdown-rendered .block-language-dataview tbody tr td:nth-child(odd),
.table-alternate-column .markdown-rendered table tbody tr td:nth-child(odd),
.table-alternate-column .markdown-rendered .table-view-table tbody tr td:nth-child(odd) {
  background-color: var(--background-hover);
  border-top: 1px solid var(--background-hover);
  border-bottom: 1px solid var(--background-hover);
}
.table-row-border:not(.table-sticky-headers) .cm-embed-block.markdown-rendered .block-language-dataview tbody tr td,
.table-row-border:not(.table-sticky-headers) .markdown-rendered table tbody tr td,
.table-row-border:not(.table-sticky-headers) .markdown-rendered .table-view-table tbody tr td {
  border-top: 1px solid var(--border-subtle-1);
  border-bottom: 1px solid var(--border-subtle-1);
}
.table-sticky-headers.table-row-border .cm-embed-block.markdown-rendered .block-language-dataview tbody tr td,
.table-sticky-headers.table-row-border .markdown-rendered table tbody tr td,
.table-sticky-headers.table-row-border .markdown-rendered .table-view-table tbody tr td {
  border-bottom: 1px solid var(--border-subtle-1);
}
.table-nums table {
  counter-reset: section;
}
.table-nums table thead > tr > th:first-child::before {
  display: inline-block;
  content: " ";
  padding-right: 8px;
  min-width: 3ch;
}
.table-nums table tbody > tr > td:first-child::before {
  display: inline-block;
  counter-increment: section;
  content: counter(section) " ";
  text-align: center;
  padding-right: 8px;
  color: var(--text-faint);
  font-size: 0.875em;
  font-variant-numeric: tabular-nums;
  min-width: 3ch;
}
.table-tabular-figures table {
  font-variant-numeric: tabular-nums;
}
body {
  --tag-size: var(--font-smaller);
  --tag-color: var(--text-accent);
  --tag-color-hover: var(--text-accent);
  --tag-decoration: none;
  --tag-decoration-hover: none;
  --tag-background: rgba(var(--color-accent-rgb), 0.15);
  --tag-background-hover: rgba(var(--color-accent-rgb), 0.25);
  --tag-border-color: rgba(var(--color-accent-rgb), 0.15);
  --tag-border-color-hover: rgba(var(--color-accent-rgb), 0.25);
  --tag-border-width: 0px;
  --tag-padding-x: 0.65em;
  --tag-padding-y: 0.25em;
  --tag-radius: 2em;
}
.view-header .clickable-icon[aria-label="Navigate back"],
.view-header .clickable-icon[aria-label="Navigate forward"] {
  box-shadow: none;
}
.view-header .view-actions {
  position: sticky;
  margin-top: 24px;
}
.workspace-leaf-content[data-type=style-settings] .view-header,
.workspace-leaf-content[data-type=graph] .view-header {
  height: unset;
}
.workspace-leaf-content[data-type=style-settings] .view-header .view-header-title,
.workspace-leaf-content[data-type=graph] .view-header .view-header-title {
  visibility: hidden;
}
.workspace-leaf-content[data-type=style-settings] .view-header .view-actions,
.workspace-leaf-content[data-type=graph] .view-header .view-actions {
  position: unset;
  margin-top: unset;
}
body.pane-relief-pane-numbering .workspace-split .workspace-leaf .view-header-icon,
body.pane-relief-pane-numbering .workspace-split .workspace-leaf-content[data-type=style-settings] .view-header-icon {
  display: none;
}
.sticky-view-actions .view-header {
  height: unset;
  border-width: 1px;
}
.sticky-view-actions .view-header .view-actions {
  position: unset;
  margin-top: unset;
}
.tooltip {
  color: var(--background-primary);
  background-color: var(--background-inverse);
  left: 50%;
  max-width: 288px;
  padding: 4px 16px;
}
.theme-light .tooltip {
  --shadow-color: 60deg 5% 59%;
  box-shadow:
    inset 0 0 0 1px rgba(0, 0, 0, 0.12),
    0px 0.5px 0.5px hsl(var(--shadow-color)/0.43),
    0px 1.5px 1.6px -1px hsl(var(--shadow-color)/0.4),
    0px 4px 4.2px -2px hsl(var(--shadow-color)/0.36),
    0px 10.1px 10.6px -3px hsl(var(--shadow-color)/0.32);
}
.theme-dark .tooltip {
  --shadow-color: 0deg 0% 1%;
  box-shadow:
    inset 0 0.5px 0.5px 0.5px rgba(255, 255, 255, 0.09),
    0px 0.5px 0.5px hsl(var(--shadow-color)/0.43),
    0px 1.5px 1.6px -1px hsl(var(--shadow-color)/0.4),
    0px 4px 4.2px -2px hsl(var(--shadow-color)/0.36),
    0px 10.1px 10.6px -3px hsl(var(--shadow-color)/0.32);
}
.tooltip .tooltip-arrow {
  position: absolute;
  top: -5px;
  left: 50%;
  width: 0;
  margin-left: -5px;
  border-bottom: 5px solid var(--background-inverse);
  border-right: 5px solid transparent;
  border-left: 5px solid transparent;
  content: " ";
  font-size: 0;
  line-height: 0;
}
.tooltip.mod-right .tooltip-arrow {
  top: calc(50% - 5px);
  left: -5px;
  border-right: 5px solid var(--background-inverse);
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
}
.tooltip.mod-left .tooltip-arrow {
  top: calc(50% - 5px);
  left: calc(100% + 5px);
  border-left: 5px solid var(--background-inverse);
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
}
.tooltip.mod-top .tooltip-arrow {
  top: 100%;
  border-top: 5px solid var(--background-inverse);
  border-bottom: 5px solid transparent;
}
.tooltip.mod-error .tooltip-arrow {
  border-bottom-color: var(--background-modifier-error);
}
.tooltip.mod-error.mod-right .tooltip-arrow {
  border-right-color: var(--background-modifier-error);
  border-bottom: 5px solid transparent;
}
.tooltip.mod-error.mod-left .tooltip-arrow {
  border-left-color: var(--background-modifier-error);
  border-bottom: 5px solid transparent;
}
body {
  font-feature-settings: "ss05", "ss02" on;
  -webkit-font-smoothing: subpixel-antialiased;
  --font-smallest: .8em;
  --font-smaller: 0.875em;
  --font-small: 0.933em;
  --font-ui-smaller: 12px;
  --font-ui-small: 12px;
  --font-ui-medium: 14px;
  --font-ui-large: 18px;
  --line-height-normal: 1.5;
  --h1-size: 2em;
  --h2-size: 1.75em;
  --h3-size: 1.5em;
  --h4-size: 1.25em;
  --h5-size: 1.125em;
  --h6-size: 1em;
  --h1-color: inherit;
  --h2-color: inherit;
  --h3-color: inherit;
  --h4-color: inherit;
  --h5-color: inherit;
  --h6-color: inherit;
  --h1-line-height: 1.2;
  --h2-line-height: 1.2;
  --h3-line-height: 1.3;
  --h4-line-height: 1.4;
  --h5-line-height: var(--line-height-normal);
  --h6-line-height: var(--line-height-normal);
  --h1-style: normal;
  --h2-style: normal;
  --h3-style: normal;
  --h4-style: normal;
  --h5-style: normal;
  --h6-style: normal;
  --h1-variant: normal;
  --h2-variant: normal;
  --h3-variant: normal;
  --h4-variant: normal;
  --h5-variant: normal;
  --h6-variant: normal;
  --h1-weight: 600;
  --h2-weight: 600;
  --h3-weight: 600;
  --h4-weight: 600;
  --h5-weight: 600;
  --h6-weight: 600;
}
body .markdown-source-view.mod-cm6 .cm-scroller {
  font-family: var(--font-editor);
}
body {
  --h1-font: var(--font-text);
  --h2-font: var(--font-text);
  --h3-font: var(--font-text);
  --h4-font: var(--font-text);
  --h5-font: var(--font-text);
  --h6-font: var(--font-text);
}
h1,
.markdown-rendered h1,
.HyperMD-header-1,
.HyperMD-list-line .cm-header-1 {
  border: none;
  font-family: var(--h1-font);
}
.h1-line h1,
.h1-line .markdown-rendered h1 {
  border-bottom: 1px solid var(--background-modifier-border);
}
.h2-line h2,
.h2-line .markdown-rendered h2 {
  border-bottom: 1px solid var(--background-modifier-border);
}
.h3-line h3,
.h3-line .markdown-rendered h3 {
  border-bottom: 1px solid var(--background-modifier-border);
}
.h4-line h4,
.h4-line .markdown-rendered h4 {
  border-bottom: 1px solid var(--background-modifier-border);
}
.h5-line h5,
.h5-line .markdown-rendered h5 {
  border-bottom: 1px solid var(--background-modifier-border);
}
.h6-line h6,
.h6-line .markdown-rendered h6 {
  border-bottom: 1px solid var(--background-modifier-border);
}
body {
  --heading-counter: " ";
}
.heading-counter-0 .el-h1 {
  counter-increment: h1-counter;
  counter-reset: h1-counter;
  counter-set: h1-counter;
}
.heading-counter-0 .el-h1 h1:before {
  content: var(--heading-counter) counter(h1-counter) "";
  padding-right: 0.5em;
}
.heading-counter-0 .el-h2 {
  counter-increment: h2-counter;
  counter-reset: h3-counter;
  counter-set: h1-counter;
}
.heading-counter-0 .el-h2 h2:before {
  content: var(--heading-counter) counter(h1-counter) "." counter(h2-counter) "";
  padding-right: 0.5em;
}
.heading-counter-0 .el-h3 {
  counter-increment: h3-counter;
  counter-reset: h4-counter;
  counter-set: h1-counter;
}
.heading-counter-0 .el-h3 h3:before {
  content: var(--heading-counter) counter(h1-counter) "." counter(h2-counter) "." counter(h3-counter) "";
  padding-right: 0.5em;
}
.heading-counter-0 .el-h4 {
  counter-increment: h4-counter;
  counter-reset: h5-counter;
  counter-set: h1-counter;
}
.heading-counter-0 .el-h4 h4:before {
  content: var(--heading-counter) counter(h1-counter) "." counter(h2-counter) "." counter(h3-counter) "." counter(h4-counter) "";
  padding-right: 0.5em;
}
.heading-counter-0 .el-h5 {
  counter-increment: h5-counter;
  counter-reset: h6-counter;
  counter-set: h1-counter;
}
.heading-counter-0 .el-h5 h5:before {
  content: var(--heading-counter) counter(h1-counter) "." counter(h2-counter) "." counter(h3-counter) "." counter(h4-counter) "." counter(h5-counter) "";
  padding-right: 0.5em;
}
.heading-counter-0 .el-h6 {
  counter-increment: h6-counter;
  counter-set: h1-counter;
}
.heading-counter-0 .el-h6 h6:before {
  content: var(--heading-counter) counter(h1-counter) "." counter(h2-counter) "." counter(h3-counter) "." counter(h4-counter) "." counter(h5-counter) "." counter(h6-counter) "";
  padding-right: 0.5em;
}
.heading-counter-1 .el-h1 {
  counter-increment: h1-counter;
  counter-reset: h1-counter;
}
.heading-counter-1 .el-h1 h1:before {
  content: var(--heading-counter) counter(h1-counter) "";
  padding-right: 0.5em;
}
.heading-counter-1 .el-h2 {
  counter-increment: h2-counter;
  counter-reset: h3-counter;
}
.heading-counter-1 .el-h2 h2:before {
  content: var(--heading-counter) counter(h1-counter) "." counter(h2-counter) "";
  padding-right: 0.5em;
}
.heading-counter-1 .el-h3 {
  counter-increment: h3-counter;
  counter-reset: h4-counter;
}
.heading-counter-1 .el-h3 h3:before {
  content: var(--heading-counter) counter(h1-counter) "." counter(h2-counter) "." counter(h3-counter) "";
  padding-right: 0.5em;
}
.heading-counter-1 .el-h4 {
  counter-increment: h4-counter;
  counter-reset: h5-counter;
}
.heading-counter-1 .el-h4 h4:before {
  content: var(--heading-counter) counter(h1-counter) "." counter(h2-counter) "." counter(h3-counter) "." counter(h4-counter) "";
  padding-right: 0.5em;
}
.heading-counter-1 .el-h5 {
  counter-increment: h5-counter;
  counter-reset: h6-counter;
}
.heading-counter-1 .el-h5 h5:before {
  content: var(--heading-counter) counter(h1-counter) "." counter(h2-counter) "." counter(h3-counter) "." counter(h4-counter) "." counter(h5-counter) "";
  padding-right: 0.5em;
}
.heading-counter-1 .el-h6 {
  counter-increment: h6-counter;
}
.heading-counter-1 .el-h6 h6:before {
  content: var(--heading-counter) counter(h1-counter) "." counter(h2-counter) "." counter(h3-counter) "." counter(h4-counter) "." counter(h5-counter) "." counter(h6-counter) "";
  padding-right: 0.5em;
}
body {
  --input-height: 32px;
  --input-radius: 4px;
}
button {
  padding: 0 16px;
  background-color: var(--layer-1);
  box-shadow: var(--input-shadow);
}
button:hover {
  background-color: var(--layer-hover-1);
  box-shadow: var(--input-shadow-hover);
}
button:focus-visible {
  box-shadow: 0 0 0 2px var(--background-modifier-border-focus);
}
button.mod-cta {
  background-color: rgba(var(--color-accent-rgb), 1);
}
button.mod-cta:hover {
  filter: brightness(0.95);
}
button.mod-cta:focus-visible {
  box-shadow: 0 0 0 2px var(--focus-inset), 0 0 0 4px var(--background-modifier-border-focus);
}
body {
  --checkbox-color-rgb:
    102,
    153,
    97;
  --checkbox-radius: 16px;
  --checkbox-checked-color-hover: rgba(var(--checkbox-color-rgb), .9);
  --checkbox-size: 16px;
  --checkbox-color: rgb(var(--checkbox-color-rgb));
  --checkbox-color-hover: rgba(var(--checkbox-color-rgb), .5);
  --checkbox-border-color: var(--text-faint);
  --checkbox-border-color-hover: rgb(var(--checkbox-color-rgb));
  --checkbox-marker-color: var(--background-primary);
}
input[type=checkbox] {
  border-radius: var(--checkbox-radius);
  border: 1.5px solid var(--checkbox-border-color);
  width: var(--checkbox-size);
  height: var(--checkbox-size);
  transition: box-shadow var(--duration-moderate-2) var(--motion-entrance-expressive);
}
input[type=checkbox]:hover {
  box-shadow: 0 0 0 3px rgba(var(--checkbox-color-rgb), 0.2);
}
input[type=checkbox]:checked {
  border-color: transparent;
  background-color: var(--checkbox-color);
}
input[type=checkbox]:checked:hover {
  background-color: var(--checkbox-checked-color-hover);
}
input[type=checkbox]:checked::after {
  top: -1px;
  left: -1px;
  background-color: var(--checkbox-marker-color);
  -webkit-mask-position: 43.75% 53.125%;
  -webkit-mask-size: 59.375%;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='hsl(36, 20%, 95.1%)' viewBox='0 0 14 14'%3E%3Cpolygon points='5.5 11.9993304 14 3.49933039 12.5 2 5.5 8.99933039 1.5 4.9968652 0 6.49933039'%3E%3C/polygon%3E%3C/svg%3E");
}
input[type=checkbox]:focus-visible {
  box-shadow: 0 0 0 1px var(--background-primary), 0 0 0 3px var(--background-modifier-border-focus);
}
.markdown-preview-view input[type=checkbox] {
  top: 2px;
}
.pickr .pcr-button,
.pickr .pcr-button::before,
.pickr .pcr-button::after {
  border-radius: 100vmax;
  box-shadow: inset 0 0 0 1px rgba(var(--mono-rgb-100), 0.25);
}
.pickr .pcr-button:hover,
.pickr .pcr-button::before:hover,
.pickr .pcr-button::after:hover {
  box-shadow: inset 0 0 0 1px rgba(var(--mono-rgb-100), 0.25), 0 0 0 3px var(--background-modifier-hover);
}
.pcr-app .pcr-swatches > button,
.pcr-app .pcr-swatches > button::before,
.pcr-app .pcr-swatches > button::after {
  border-radius: 50%;
}
.pcr-app.visible {
  border-radius: var(--radius-m);
  gap: 4px;
}
.pcr-app[data-theme=nano] .pcr-selection .pcr-color-preview {
  justify-content: flex-start;
}
.pcr-app[data-theme=nano] .pcr-swatches {
  margin: 0;
}
.pcr-app[data-theme=nano] .pcr-interaction {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr;
  align-items: center;
  gap: 4px;
  grid-template-areas: "result result result" "type-1 type-2 type-3" "save save cancel";
}
.pcr-app[data-theme=nano] .pcr-interaction .pcr-result {
  grid-area: result;
  margin: 0;
}
.pcr-app[data-theme=nano] .pcr-interaction .pcr-type[data-type=RGBA] {
  grid-area: type-1;
  margin: 0;
  font-family: var(--font-interface);
}
.pcr-app[data-theme=nano] .pcr-interaction .pcr-type[data-type=HEXA] {
  grid-area: type-2;
  margin: 0;
  font-family: var(--font-interface);
}
.pcr-app[data-theme=nano] .pcr-interaction .pcr-type[data-type=HSLA] {
  grid-area: type-3;
  margin: 0;
  font-family: var(--font-interface);
}
.pcr-app[data-theme=nano] .pcr-interaction .pcr-save {
  grid-area: save;
  margin: 0;
  text-transform: capitalize;
  font-family: var(--font-interface);
}
.pcr-app[data-theme=nano] .pcr-interaction .pcr-cancel {
  grid-area: cancel;
  margin: 0;
  font-family: var(--font-interface);
}
select,
.dropdown {
  padding: 0 32px 0 16px;
  box-shadow: var(--input-shadow);
  border-radius: var(--input-radius);
  background-color: var(--layer-1);
  background-repeat: no-repeat, repeat;
  background-position: right 8px top 60%, 0 0;
  background-size: 16px auto, 100%;
}
select:hover,
.dropdown:hover {
  background-color: var(--layer-hover-1);
  box-shadow: var(--input-shadow-hover);
}
select:focus-visible,
.dropdown:focus-visible {
  box-shadow: 0 0 0 2px var(--background-modifier-border-focus);
}
.theme-light .dropdown {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' version='1.1' id='icon' opacity='0.4' x='0px' y='0px' viewBox='0 0 32 32' style='enable-background:new 0 0 32 32;' xml:space='preserve'%3E%3Cstyle type='text/css'%3E .st0%7Bfill:none;%7D%0A%3C/style%3E%3Cpolygon points='16,22 6,12 7.4,10.6 16,19.2 24.6,10.6 26,12 '/%3E%3Crect id='_x3C_Transparent_Rectangle_x3E_' class='st0' width='32' height='32'/%3E%3C/svg%3E");
}
.theme-dark .dropdown {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' version='1.1' id='icon' fill='white' opacity='0.4' x='0px' y='0px' viewBox='0 0 32 32' style='enable-background:new 0 0 32 32;' xml:space='preserve'%3E%3Cstyle type='text/css'%3E .st0%7Bfill:none;%7D%0A%3C/style%3E%3Cpolygon points='16,22 6,12 7.4,10.6 16,19.2 24.6,10.6 26,12 '/%3E%3Crect id='_x3C_Transparent_Rectangle_x3E_' class='st0' width='32' height='32'/%3E%3C/svg%3E");
}
textarea,
input[type=text],
input[type=search],
input[type=email],
input[type=password],
input[type=number] {
  background: var(--background-modifier-form-field);
  border-radius: var(--input-radius);
  height: var(--input-height);
  border-bottom: 1px solid var(--border-strong-1);
  border-bottom-color: var(--border-strong-1);
  transition: all var(--duration-moderate-2) var(--motion-expressive-entrance);
}
textarea:hover:not(:focus):not(:focus-visible),
input[type=text]:hover:not(:focus):not(:focus-visible),
input[type=search]:hover:not(:focus):not(:focus-visible),
input[type=email]:hover:not(:focus):not(:focus-visible),
input[type=password]:hover:not(:focus):not(:focus-visible),
input[type=number]:hover:not(:focus):not(:focus-visible) {
  border-bottom-color: var(--background-modifier-border-focus);
  background-color: var(--field-hover-1);
}
textarea:hover:not(:focus):not(:focus-visible)::placeholder,
input[type=text]:hover:not(:focus):not(:focus-visible)::placeholder,
input[type=search]:hover:not(:focus):not(:focus-visible)::placeholder,
input[type=email]:hover:not(:focus):not(:focus-visible)::placeholder,
input[type=password]:hover:not(:focus):not(:focus-visible)::placeholder,
input[type=number]:hover:not(:focus):not(:focus-visible)::placeholder {
  color: var(--text-muted);
}
textarea:focus-visible,
input[type=text]:focus-visible,
input[type=search]:focus-visible,
input[type=email]:focus-visible,
input[type=password]:focus-visible,
input[type=number]:focus-visible {
  box-shadow: none;
}
textarea:focus,
input[type=text]:focus,
input[type=search]:focus,
input[type=email]:focus,
input[type=password]:focus,
input[type=number]:focus {
  box-shadow: none;
  border-color: var(--background-modifier-border);
  border-bottom-color: var(--background-modifier-border-focus);
}
textarea:focus:placeholder-shown,
input[type=text]:focus:placeholder-shown,
input[type=search]:focus:placeholder-shown,
input[type=email]:focus:placeholder-shown,
input[type=password]:focus:placeholder-shown,
input[type=number]:focus:placeholder-shown {
  box-shadow: 0 0 0 0px var(--background-primary), 0 0 0 2px var(--background-modifier-border-focus);
  border: none;
}
input.prompt-input {
  padding: 24px;
}
div.search-input-container:before {
  background-color: var(--text-faint);
}
.markdown-rendered progress,
.markdown-source-view.is-live-preview progress {
  height: 4px;
  width: 240px;
  border-radius: 0;
  vertical-align: 0;
  overflow: visible;
}
.markdown-rendered progress[value],
.markdown-source-view.is-live-preview progress[value] {
  position: relative;
}
.markdown-rendered progress[value]::-webkit-progress-bar,
.markdown-source-view.is-live-preview progress[value]::-webkit-progress-bar {
  background-color: var(--background-active);
  box-shadow: none;
  border-radius: 6px;
}
.markdown-rendered progress[value]::-webkit-progress-value,
.markdown-source-view.is-live-preview progress[value]::-webkit-progress-value {
  background-color: var(--interactive-accent);
}
.markdown-rendered progress[value]:before,
.markdown-source-view.is-live-preview progress[value]:before {
  font-size: 60%;
  line-height: 1.3;
  content: attr(value) "/" attr(max);
  color: var(--text-muted);
  float: right;
}
.markdown-rendered progress[value][max="100"]:before,
.markdown-source-view.is-live-preview progress[value][max="100"]:before {
  content: attr(value) "%";
}
.markdown-rendered progress[value][aria-description]:after,
.markdown-source-view.is-live-preview progress[value][aria-description]:after {
  content: attr(aria-description);
  position: absolute;
  font-size: 60%;
  line-height: 1.3;
  top: 0;
  color: var(--text-muted);
}
.markdown-source-view.is-live-preview progress[value^="1"]::-webkit-progress-value,
.markdown-source-view.is-live-preview progress[value^="2"]::-webkit-progress-value,
.markdown-source-view.is-live-preview progress[value^="3"]::-webkit-progress-value,
.markdown-preview-view progress[value^="1"]::-webkit-progress-value,
.markdown-preview-view progress[value^="2"]::-webkit-progress-value,
.markdown-preview-view progress[value^="3"]::-webkit-progress-value {
  background-color: var(--red);
}
.markdown-source-view.is-live-preview progress[value^="4"]::-webkit-progress-value,
.markdown-source-view.is-live-preview progress[value^="5"]::-webkit-progress-value,
.markdown-source-view.is-live-preview progress[value^="6"]::-webkit-progress-value,
.markdown-source-view.is-live-preview progress[value^="7"]::-webkit-progress-value,
.markdown-preview-view progress[value^="4"]::-webkit-progress-value,
.markdown-preview-view progress[value^="5"]::-webkit-progress-value,
.markdown-preview-view progress[value^="6"]::-webkit-progress-value,
.markdown-preview-view progress[value^="7"]::-webkit-progress-value {
  background-color: var(--orange);
}
.markdown-source-view.is-live-preview progress[value^="8"]::-webkit-progress-value,
.markdown-source-view.is-live-preview progress[value^="9"]::-webkit-progress-value,
.markdown-source-view.is-live-preview progress[value^="100"]::-webkit-progress-value,
.markdown-preview-view progress[value^="8"]::-webkit-progress-value,
.markdown-preview-view progress[value^="9"]::-webkit-progress-value,
.markdown-preview-view progress[value^="100"]::-webkit-progress-value {
  background-color: var(--green);
}
.markdown-source-view.is-live-preview progress:not([value]),
.markdown-preview-view progress:not([value]) {
  background-color: var(--background-active);
  border-radius: 3px;
}
.markdown-source-view.is-live-preview progress:not([value]):indeterminate,
.markdown-preview-view progress:not([value]):indeterminate {
  animation: clr-progress-looper 2s infinite linear;
}
@keyframes clr-progress-looper {
  0% {
    padding-left: 0;
  }
  50% {
    padding-left: 7.5em;
  }
  100% {
    padding-left: 208px;
  }
}
.markdown-source-view.is-live-preview progress:not([value]):indeterminate::-webkit-progress-bar,
.markdown-preview-view progress:not([value]):indeterminate::-webkit-progress-bar {
  background-color: var(--cyan);
  width: 32px;
}
body {
  --slider-thumb-height: 18px;
  --slider-thumb-width: 18px;
  --slider-thumb-y: -7px;
  --slider-thumb-radius: 50%;
}
input[type=range] {
  min-width: 200px;
  max-width: 640px;
  background-color: transparent;
  height: 32px;
  margin: 0 4px;
  display: flex;
  flex-direction: row;
  align-items: center;
}
input[type=range]:hover::-webkit-slider-runnable-track {
  background-color: var(--border-subtle-selected-1);
}
input[type=range]::-webkit-slider-runnable-track {
  height: 3px;
  background-color: var(--background-modifier-border);
  border-radius: 3px;
}
input[type=range]::-webkit-slider-thumb {
  top: var(--slider-thumb-y);
  transition: all 0.1s linear;
  box-shadow: 0 1px 1px 0px rgba(0, 0, 0, 0.05), 0 2px 2px 0px rgba(0, 0, 0, 0.1);
}
input[type=range]::-webkit-slider-thumb:hover,
input[type=range]::-webkit-slider-thumb:active {
  --slider-thumb-height: 20px;
  --slider-thumb-width: 20px;
  --slider-thumb-y: -8px;
  border-color: var(--background-modifier-border-hover);
}
body:not(.is-mobile) input[type=range]:focus-visible::-webkit-slider-thumb,
body:not(.is-mobile) input[type=range]:focus::-webkit-slider-thumb {
  border-width: 2px;
  border-color: var(--background-modifier-border-focus);
  box-shadow:
    0 1px 2px 0px rgba(0, 0, 0, 0.05),
    0 2px 3px 0px rgba(0, 0, 0, 0.2),
    0 0 0 1px var(--focus-inset),
    0 0 0px 3px var(--background-modifier-border-focus);
}
input[type=range]:before {
  content: attr(min);
  display: block;
  font-size: var(--font-ui-smaller);
  padding-right: 8px;
  color: var(--text-muted);
}
input[type=range]:after {
  pointer-events: none;
  content: attr(max);
  display: block;
  font-size: var(--font-ui-smaller);
  padding-left: 8px;
  color: var(--text-muted);
}
body {
  --toggle-border-width: 2px;
  --toggle-width: 48px;
  --toggle-radius: 100vmax;
  --toggle-thumb-color: rgb(var(--white));
  --toggle-thumb-radius: 20px;
  --toggle-thumb-height: 20px;
  --toggle-thumb-width: 20px;
  --toggle-s-border-width: 2px;
  --toggle-s-width: 32px;
  --toggle-s-thumb-height: 14px;
  --toggle-s-thumb-width: 14px;
}
.checkbox-container {
  --shadow-color: 0deg 0% 61%;
  --toggle-shadow:
    inset 0px 0.5px 0.5px hsl(var(--shadow-color) / 0.57),
    inset 0px 0.8px 0.7px -2px hsl(var(--shadow-color) / 0.43),
    inset 0px 2.1px 1.9px -4px hsl(var(--shadow-color) / 0.29);
  border-radius: var(--toggle-radius);
  height: calc(var(--toggle-thumb-height) + var(--toggle-border-width) * 2);
  width: var(--toggle-width);
  box-shadow: var(--toggle-shadow);
  transition:
    box-shadow 0.15s ease-in-out,
    outline 0.15s ease-in-out,
    border 0.15s ease-in-out,
    opacity 0.15s ease-in-out;
  outline: 0 solid var(--background-modifier-border-focus);
}
.checkbox-container:not(.is-enabled):hover {
  background-color: var(--border-subtle-selected-1);
  box-shadow:
    inset 0px 0.8px 0.7px hsl(var(--shadow-color)/0.57),
    inset 0px 1.2px 1.1px -2px hsl(var(--shadow-color)/0.43),
    inset 0px 3.2px 2.9px -4px hsl(var(--shadow-color)/0.29);
}
.checkbox-container.is-enabled:focus-within {
  outline-offset: 2px;
}
.theme-dark .checkbox-container {
  --shadow-color: 0deg 0% 1%;
}
body {
  --aside-background: transparent;
  --aside-background-hover: var(--background-modifier-hover);
  --aside-text-color: var(--text-muted);
  --aside-border-color: var(--background-modifier-border);
}
.callout[data-callout~=aside] {
  --callout-color:
    0,
    0,
    0;
  --callout-icon: none;
  --move: 0;
  background-color: var(--aside-background);
  position: relative;
  float: right;
  clear: both;
  margin: 0;
  padding: 0;
  max-width: 50%;
  padding-top: 4px;
  margin-left: 1em;
  padding-left: 1em;
  padding-right: 1em;
  margin-bottom: 4px;
  margin-top: 4px;
  font-size: 80%;
  line-height: 1.6;
  color: var(--aside-text-color);
  border-radius: 0;
}
.callout[data-callout~=aside][data-callout-metadata~="+"] {
  --move: calc(40% - 1em);
}
.callout[data-callout~=aside][data-callout-metadata~="++"] {
  --move: calc(30% - 1em);
}
.callout[data-callout~=aside][data-callout-metadata~="+++"] {
  --move: calc(20% - 1em);
}
.callout[data-callout~=aside][data-callout-metadata~="++++"] {
  --move: calc(10% - 1em);
}
.callout[data-callout~=aside][data-callout-metadata~="+++++"] {
  --move: -1em;
}
.callout[data-callout~=aside]:hover {
  border-radius: var(--callout-radius);
}
.callout[data-callout~=aside] .callout-title {
  display: none;
}
.callout[data-callout~=aside] .callout-content {
  padding: 0;
}
.callout[data-callout~=aside] .callout-content > p {
  margin-top: 0;
  margin-block-start: 4px;
  margin-block-end: 8px;
}
.callout[data-callout-metadata~=right] {
  float: right;
  clear: right;
  margin-right: calc(-50% + var(--move));
  margin-left: 1em;
}
.aside-border .callout[data-callout-metadata~=right] {
  border-left: 1px solid var(--aside-border-color);
}
.callout[data-callout-metadata~=left] {
  float: left;
  clear: left;
  margin-right: 1em;
  margin-left: calc(-50% + var(--move));
  border-left: none;
}
.aside-border .callout[data-callout-metadata~=left] {
  border-right: 1px solid var(--aside-border-color);
}
.markdown-rendered {
  counter-reset: sidenote-counter;
}
.el-div[data-callout~=aside] {
  counter-increment: sidenote-counter;
}
.el-div[data-callout~=aside]:hover > .callout[data-callout~=aside] {
  background-color: var(--aside-background-hover);
}
.aside-counter .callout[data-callout~=aside] .callout-content::before {
  content: counter(sidenote-counter) ".";
  position: relative;
  float: left;
  padding-right: 4px;
  vertical-align: baseline;
  font-size: 100%;
  font-weight: bold;
}
.aside-counter .el-div[data-callout~=aside]::after {
  content: counter(sidenote-counter);
  vertical-align: super;
  font-size: 80%;
  font-weight: bold;
}
.code-lines .markdown-rendered pre:not(pre.frontmatter) code[class*=language-] {
  padding-left: 24px;
}
.code-lines .markdown-rendered pre:not(pre.frontmatter) code[class*=language-]:before {
  box-sizing: inherit;
  overflow: hidden;
  position: absolute;
  height: 100%;
  width: 3.1ch;
  content: "1  2  3  4  5  6  7  8  9  10  11  12  13  14  15  16  17  18  19  20  21  22  23  24  25  26  27  28  29  30  31  32  33  34  35  36  37  38  39  40  41  42  43  44  45  46  47  48  49  50  51  52  53  54  55  56  57  58  59  60  61  62  63  64  65  66  67  68  69  70  71  72  73  74  75  76  77  78  79  80  81  82  83  84  85  86  87  88  89  90  91  92  93  94  95  96  97  98  99 100 101 102 103 104 105 106 107 108 109 110 111 112 113 114 115 116 117 118 119 120 121 122 123 124 125 126 127 128 129 130 131 132 133 134 135 136 137 138 139 140 141 142 143 144 145 146 147 148 149 150 151 152 153 154 155 156 157 158 159 160 161 162 163 164 165 166 167 168 169 170 171 172 173 174 175 176 177 178 179 180 181 182 183 184 185 186 187 188 189 190 191 192 193 194 195 196 197 198 199 200 201 202 203 204 205 206 207 208 209 210 211 212 213 214 215 216 217 218 219 220 221 222 223 224 225 226 227 228 229 230 231 232 233 234 235 236 237 238 239 240 241 242 243 244 245 246 247 248 249 250 251 252 253 254 255 256 257 258 259 260 261 262 263 264 265 266 267 268 269 270 271 272 273 274 275 276 277 278 279 280 281 282 283 284 285 286 287 289 290 291 292 293 294 295 296 297 298 299 300 301 302 303 304 305 306 307 308 309 310 311 312 313 314 315 316 317 318 319 320 321 322 323 324 325 326 327 328 329 330 331 332 333 334 335 336 337 338 339 340 341 342 343 344 345 346 347 348 349 350 351 352 353 354 355 356 357 358 359 360 361 362 363 364 365 366 367 368 369 370 371 372 373 374 375 376 377 378 379 380 381 382 383 384 385 386 387 388 389 390 391 392 393 394 395 396 397 398 399 400 401 402 403 404 405 406 407 408 409 410 411 412 413 414 415 416 417 418 419 420 421 422 423 424 425 426 427 428 429 430 431 432 433 434 435 436 437 438 439 440 441 442 443 444 445 446 447 448 449 450 451 452 453 454 455 456 457 458 459 460 461 462 463 464 465 466 467 468 469 470 471 472 473 474 475 476 477 478 479 480 481 482 483 484 485 486 487 488 489 490 491 492 493 494 495 496 497 498 499 500 501 502 503 504 505 506 507 508 509 510 511 512 513 514 515 516 517 518 519 520 521 522 523 524 525 526 527 528 529 530 531 532 533 534 535 536 537 538 539 540 541 542 543 544 545 546 547 548 549 550 551 552 553 554 555 556 557 558 559 560 561 562 563 564 565 566 567 568 569 570 571 572 573 574 575 576 577 578 579 580 581 582 583 584 585 586 587 588 589 590 591 592 593 594 595 596 597 598 599 600 601 602 603 604 605 606 607 608 609 610 611 612 613 614 615 616 617 618 619 620 621 622 623 624 625 626 627 628 629 630 631 632 633 634 635 636 637 638 639 640 641 642 643 644 645 646 647 648 649 650 651 652 653 654 655 656 657 658 659 660 661 662 663 664 665 666 667 668 669 670 671 672 673 674 675 676 677 678 679 680 681 682 683 684 685 686 687 688 689 690 691 692 693 694 695 696 697 698 699 700 701 702 703 704 705 706 707 708 709 710 711 712 713 714 715 716 717 718 719 720 721 722 723 724 725 726 727 728 729 730 731 732 733 734 735 736 737 738 739 740 741 742 743 744 745 746 747 748 749 750 751 752 753 754 755 756 757 758 759 760 761 762 763 764 765 766 767 768 769 770 771 772 773 774 775 776 777 778 779 780 781 782 783 784 785 786 787 788 789 790 791 792 793 794 795 796 797 798 799 800 801 802 803 804 805 806 807 808 809 810 811 812 813 814 815 816 817 818 819 820 821 822 823 824 825 826 827 828 829 830 831 832 833 834 835 836 837 838 839 840 841 842 843 844 845 846 847 848 849 850 851 852 853 854 855 856 857 858 859 860 861 862 863 864 865 866 867 868 869 870 871 872 873 874 875 876 877 878 879 880 881 882 883 884 885 886 887 888 889 890 891 892 893 894 895 896 897 898 899 900 901 902 903 904 905 906 907 908 909 910 911 912 913 914 915 916 917 918 919 920 921 922 923 924 925 926 927 928 929 930 931 932 933 934 935 936 937 938 939 940 941 942 943 944 945 946 947 948 949 950 951 952 953 954 955 956 957 958 959 960 961 962 963 964 965 966 967 968 969 970 971 972 973 974 975 976 977 978 979 980 981 982 983 984 985 986 987 988 989 990 991 992 993 994 995 996 997 998 999";
  color: var(--text-faint);
  direction: rtl;
  font-size: 0.75rem;
  line-height: 1.75em;
  padding-top: 0px;
  left: 6px;
  clip-path: polygon(0 0, 4ch 0, 4ch calc(100% - 20px), 0 calc(100% - 20px));
}
.code-label .markdown-rendered pre[class*=language-]:not(pre.frontmatter) button.copy-code-button {
  top: 24px;
}
.code-label .markdown-rendered pre[class*=language-]:not(pre.frontmatter) {
  padding-top: 32px;
}
.code-label .markdown-rendered pre[class*=language-]:not(pre.frontmatter)::before {
  content: attr(class);
  font-size: 0.75em;
  color: var(--text-muted);
  position: absolute;
  top: 4px;
  right: 8px;
  clip-path: polygon(9ch 0, 100% 0, 100% 100%, 9ch 100%);
}
.code-lines.code-label .markdown-rendered :not(pre.frontmatter) code[class*=language-]:before {
  clip-path: polygon(0 0, 4ch 0, 4ch calc(100% - 40px), 0 calc(100% - 40px));
}
.callout[data-callout*=column] {
  --columns: 2;
  --callout-color:
    0,
    0,
    0,
    0;
  border-width: 0;
  padding: 0;
}
.callout[data-callout*=column] .callout-content blockquote {
  padding: 0;
}
.callout[data-callout*=column] .callout-title {
  display: none;
}
.callout[data-callout*=column] .callout-content {
  display: grid;
  grid-template-columns: repeat(var(--columns), 1fr);
  gap: 0.5em;
  padding: 0;
}
.callout[data-callout*=column][data-callout-metadata~="3"] .callout-content {
  --columns: 3;
}
.callout[data-callout*=column][data-callout-metadata~="4"] .callout-content {
  --columns: 4;
}
li.is-checked:where(li[data-task="*"], li[data-task=a], li[data-task=f], li[data-task=S], li[data-task="-"], li[data-task=">"], li[data-task="<"], li[data-task=l], li[data-task=B], li[data-task=X], li[data-task=n], li[data-task=p], li[data-task=c], li[data-task=w], li[data-task=b], li[data-task=I], li[data-task="!"], li[data-task="?"], li[data-task=i], li[data-task="/"], li[data-task=u], li[data-task=d], li[data-task=F], li[data-task=r], li[data-task=m], li[data-task=M], li[data-task=L], li[data-task=t], li[data-task=T], li[data-task=P]) input[type=checkbox],
.markdown-source-view input[type=checkbox]:where([data-task="*"], [data-task=a], [data-task=f], [data-task=S], [data-task="-"], [data-task=">"], [data-task="<"], [data-task=l], [data-task=B], [data-task=X], [data-task=n], [data-task=p], [data-task=c], [data-task=w], [data-task=b], [data-task=I], [data-task="!"], [data-task="?"], [data-task=i], [data-task="/"], [data-task=u], [data-task=d], [data-task=F], [data-task=r], [data-task=m], [data-task=M], [data-task=L], [data-task=t], [data-task=T], [data-task=P]):checked {
  background-color: transparent;
  border-width: 0;
  pointer-events: none;
}
li.is-checked:where(li[data-task="*"], li[data-task=a], li[data-task=f], li[data-task=S], li[data-task="-"], li[data-task=">"], li[data-task="<"], li[data-task=l], li[data-task=B], li[data-task=X], li[data-task=n], li[data-task=p], li[data-task=c], li[data-task=w], li[data-task=b], li[data-task=I], li[data-task="!"], li[data-task="?"], li[data-task=i], li[data-task="/"], li[data-task=u], li[data-task=d], li[data-task=F], li[data-task=r], li[data-task=m], li[data-task=M], li[data-task=L], li[data-task=t], li[data-task=T], li[data-task=P]) input[type=checkbox]:hover,
.markdown-source-view input[type=checkbox]:where([data-task="*"], [data-task=a], [data-task=f], [data-task=S], [data-task="-"], [data-task=">"], [data-task="<"], [data-task=l], [data-task=B], [data-task=X], [data-task=n], [data-task=p], [data-task=c], [data-task=w], [data-task=b], [data-task=I], [data-task="!"], [data-task="?"], [data-task=i], [data-task="/"], [data-task=u], [data-task=d], [data-task=F], [data-task=r], [data-task=m], [data-task=M], [data-task=L], [data-task=t], [data-task=T], [data-task=P]):checked:hover {
  box-shadow: none;
  border-color: transparent;
  pointer-events: none;
}
li.is-checked:where(li[data-task="*"], li[data-task=a], li[data-task=f], li[data-task=S], li[data-task="-"], li[data-task=">"], li[data-task="<"], li[data-task=l], li[data-task=B], li[data-task=X], li[data-task=n], li[data-task=p], li[data-task=c], li[data-task=w], li[data-task=b], li[data-task=I], li[data-task="!"], li[data-task="?"], li[data-task=i], li[data-task="/"], li[data-task=u], li[data-task=d], li[data-task=F], li[data-task=r], li[data-task=m], li[data-task=M], li[data-task=L], li[data-task=t], li[data-task=T], li[data-task=P]) input[type=checkbox]::after,
.markdown-source-view input[type=checkbox]:where([data-task="*"], [data-task=a], [data-task=f], [data-task=S], [data-task="-"], [data-task=">"], [data-task="<"], [data-task=l], [data-task=B], [data-task=X], [data-task=n], [data-task=p], [data-task=c], [data-task=w], [data-task=b], [data-task=I], [data-task="!"], [data-task="?"], [data-task=i], [data-task="/"], [data-task=u], [data-task=d], [data-task=F], [data-task=r], [data-task=m], [data-task=M], [data-task=L], [data-task=t], [data-task=T], [data-task=P]):checked::after {
  -webkit-mask-size: 110%;
  pointer-events: none;
  left: 0px;
}
.markdown-source-view.mod-cm6 input[type=checkbox][data-task] {
  top: -3px;
}
li[data-task="*"] input[type=checkbox]:checked::after,
input[type=checkbox][data-task="*"]::after {
  background-color: var(--yellow);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='m16 2-4.55 9.22-10.17 1.47 7.36 7.18L6.9 30l9.1-4.78L25.1 30l-1.74-10.13 7.36-7.17-10.17-1.48Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
li[data-task=a] input[type=checkbox]:checked::after,
input[type=checkbox][data-task=a]::after {
  background-color: var(--yellow);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32'%3E%3Cpath d='M28.707 19.293 26 16.586V13a10.014 10.014 0 0 0-9-9.95V1h-2v2.05A10.014 10.014 0 0 0 6 13v3.586l-2.707 2.707A1 1 0 0 0 3 20v3a1 1 0 0 0 1 1h7v1a5 5 0 0 0 10 0v-1h7a1 1 0 0 0 1-1v-3a1 1 0 0 0-.293-.707ZM19 25a3 3 0 0 1-6 0v-1h6Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
li[data-task=f] input[type=checkbox]:checked::after,
input[type=checkbox][data-task=f]::after {
  background-color: var(--red);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xml:space='preserve' width='32' height='32' enable-background='new 0 0 32 32'%3E%3Cpath d='M22.5 4c-2 0-3.9.8-5.3 2.2L16 7.4l-1.1-1.1c-2.9-3-7.7-3-10.6-.1l-.1.1c-3 3-3 7.8 0 10.8L16 29l11.8-11.9c3-3 3-7.8 0-10.8C26.4 4.8 24.5 4 22.5 4z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
}
li[data-task=S] input[type=checkbox]:checked::after,
input[type=checkbox][data-task=S]::after {
  background-color: var(--yellow);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' xml:space='preserve'%3E%3Cpath fill='none' d='M0 0h32v32H0V0z'/%3E%3Cpath d='M29 13h-2c-.1-1.6-.8-3.1-2-4.1V5c0-.6-.4-1-1-1-.2 0-.4.1-.6.2L19.7 7H15c-5.5 0-9.5 3.2-9.9 8H5c-.6 0-1-.4-1-1v-2H2v2c0 1.7 1.3 3 3 3h.1c.3 2.7 1.7 5 3.9 6.6V27c0 .6.4 1 1 1h4c.6 0 1-.4 1-1v-2h3v2c0 .6.4 1 1 1h4c.6 0 1-.4 1-1v-3.4c1.5-.7 2.5-2 2.8-3.6H29c.6 0 1-.4 1-1v-5c0-.6-.4-1-1-1zm-9 0h-7v-2h7v2z'/%3E%3C/svg%3E");
}
li[data-task="-"] input[type=checkbox]:checked::after,
input[type=checkbox][data-task="-"]::after {
  background-color: var(--red);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon' fill='rgb(8, 8, 8)' viewBox='0 0 32 32' xml:space='preserve'%3E%3Cstyle%3E.st0%7Bfill:none;%7D%3C/style%3E%3Cpath id='inner-path' d='M9,10.6L10.6,9L23,21.4L21.4,23L9,10.6z' class='st0'/%3E%3Cpath d='M6.1,6.1C0.6,11.5,0.6,20.3,6,25.8c0,0,0.1,0.1,0.1,0.1c5.4,5.5,14.2,5.5,19.7,0.1c0,0,0.1-0.1,0.1-0.1	c5.5-5.4,5.5-14.2,0.1-19.7c0,0-0.1-0.1-0.1-0.1C20.5,0.6,11.7,0.6,6.2,6C6.2,6,6.1,6.1,6.1,6.1z M24.8,17.1l-17.6,0v-2.2l17.6,0	V17.1z'/%3E%3Cpath id='_Transparent_Rectangle_' d='M0,0h32v32H0V0z' class='st0'/%3E%3C/svg%3E");
}
li[data-task=">"] input[type=checkbox]:checked::after,
input[type=checkbox][data-task=">"]::after {
  background-color: var(--cyan);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='m27.45 15.11-22-11a1 1 0 0 0-1.08.12 1 1 0 0 0-.33 1L6.69 15H18v2H6.69L4 26.74A1 1 0 0 0 5 28a1 1 0 0 0 .45-.11l22-11a1 1 0 0 0 0-1.78Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
li[data-task="<"] input[type=checkbox]:checked::after,
input[type=checkbox][data-task="<"]::after {
  background-color: var(--lavender-60);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M26 4h-4V2h-2v2h-8V2h-2v2H6a2.002 2.002 0 0 0-2 2v20a2.002 2.002 0 0 0 2 2h20a2.002 2.002 0 0 0 2-2V6a2.002 2.002 0 0 0-2-2ZM6 6h4v2h2V6h8v2h2V6h4v4H6Zm0 6h5v6H6Zm13 14h-6v-6h6Zm0-8h-6v-6h6Zm2 8v-6h5l.001 6Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
.theme-dark li[data-task="<"] input[type=checkbox]:checked::after,
.theme-dark input[type=checkbox][data-task="<"]::after {
  background-color: var(--lavender);
}
li[data-task=l] input[type=checkbox]:checked::after,
input[type=checkbox][data-task=l]::after {
  background-color: var(--red);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon' width='32' height='32'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:none%7D%3C/style%3E%3C/defs%3E%3Cpath d='M16 2A11.013 11.013 0 0 0 5 13a10.889 10.889 0 0 0 2.216 6.6s.3.395.349.452L16 30l8.439-9.953c.044-.053.345-.447.345-.447l.001-.003A10.885 10.885 0 0 0 27 13 11.013 11.013 0 0 0 16 2Zm0 15a4 4 0 1 1 4-4 4.005 4.005 0 0 1-4 4Z'/%3E%3Ccircle id='_Inner-Path_' cx='16' cy='13' r='4' class='cls-1' data-name='&lt;Inner-Path&gt;'/%3E%3Cpath id='_Transparent_Rectangle_' d='M0 0h32v32H0z' class='cls-1' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
li[data-task=B] input[type=checkbox]:checked::after,
input[type=checkbox][data-task=B]::after {
  background-color: var(--pink);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' xml:space='preserve'%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3Cpath d='m16 11 8-2.1c-.9-1.7-2.3-3-4-3.9V2h-2v2.2c-1.3-.3-2.7-.3-4 0V2h-2v3c-1.7.9-3.1 2.2-4 3.9l8 2.1z'/%3E%3Cpath d='m29.8 20 .3-2-5.1-.8v-4.4l5.1-1.4-.5-1.9-4.8 1.3-7.8 2V17h-2v-4.2l-7.7-2.1-4.8-1.2-.5 1.9 5 1.4v4.4l-5.2.8.3 2 4.9-.8c0 1.2.3 2.4.8 3.6l-4.5 4.5 1.4 1.4 4.2-4.2c3.1 3.9 8.7 4.6 12.6 1.6.6-.5 1.1-1 1.6-1.6l4.2 4.2 1.4-1.4-4.5-4.5c.5-1.1.8-2.3.8-3.6l4.8.8z'/%3E%3C/svg%3E");
}
li[data-task=X] input[type=checkbox]:checked::after,
input[type=checkbox][data-task=X]::after {
  background-color: var(--red);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' xml:space='preserve'%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3Cpath d='M16 2C8.3 2 2 8.3 2 16s6.3 14 14 14 14-6.3 14-14S23.7 2 16 2zm5.4 21L16 17.6 10.6 23 9 21.4l5.4-5.4L9 10.6 10.6 9l5.4 5.4L21.4 9l1.6 1.6-5.4 5.4 5.4 5.4-1.6 1.6z'/%3E%3C/svg%3E");
}
li[data-task=n] input[type=checkbox]:checked::after,
input[type=checkbox][data-task=n]::after {
  background-color: var(--cyan);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28.586 13.314 30 11.9 20 2l-1.314 1.415 1.186 1.186L8.38 14.322l-1.716-1.715L5.25 14l5.657 5.677L2 28.583 3.41 30l8.911-8.909L18 26.748l1.393-1.414-1.716-1.716 9.724-11.49Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
li[data-task=p] input[type=checkbox]:checked::after,
input[type=checkbox][data-task=p]::after {
  background-color: var(--green);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M2 16h5v14H2zM23 30H9V15.197l3.042-4.563.845-5.917A2.01 2.01 0 0 1 14.867 3H15a3.003 3.003 0 0 1 3 3v6h8a4.005 4.005 0 0 1 4 4v7a7.008 7.008 0 0 1-7 7Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
li[data-task=c] input[type=checkbox]:checked::after,
input[type=checkbox][data-task=c]::after {
  background-color: var(--red);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M2 2h5v14H2zM23 2H9v14.803l3.042 4.563.845 5.917A2.01 2.01 0 0 0 14.867 29H15a3.003 3.003 0 0 0 3-3v-6h8a4.005 4.005 0 0 0 4-4V9a7.008 7.008 0 0 0-7-7Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
li[data-task=w] input[type=checkbox]:checked::after,
input[type=checkbox][data-task=w]::after {
  background-color: var(--yellow);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M26 7h-2V6a2.002 2.002 0 0 0-2-2H10a2.002 2.002 0 0 0-2 2v1H6a2.002 2.002 0 0 0-2 2v3a4.005 4.005 0 0 0 4 4h.322A8.169 8.169 0 0 0 15 21.934V26h-5v2h12v-2h-5v-4.069A7.966 7.966 0 0 0 23.74 16H24a4.005 4.005 0 0 0 4-4V9a2.002 2.002 0 0 0-2-2ZM8 14a2.002 2.002 0 0 1-2-2V9h2Zm18-2a2.002 2.002 0 0 1-2 2V9h2Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
li[data-task=b] input[type=checkbox]:checked::after,
input[type=checkbox][data-task=b]::after {
  background-color: var(--red);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M24 2H8a2 2 0 0 0-2 2v26l10-5.054L26 30V4a2 2 0 0 0-2-2Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
li[data-task=I] input[type=checkbox]:checked::after,
input[type=checkbox][data-task=I]::after {
  background-color: var(--yellow);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' xml:space='preserve'%3E%3Cpath d='M11 22h10v4H11v-4zm2 4h6v4h-6v-4zm8-4c0-.9.5-1.5 1.5-2.4 2.3-1.8 3.6-4.7 3.5-7.6 0-5.5-4.5-10-10-10S6 6.5 6 12c-.1 2.9 1.1 5.8 3.5 7.6 1 .9 1.5 1.5 1.5 2.4'/%3E%3Cpath fill='none' d='M0 0h32v32H0V0z'/%3E%3C/svg%3E");
}
li[data-task="!"] input[type=checkbox]:checked::after,
input[type=checkbox][data-task="!"]::after {
  background-color: var(--red);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' xml:space='preserve'%3E%3Cstyle%3E.st0%7Bfill:none%7D%3C/style%3E%3Cpath id='inner-path' d='M16 26c-.8 0-1.5-.7-1.5-1.5S15.2 23 16 23s1.5.7 1.5 1.5S16.8 26 16 26zm-1.1-5h2.2v-9h-2.2v9z' class='st0'/%3E%3Cpath d='M16 6.2 4.6 28h22.7L16 6.2zM14.9 12h2.2v9h-2.2v-9zM16 26c-.8 0-1.5-.7-1.5-1.5S15.2 23 16 23s1.5.7 1.5 1.5S16.8 26 16 26z'/%3E%3Cpath d='M29 30H3c-.6 0-1-.4-1-1 0-.2 0-.3.1-.5l13-25c.3-.5.9-.6 1.4-.4.2.1.3.2.4.4l13 25c.3.5.1 1.1-.4 1.3-.2.2-.3.2-.5.2zM4.7 28h22.7L16 6.2 4.7 28z'/%3E%3Cpath id='_Transparent_Rectangle_' d='M0 0h32v32H0z' class='st0'/%3E%3Cpath d='M-2.9 15.3h26.4v2.9H-2.9z' transform='rotate(-62.979 10.321 16.712)'/%3E%3Cpath d='M8.3 15.6h26.4v2.9H8.3z' transform='rotate(-117.021 21.471 17.086)'/%3E%3Cpath d='M3.9 26.2h24.4v2.9H3.9z' transform='matrix(-.9999 -.01155 .01155 -.9999 31.881 55.41)'/%3E%3C/svg%3E");
}
li[data-task="?"] input[type=checkbox]:checked::after,
input[type=checkbox][data-task="?"]::after {
  background-color: var(--orange);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:none%7D%3C/style%3E%3C/defs%3E%3Cpath d='M16 2a14 14 0 1 0 14 14A14 14 0 0 0 16 2Zm0 23a1.5 1.5 0 1 1 1.5-1.5A1.5 1.5 0 0 1 16 25Zm1.142-7.754v2.501h-2.25V15h2.125a2.376 2.376 0 0 0 0-4.753h-1.5a2.378 2.378 0 0 0-2.375 2.375v.638h-2.25v-.638A4.628 4.628 0 0 1 15.517 8h1.5a4.624 4.624 0 0 1 .125 9.246Z'/%3E%3Cpath id='inner-path' d='M16 25a1.5 1.5 0 1 1 1.5-1.5A1.5 1.5 0 0 1 16 25Zm1.142-7.754v2.501h-2.25V15h2.125a2.376 2.376 0 0 0 0-4.753h-1.5a2.378 2.378 0 0 0-2.375 2.375v.638h-2.25v-.638A4.628 4.628 0 0 1 15.517 8h1.5a4.624 4.624 0 0 1 .125 9.246Z' class='cls-1'/%3E%3Cpath id='_Transparent_Rectangle_' d='M0 0h32v32H0z' class='cls-1' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
li[data-task=i] input[type=checkbox]:checked::after,
input[type=checkbox][data-task=i]::after {
  background-color: var(--cyan);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:none%7D%3C/style%3E%3C/defs%3E%3Cpath id='inner-path' d='M16 8a1.5 1.5 0 1 1-1.5 1.5A1.5 1.5 0 0 1 16 8Zm4 13.875h-2.875v-8H13v2.25h1.875v5.75H12v2.25h8Z' class='cls-1'/%3E%3Cpath d='M16 2a14 14 0 1 0 14 14A14 14 0 0 0 16 2Zm0 6a1.5 1.5 0 1 1-1.5 1.5A1.5 1.5 0 0 1 16 8Zm4 16.125h-8v-2.25h2.875v-5.75H13v-2.25h4.125v8H20Z'/%3E%3Cpath id='_Transparent_Rectangle_' d='M0 0h32v32H0z' class='cls-1' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
li[data-task="/"] input[type=checkbox]:checked::after,
input[type=checkbox][data-task="/"]::after {
  background-color: var(--checkbox-color);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M16 2a14 14 0 1 0 14 14A14.016 14.016 0 0 0 16 2Zm0 26a12 12 0 0 1 0-24v12l8.481 8.481A11.963 11.963 0 0 1 16 28Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
li[data-task=u] input[type=checkbox]:checked::after,
input[type=checkbox][data-task=u]::after {
  background-color: var(--green);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M20 8v2h6.586L18 18.586l-4.293-4.293a1 1 0 0 0-1.414 0L2 24.586 3.414 26 13 16.414l4.293 4.293a1 1 0 0 0 1.414 0L28 11.414V18h2V8Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
li[data-task=d] input[type=checkbox]:checked::after,
input[type=checkbox][data-task=d]::after {
  background-color: var(--red);
  transform: scaleY(-1);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M20 8v2h6.586L18 18.586l-4.293-4.293a1 1 0 0 0-1.414 0L2 24.586 3.414 26 13 16.414l4.293 4.293a1 1 0 0 0 1.414 0L28 11.414V18h2V8Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
li[data-task=F] input[type=checkbox]:checked::after,
input[type=checkbox][data-task=F]::after {
  background-color: var(--orange);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M11.61 29.92a1 1 0 0 1-.6-1.07L12.83 17H8a1 1 0 0 1-1-1.23l3-13A1 1 0 0 1 11 2h10a1 1 0 0 1 .78.37 1 1 0 0 1 .2.85L20.25 11H25a1 1 0 0 1 .9.56 1 1 0 0 1-.11 1l-13 17A1 1 0 0 1 12 30a1.09 1.09 0 0 1-.39-.08Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;' transform='rotate(-180 16 16)'/%3E%3C/svg%3E");
}
li[data-task=r] input[type=checkbox]:checked::after,
input[type=checkbox][data-task=r]::after {
  background-color: var(--cyan);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' data-name='Layer 1'%3E%3Cpath d='M20 16a5 5 0 0 0 10 0 1 1 0 0 0-.105-.447l-3.999-7.997a.891.891 0 0 0-.045-.081A1 1 0 0 0 25 7h-6.178A3.015 3.015 0 0 0 17 5.184V2h-2v3.184A3.015 3.015 0 0 0 13.178 7H7a1 1 0 0 0-.894.553l-4 8A1 1 0 0 0 2 16a5 5 0 0 0 10 0 1 1 0 0 0-.105-.447L8.617 9h4.56A3.015 3.015 0 0 0 15 10.815V28H6v2h20v-2h-9V10.816A3.015 3.015 0 0 0 18.822 9h4.56l-3.277 6.553A1 1 0 0 0 20 16ZM7 19a2.996 2.996 0 0 1-2.815-2h5.63A2.996 2.996 0 0 1 7 19Zm2.382-4H4.618L7 10.236ZM16 9a1 1 0 1 1 1-1 1 1 0 0 1-1 1Zm9 10a2.996 2.996 0 0 1-2.815-2h5.63A2.996 2.996 0 0 1 25 19Zm0-8.764L27.382 15h-4.764Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
li[data-task=m] input[type=checkbox]:checked::after,
input[type=checkbox][data-task=m]::after {
  top: 0px;
  background-color: var(--orange);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M29 10H3a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h26a1 1 0 0 0 1-1V11a1 1 0 0 0-1-1Zm-1 10H4v-8h4v4h2v-4h5v4h2v-4h5v4h2v-4h4Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
li[data-task=M] input[type=checkbox]:checked::after,
input[type=checkbox][data-task=M]::after {
  background-color: var(--cyan);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32'%3E%3Cpath d='M24 2v2h2v6a4 4 0 0 1-8 0V4h2V2h-4v8a6.005 6.005 0 0 0 5 5.91V22a6 6 0 0 1-12 0v-6.142a4 4 0 1 0-2 0V22a8 8 0 0 0 16 0v-6.09A6.005 6.005 0 0 0 28 10V2ZM6 12a2 2 0 1 1 2 2 2.002 2.002 0 0 1-2-2Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
li[data-task=L] input[type=checkbox]:checked::after,
input[type=checkbox][data-task=L]::after {
  background-color: var(--green);
  -webkit-mask-size: 100%;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M27.85 29H30l-6-15h-2.35l-6 15h2.15l1.6-4h6.85Zm-7.65-6 2.62-6.56L25.45 23ZM18 7V5h-7V2H9v3H2v2h10.74a14.71 14.71 0 0 1-3.19 6.18A13.5 13.5 0 0 1 7.26 9h-2.1a16.47 16.47 0 0 0 3 5.58A16.84 16.84 0 0 1 3 18l.75 1.86A18.47 18.47 0 0 0 9.53 16a16.92 16.92 0 0 0 5.76 3.84L16 18a14.48 14.48 0 0 1-5.12-3.37A17.64 17.64 0 0 0 14.8 7Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
li[data-task=t] input[type=checkbox]:checked::after,
input[type=checkbox][data-task=t]::after {
  background-color: var(--orange);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M16 30a14 14 0 1 1 14-14 14 14 0 0 1-14 14Zm0-26a12 12 0 1 0 12 12A12 12 0 0 0 16 4Z'/%3E%3Cpath d='M20.59 22 15 16.41V7h2v8.58l5 5.01L20.59 22z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
li[data-task=T] input[type=checkbox]:checked::after,
input[type=checkbox][data-task=T]::after {
  background-color: var(--green);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32'%3E%3Cpath d='M22 4H10a2.002 2.002 0 0 0-2 2v22a2.002 2.002 0 0 0 2 2h12a2.003 2.003 0 0 0 2-2V6a2.002 2.002 0 0 0-2-2Zm0 2v2H10V6ZM10 28V10h12v18Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
li[data-task=P] inPut[type=checkbox]:checked::after,
input[type=checkbox][data-task=P]::after {
  background-color: var(--cyan);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M16 4a5 5 0 1 1-5 5 5 5 0 0 1 5-5m0-2a7 7 0 1 0 7 7 7 7 0 0 0-7-7Zm10 28h-2v-5a5 5 0 0 0-5-5h-6a5 5 0 0 0-5 5v5H6v-5a7 7 0 0 1 7-7h6a7 7 0 0 1 7 7Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-file-plus,
body:not(.no-sanctum-icons) svg.lucide-plus-circle,
body:not(.no-sanctum-icons) svg.lucide-file-audio,
body:not(.no-sanctum-icons) svg.lucide-layout-list,
body:not(.no-sanctum-icons) svg.lucide-bold,
body:not(.no-sanctum-icons) svg.box-glyph,
body:not(.no-sanctum-icons) svg.bracket-glyph,
body:not(.no-sanctum-icons) svg.broken-link,
body:not(.no-sanctum-icons) svg.lucide-list,
body:not(.no-sanctum-icons) svg.bullet-list,
body:not(.no-sanctum-icons) svg.lucide-calendar-days,
body:not(.no-sanctum-icons) svg.lucide-calendar-check,
body:not(.no-sanctum-icons) svg.lucide-check-circle-2,
body:not(.no-sanctum-icons) svg.check-small,
body:not(.no-sanctum-icons) svg.lucide-check-square,
body:not(.no-sanctum-icons) svg.lucide-check,
body:not(.no-sanctum-icons) svg.lucide-clock,
body:not(.no-sanctum-icons) svg.clock,
body:not(.no-sanctum-icons) svg.lucide-cloud,
body:not(.no-sanctum-icons) svg.lucide-code-2,
body:not(.no-sanctum-icons) svg.lucide-terminal-square,
body:not(.no-sanctum-icons) svg.lucide-minimize-2,
body:not(.no-sanctum-icons) svg.lucide-edit,
body:not(.no-sanctum-icons) svg.lucide-x-square,
body:not(.no-sanctum-icons) svg.lucide-x,
body:not(.no-sanctum-icons) svg.lucide-star-off,
body:not(.no-sanctum-icons) svg.dice-glyph,
body:not(.no-sanctum-icons) svg.dice,
body:not(.no-sanctum-icons) svg.lucide-file,
body:not(.no-sanctum-icons) svg.lucide-files,
body:not(.no-sanctum-icons) svg.lucide-git-fork,
body:not(.no-sanctum-icons) svg.lucide-chevrons-down,
body:not(.no-sanctum-icons) svg.lucide-chevrons-up,
body:not(.no-sanctum-icons) svg.lucide-arrow-down,
body:not(.no-sanctum-icons) svg.lucide-chevron-down,
body:not(.no-sanctum-icons) svg.lucide-corner-right-down,
body:not(.no-sanctum-icons) svg.lucide-copy,
body:not(.no-sanctum-icons) svg.lucide-maximize-2,
body:not(.no-sanctum-icons) svg.lucide-log-in,
body:not(.no-sanctum-icons) svg.lucide-minimize,
body:not(.no-sanctum-icons) svg.lucide-move-vertical,
body:not(.no-sanctum-icons) svg.lucide-files,
body:not(.no-sanctum-icons) svg.lucide-pin,
body:not(.no-sanctum-icons) svg.lucide-folder-plus,
body:not(.no-sanctum-icons) svg.lucide-folder-open,
body:not(.no-sanctum-icons) svg.lucide-folder-closed,
body:not(.no-sanctum-icons) svg.lucide-forward,
body:not(.no-sanctum-icons) svg.lucide-maximize,
body:not(.no-sanctum-icons) svg.lucide-settings,
body:not(.no-sanctum-icons) svg.lucide-file-code,
body:not(.no-sanctum-icons) svg.lucide-file-input,
body:not(.no-sanctum-icons) svg.lucide-git-fork,
body:not(.no-sanctum-icons) svg.lucide-hash,
body:not(.no-sanctum-icons) svg.heading-glyph,
body:not(.no-sanctum-icons) svg.help,
body:not(.no-sanctum-icons) svg.lucide-highlighter,
body:not(.no-sanctum-icons) svg.lucide-separator-horizontal,
body:not(.no-sanctum-icons) svg.lucide-image,
body:not(.no-sanctum-icons) svg.lucide-paperclip,
body:not(.no-sanctum-icons) svg.lucide-download,
body:not(.no-sanctum-icons) svg.lucide-indent,
body:not(.no-sanctum-icons) svg.lucide-info,
body:not(.no-sanctum-icons) svg.lucide-download-cloud,
body:not(.no-sanctum-icons) svg.lucide-italic,
body:not(.no-sanctum-icons) svg.lucide-keyboard,
body:not(.no-sanctum-icons) svg.lucide-languages,
body:not(.no-sanctum-icons) svg.lucide-arrow-left,
body:not(.no-sanctum-icons) svg.left-arrow,
body:not(.no-sanctum-icons) svg.lucide-chevron-left,
body:not(.no-sanctum-icons) svg.lucide-align-left,
body:not(.no-sanctum-icons) svg.link-glyph,
body:not(.no-sanctum-icons) svg.lucide-link,
body:not(.no-sanctum-icons) svg.link,
body:not(.no-sanctum-icons) svg.links-coming-in,
body:not(.no-sanctum-icons) svg.links-going-out,
body:not(.no-sanctum-icons) svg.lucide-search,
body:not(.no-sanctum-icons) svg.lucide-file-search,
body:not(.no-sanctum-icons) svg.lucide-git-merge,
body:not(.no-sanctum-icons) svg.merge-files,
body:not(.no-sanctum-icons) svg.lucide-mic,
body:not(.no-sanctum-icons) svg.microphone,
body:not(.no-sanctum-icons) svg.lucide-minus-circle,
body:not(.no-sanctum-icons) svg.lucide-navigation,
body:not(.no-sanctum-icons) svg.lucide-sticky-note,
body:not(.no-sanctum-icons) svg.lucide-list-ordered,
body:not(.no-sanctum-icons) svg.lucide-arrow-up-right,
body:not(.no-sanctum-icons) svg.open-vault,
body:not(.no-sanctum-icons) svg.lucide-layout,
body:not(.no-sanctum-icons) svg.lucide-send,
body:not(.no-sanctum-icons) svg.paper-plane,
body:not(.no-sanctum-icons) svg.lucide-clipboard-type,
body:not(.no-sanctum-icons) svg.lucide-clipboard-check,
body:not(.no-sanctum-icons) svg.paused,
body:not(.no-sanctum-icons) svg.lucide-file-text,
body:not(.no-sanctum-icons) svg.lucide-file-down,
body:not(.no-sanctum-icons) svg.lucide-edit-3,
body:not(.no-sanctum-icons) svg.lucide-percent,
body:not(.no-sanctum-icons) svg.pin,
body:not(.no-sanctum-icons) svg.lucide-play-circle,
body:not(.no-sanctum-icons) svg.lucide-diff,
body:not(.no-sanctum-icons) svg.plus-with-circle,
body:not(.no-sanctum-icons) svg.popup-open,
body:not(.no-sanctum-icons) svg.lucide-monitor,
body:not(.no-sanctum-icons) svg.presentation,
body:not(.no-sanctum-icons) svg.lucide-tag,
body:not(.no-sanctum-icons) svg.question-mark-glyph,
body:not(.no-sanctum-icons) svg.lucide-quote,
body:not(.no-sanctum-icons) svg.lucide-redo-2,
body:not(.no-sanctum-icons) svg.lucide-rotate-ccw,
body:not(.no-sanctum-icons) svg.restore-file-glyph,
body:not(.no-sanctum-icons) svg.lucide-arrow-right,
body:not(.no-sanctum-icons) svg.right-arrow,
body:not(.no-sanctum-icons) svg.lucide-chevron-right,
body:not(.no-sanctum-icons) svg.right-triangle,
body:not(.no-sanctum-icons) svg.lucide-terminal,
body:not(.no-sanctum-icons) svg.lucide-scissors,
body:not(.no-sanctum-icons) svg.scissors,
body:not(.no-sanctum-icons) svg.search-glyph,
body:not(.no-sanctum-icons) svg.search,
body:not(.no-sanctum-icons) svg.lucide-box-select,
body:not(.no-sanctum-icons) svg.sheets-in-box,
body:not(.no-sanctum-icons) svg.lucide-git-branch-plus,
body:not(.no-sanctum-icons) svg.spreadsheet,
body:not(.no-sanctum-icons) svg.lucide-folder-tree,
body:not(.no-sanctum-icons) svg.lucide-star,
body:not(.no-sanctum-icons) svg.star-list,
body:not(.no-sanctum-icons) svg.star,
body:not(.no-sanctum-icons) svg.lucide-stop-circle,
body:not(.no-sanctum-icons) svg.lucide-strikethrough,
body:not(.no-sanctum-icons) svg.lucide-repeat,
body:not(.no-sanctum-icons) svg.sync-small,
body:not(.no-sanctum-icons) svg.lucide-refresh-cw,
body:not(.no-sanctum-icons) svg.tag-glyph,
body:not(.no-sanctum-icons) svg.lucide-menu,
body:not(.no-sanctum-icons) svg.lucide-calendar-plus,
body:not(.no-sanctum-icons) svg.lucide-calendar,
body:not(.no-sanctum-icons) svg.lucide-trash-2,
body:not(.no-sanctum-icons) svg.lucide-copy,
body:not(.no-sanctum-icons) svg.lucide-undo-2,
body:not(.no-sanctum-icons) svg.lucide-outdent,
body:not(.no-sanctum-icons) svg.lucide-move-vertical,
body:not(.no-sanctum-icons) svg.lucide-arrow-up,
body:not(.no-sanctum-icons) svg.lucide-chevron-up,
body:not(.no-sanctum-icons) svg.lucide-corner-right-up,
body:not(.no-sanctum-icons) svg.uppercase-lowercase-a,
body:not(.no-sanctum-icons) svg.vault,
body:not(.no-sanctum-icons) svg.lucide-separator-vertical,
body:not(.no-sanctum-icons) svg.lucide-more-vertical,
body:not(.no-sanctum-icons) svg.lucide-wand,
body:not(.no-sanctum-icons) svg.lucide-wand-2,
body:not(.no-sanctum-icons) svg.workspace-glyph,
body:not(.no-sanctum-icons) svg.lucide-wrench,
body:not(.no-sanctum-icons) svg.lucide-calendar-minus,
body:not(.no-sanctum-icons) svg.bar-graph,
body:not(.no-sanctum-icons) svg.BC-trail-icon,
body:not(.no-sanctum-icons) svg.longform,
body:not(.no-sanctum-icons) svg.sweep,
body:not(.no-sanctum-icons) svg.excalidraw-icon,
body:not(.no-sanctum-icons) svg.disk,
body:not(.no-sanctum-icons) svg.save-png,
body:not(.no-sanctum-icons) svg.save-svg,
body:not(.no-sanctum-icons) svg.fa-Images,
body:not(.no-sanctum-icons) svg.chart,
body:not(.no-sanctum-icons) svg.fa-search,
body:not(.no-sanctum-icons) svg.changelog,
body:not(.no-sanctum-icons) svg.fa-copy,
body:not(.no-sanctum-icons) svg.fantasy-calendar-reveal,
body:not(.no-sanctum-icons) svg.BratIcon,
body:not(.no-sanctum-icons) svg.running-man,
body:not(.no-sanctum-icons) svg.lucide-glasses,
body:not(.no-sanctum-icons) svg.calendar-day,
body:not(.no-sanctum-icons) svg.feather-tv,
body:not(.no-sanctum-icons) svg.sidebar-right,
body:not(.no-sanctum-icons) svg.sidebar-left,
body:not(.no-sanctum-icons) svg.lucide-history,
body:not(.no-sanctum-icons) svg.lucide-book-open {
  background-color: currentColor;
}
body:not(.no-sanctum-icons) svg.lucide-file-plus > path,
body:not(.no-sanctum-icons) svg.lucide-plus-circle > path,
body:not(.no-sanctum-icons) svg.lucide-file-audio > path,
body:not(.no-sanctum-icons) svg.lucide-layout-list > path,
body:not(.no-sanctum-icons) svg.lucide-bold > path,
body:not(.no-sanctum-icons) svg.box-glyph > path,
body:not(.no-sanctum-icons) svg.bracket-glyph > path,
body:not(.no-sanctum-icons) svg.broken-link > path,
body:not(.no-sanctum-icons) svg.lucide-list > path,
body:not(.no-sanctum-icons) svg.bullet-list > path,
body:not(.no-sanctum-icons) svg.lucide-calendar-days > path,
body:not(.no-sanctum-icons) svg.lucide-calendar-check > path,
body:not(.no-sanctum-icons) svg.lucide-check-circle-2 > path,
body:not(.no-sanctum-icons) svg.check-small > path,
body:not(.no-sanctum-icons) svg.lucide-check-square > path,
body:not(.no-sanctum-icons) svg.lucide-check > path,
body:not(.no-sanctum-icons) svg.lucide-clock > path,
body:not(.no-sanctum-icons) svg.clock > path,
body:not(.no-sanctum-icons) svg.lucide-cloud > path,
body:not(.no-sanctum-icons) svg.lucide-code-2 > path,
body:not(.no-sanctum-icons) svg.lucide-terminal-square > path,
body:not(.no-sanctum-icons) svg.lucide-minimize-2 > path,
body:not(.no-sanctum-icons) svg.lucide-edit > path,
body:not(.no-sanctum-icons) svg.lucide-x-square > path,
body:not(.no-sanctum-icons) svg.lucide-x > path,
body:not(.no-sanctum-icons) svg.lucide-star-off > path,
body:not(.no-sanctum-icons) svg.dice-glyph > path,
body:not(.no-sanctum-icons) svg.dice > path,
body:not(.no-sanctum-icons) svg.lucide-file > path,
body:not(.no-sanctum-icons) svg.lucide-files > path,
body:not(.no-sanctum-icons) svg.lucide-git-fork > path,
body:not(.no-sanctum-icons) svg.lucide-chevrons-down > path,
body:not(.no-sanctum-icons) svg.lucide-chevrons-up > path,
body:not(.no-sanctum-icons) svg.lucide-arrow-down > path,
body:not(.no-sanctum-icons) svg.lucide-chevron-down > path,
body:not(.no-sanctum-icons) svg.lucide-corner-right-down > path,
body:not(.no-sanctum-icons) svg.lucide-copy > path,
body:not(.no-sanctum-icons) svg.lucide-maximize-2 > path,
body:not(.no-sanctum-icons) svg.lucide-log-in > path,
body:not(.no-sanctum-icons) svg.lucide-minimize > path,
body:not(.no-sanctum-icons) svg.lucide-move-vertical > path,
body:not(.no-sanctum-icons) svg.lucide-files > path,
body:not(.no-sanctum-icons) svg.lucide-pin > path,
body:not(.no-sanctum-icons) svg.lucide-folder-plus > path,
body:not(.no-sanctum-icons) svg.lucide-folder-open > path,
body:not(.no-sanctum-icons) svg.lucide-folder-closed > path,
body:not(.no-sanctum-icons) svg.lucide-forward > path,
body:not(.no-sanctum-icons) svg.lucide-maximize > path,
body:not(.no-sanctum-icons) svg.lucide-settings > path,
body:not(.no-sanctum-icons) svg.lucide-file-code > path,
body:not(.no-sanctum-icons) svg.lucide-file-input > path,
body:not(.no-sanctum-icons) svg.lucide-git-fork > path,
body:not(.no-sanctum-icons) svg.lucide-hash > path,
body:not(.no-sanctum-icons) svg.heading-glyph > path,
body:not(.no-sanctum-icons) svg.help > path,
body:not(.no-sanctum-icons) svg.lucide-highlighter > path,
body:not(.no-sanctum-icons) svg.lucide-separator-horizontal > path,
body:not(.no-sanctum-icons) svg.lucide-image > path,
body:not(.no-sanctum-icons) svg.lucide-paperclip > path,
body:not(.no-sanctum-icons) svg.lucide-download > path,
body:not(.no-sanctum-icons) svg.lucide-indent > path,
body:not(.no-sanctum-icons) svg.lucide-info > path,
body:not(.no-sanctum-icons) svg.lucide-download-cloud > path,
body:not(.no-sanctum-icons) svg.lucide-italic > path,
body:not(.no-sanctum-icons) svg.lucide-keyboard > path,
body:not(.no-sanctum-icons) svg.lucide-languages > path,
body:not(.no-sanctum-icons) svg.lucide-arrow-left > path,
body:not(.no-sanctum-icons) svg.left-arrow > path,
body:not(.no-sanctum-icons) svg.lucide-chevron-left > path,
body:not(.no-sanctum-icons) svg.lucide-align-left > path,
body:not(.no-sanctum-icons) svg.link-glyph > path,
body:not(.no-sanctum-icons) svg.lucide-link > path,
body:not(.no-sanctum-icons) svg.link > path,
body:not(.no-sanctum-icons) svg.links-coming-in > path,
body:not(.no-sanctum-icons) svg.links-going-out > path,
body:not(.no-sanctum-icons) svg.lucide-search > path,
body:not(.no-sanctum-icons) svg.lucide-file-search > path,
body:not(.no-sanctum-icons) svg.lucide-git-merge > path,
body:not(.no-sanctum-icons) svg.merge-files > path,
body:not(.no-sanctum-icons) svg.lucide-mic > path,
body:not(.no-sanctum-icons) svg.microphone > path,
body:not(.no-sanctum-icons) svg.lucide-minus-circle > path,
body:not(.no-sanctum-icons) svg.lucide-navigation > path,
body:not(.no-sanctum-icons) svg.lucide-sticky-note > path,
body:not(.no-sanctum-icons) svg.lucide-list-ordered > path,
body:not(.no-sanctum-icons) svg.lucide-arrow-up-right > path,
body:not(.no-sanctum-icons) svg.open-vault > path,
body:not(.no-sanctum-icons) svg.lucide-layout > path,
body:not(.no-sanctum-icons) svg.lucide-send > path,
body:not(.no-sanctum-icons) svg.paper-plane > path,
body:not(.no-sanctum-icons) svg.lucide-clipboard-type > path,
body:not(.no-sanctum-icons) svg.lucide-clipboard-check > path,
body:not(.no-sanctum-icons) svg.paused > path,
body:not(.no-sanctum-icons) svg.lucide-file-text > path,
body:not(.no-sanctum-icons) svg.lucide-file-down > path,
body:not(.no-sanctum-icons) svg.lucide-edit-3 > path,
body:not(.no-sanctum-icons) svg.lucide-percent > path,
body:not(.no-sanctum-icons) svg.pin > path,
body:not(.no-sanctum-icons) svg.lucide-play-circle > path,
body:not(.no-sanctum-icons) svg.lucide-diff > path,
body:not(.no-sanctum-icons) svg.plus-with-circle > path,
body:not(.no-sanctum-icons) svg.popup-open > path,
body:not(.no-sanctum-icons) svg.lucide-monitor > path,
body:not(.no-sanctum-icons) svg.presentation > path,
body:not(.no-sanctum-icons) svg.lucide-tag > path,
body:not(.no-sanctum-icons) svg.question-mark-glyph > path,
body:not(.no-sanctum-icons) svg.lucide-quote > path,
body:not(.no-sanctum-icons) svg.lucide-redo-2 > path,
body:not(.no-sanctum-icons) svg.lucide-rotate-ccw > path,
body:not(.no-sanctum-icons) svg.restore-file-glyph > path,
body:not(.no-sanctum-icons) svg.lucide-arrow-right > path,
body:not(.no-sanctum-icons) svg.right-arrow > path,
body:not(.no-sanctum-icons) svg.lucide-chevron-right > path,
body:not(.no-sanctum-icons) svg.right-triangle > path,
body:not(.no-sanctum-icons) svg.lucide-terminal > path,
body:not(.no-sanctum-icons) svg.lucide-scissors > path,
body:not(.no-sanctum-icons) svg.scissors > path,
body:not(.no-sanctum-icons) svg.search-glyph > path,
body:not(.no-sanctum-icons) svg.search > path,
body:not(.no-sanctum-icons) svg.lucide-box-select > path,
body:not(.no-sanctum-icons) svg.sheets-in-box > path,
body:not(.no-sanctum-icons) svg.lucide-git-branch-plus > path,
body:not(.no-sanctum-icons) svg.spreadsheet > path,
body:not(.no-sanctum-icons) svg.lucide-folder-tree > path,
body:not(.no-sanctum-icons) svg.lucide-star > path,
body:not(.no-sanctum-icons) svg.star-list > path,
body:not(.no-sanctum-icons) svg.star > path,
body:not(.no-sanctum-icons) svg.lucide-stop-circle > path,
body:not(.no-sanctum-icons) svg.lucide-strikethrough > path,
body:not(.no-sanctum-icons) svg.lucide-repeat > path,
body:not(.no-sanctum-icons) svg.sync-small > path,
body:not(.no-sanctum-icons) svg.lucide-refresh-cw > path,
body:not(.no-sanctum-icons) svg.tag-glyph > path,
body:not(.no-sanctum-icons) svg.lucide-menu > path,
body:not(.no-sanctum-icons) svg.lucide-calendar-plus > path,
body:not(.no-sanctum-icons) svg.lucide-calendar > path,
body:not(.no-sanctum-icons) svg.lucide-trash-2 > path,
body:not(.no-sanctum-icons) svg.lucide-copy > path,
body:not(.no-sanctum-icons) svg.lucide-undo-2 > path,
body:not(.no-sanctum-icons) svg.lucide-outdent > path,
body:not(.no-sanctum-icons) svg.lucide-move-vertical > path,
body:not(.no-sanctum-icons) svg.lucide-arrow-up > path,
body:not(.no-sanctum-icons) svg.lucide-chevron-up > path,
body:not(.no-sanctum-icons) svg.lucide-corner-right-up > path,
body:not(.no-sanctum-icons) svg.uppercase-lowercase-a > path,
body:not(.no-sanctum-icons) svg.vault > path,
body:not(.no-sanctum-icons) svg.lucide-separator-vertical > path,
body:not(.no-sanctum-icons) svg.lucide-more-vertical > path,
body:not(.no-sanctum-icons) svg.lucide-wand > path,
body:not(.no-sanctum-icons) svg.lucide-wand-2 > path,
body:not(.no-sanctum-icons) svg.workspace-glyph > path,
body:not(.no-sanctum-icons) svg.lucide-wrench > path,
body:not(.no-sanctum-icons) svg.lucide-calendar-minus > path,
body:not(.no-sanctum-icons) svg.bar-graph > path,
body:not(.no-sanctum-icons) svg.BC-trail-icon > path,
body:not(.no-sanctum-icons) svg.longform > path,
body:not(.no-sanctum-icons) svg.sweep > path,
body:not(.no-sanctum-icons) svg.excalidraw-icon > path,
body:not(.no-sanctum-icons) svg.disk > path,
body:not(.no-sanctum-icons) svg.save-png > path,
body:not(.no-sanctum-icons) svg.save-svg > path,
body:not(.no-sanctum-icons) svg.fa-Images > path,
body:not(.no-sanctum-icons) svg.chart > path,
body:not(.no-sanctum-icons) svg.fa-search > path,
body:not(.no-sanctum-icons) svg.changelog > path,
body:not(.no-sanctum-icons) svg.fa-copy > path,
body:not(.no-sanctum-icons) svg.fantasy-calendar-reveal > path,
body:not(.no-sanctum-icons) svg.BratIcon > path,
body:not(.no-sanctum-icons) svg.running-man > path,
body:not(.no-sanctum-icons) svg.lucide-glasses > path,
body:not(.no-sanctum-icons) svg.calendar-day > path,
body:not(.no-sanctum-icons) svg.feather-tv > path,
body:not(.no-sanctum-icons) svg.sidebar-right > path,
body:not(.no-sanctum-icons) svg.sidebar-left > path,
body:not(.no-sanctum-icons) svg.lucide-history > path,
body:not(.no-sanctum-icons) svg.lucide-book-open > path {
  display: none;
}
body:not(.no-sanctum-icons) svg.lucide-file-plus {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M30 24h-4v-4h-2v4h-4v2h4v4h2v-4h4v-2z'/%3E%3Cpath d='M16 28H8V4h8v6a2.006 2.006 0 0 0 2 2h6v4h2v-6a.91.91 0 0 0-.3-.7l-7-7A.909.909 0 0 0 18 2H8a2.006 2.006 0 0 0-2 2v24a2.006 2.006 0 0 0 2 2h8Zm2-23.6 5.6 5.6H18Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-plus-circle {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M24 13a4 4 0 0 0 4-4V8a4 4 0 0 0-4-4h-1a4 4 0 0 0-4 4v3h-6V8a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v1a4 4 0 0 0 4 4h3v6H8a4 4 0 0 0-4 4v1a4 4 0 0 0 4 4h1a4 4 0 0 0 4-4v-3h6v3a4 4 0 0 0 4 4h1a4 4 0 0 0 4-4v-1a4 4 0 0 0-4-4h-3v-6Zm-3-5a2 2 0 0 1 2-2h1a2 2 0 0 1 2 2v1a2 2 0 0 1-2 2h-3ZM8 11a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h1a2 2 0 0 1 2 2v3H8Zm3 13a2 2 0 0 1-2 2H8a2 2 0 0 1-2-2v-1a2 2 0 0 1 2-2h3Zm8-5h-6v-6h6Zm2 2h3a2 2 0 0 1 2 2v1a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-file-audio {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M29 31a.999.999 0 0 1-.625-.22L23.65 27H20a1 1 0 0 1-1-1v-5a1 1 0 0 1 1-1h3.65l4.726-3.78A1 1 0 0 1 30 17v13a1 1 0 0 1-1 1Zm-8-6h3a1 1 0 0 1 .625.22L28 27.92v-8.84l-3.376 2.7A1 1 0 0 1 24 22h-3Z'/%3E%3Cpath d='M16 28H8V4h8v6a2.006 2.006 0 0 0 2 2h6v3h2v-5a.91.91 0 0 0-.3-.7l-7-7A.909.909 0 0 0 18 2H8a2.006 2.006 0 0 0-2 2v24a2.006 2.006 0 0 0 2 2h8Zm2-23.6 5.6 5.6H18Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-layout-list {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28 19H14.83l2.58-2.59L16 15l-5 5 5 5 1.41-1.41L14.83 21H28v-2z'/%3E%3Cpath d='M24 14v-4a1 1 0 0 0-.29-.71l-7-7A1 1 0 0 0 16 2H6a2 2 0 0 0-2 2v24a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2h-2v2H6V4h8v6a2 2 0 0 0 2 2h6v2Zm-8-4V4.41L21.59 10Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-bold {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M18.25 25H9V7h8.5a5.25 5.25 0 0 1 4 8.65A5.25 5.25 0 0 1 18.25 25ZM12 22h6.23a2.25 2.25 0 1 0 0-4.5H12Zm0-7.5h5.5a2.25 2.25 0 1 0 0-4.5H12Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.box-glyph {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M20 21h-8a2 2 0 0 1-2-2v-2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2Zm-8-4v2h8v-2Z'/%3E%3Cpath d='M28 4H4a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2v16a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2V12a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2Zm-2 24H6V12h20Zm2-18H4V6h24v4Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.bracket-glyph {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28 26h-3v-2h3V8h-3V6h3a2.002 2.002 0 0 1 2 2v16a2.003 2.003 0 0 1-2 2Z'/%3E%3Ccircle cx='23' cy='16' r='2'/%3E%3Ccircle cx='16' cy='16' r='2'/%3E%3Ccircle cx='9' cy='16' r='2'/%3E%3Cpath d='M7 26H4a2.002 2.002 0 0 1-2-2V8a2.002 2.002 0 0 1 2-2h3v2H4v16h3Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.broken-link {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='m3.584 5.002 1.414-1.415 3.416 3.415L7 8.417zm19.997 19.99 1.415-1.414 3.415 3.416-1.415 1.414zM11 2h2v4h-2zm-9 9h4v2H2zm24 8h4v2h-4zm-7 7h2v4h-2zm-2.42-4.93-3.71 3.72a4 4 0 1 1-5.66-5.66l3.72-3.72L9.51 14 5.8 17.72a6 6 0 0 0-.06 8.54A6 6 0 0 0 10 28a6.07 6.07 0 0 0 4.32-1.8L18 22.49Zm-1.17-10.14 3.72-3.72a4 4 0 1 1 5.66 5.66l-3.72 3.72L22.49 18l3.71-3.72a6 6 0 0 0 .06-8.54A6 6 0 0 0 22 4a6.07 6.07 0 0 0-4.32 1.8L14 9.51Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-list {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M10 6h18v2H10zm0 18h18v2H10zm0-9h18v2H10zm-6 0h2v2H4zm0-9h2v2H4zm0 18h2v2H4z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.bullet-list {
  transform: translateY(1px);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M10 6h18v2H10zm0 18h18v2H10zm0-9h18v2H10zm-6 0h2v2H4zm0-9h2v2H4zm0 18h2v2H4z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.calendar-day {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' xml:space='preserve'%3E%3Cpath d='M26 4h-4V2h-2v2h-8V2h-2v2H6c-1.1 0-2 .9-2 2v20c0 1.1.9 2 2 2h20c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 22H6V12h20v14zm0-16H6V6h4v2h2V6h8v2h2V6h4v4z'/%3E%3Cpath fill='none' d='M0 0h32v32H0V0z'/%3E%3Cpath d='M13.8 23.3v-1.5H16v-5.6h-.1l-1.7 2.2-1.2-.9 2-2.6h2.8v6.9h1.8v1.5h-5.8z'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-calendar-days {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M26 4h-4V2h-2v2h-8V2h-2v2H6a2.002 2.002 0 0 0-2 2v20a2.002 2.002 0 0 0 2 2h20a2.002 2.002 0 0 0 2-2V6a2.002 2.002 0 0 0-2-2ZM6 6h4v2h2V6h8v2h2V6h4v4H6Zm0 6h5v6H6Zm13 14h-6v-6h6Zm0-8h-6v-6h6Zm2 8v-6h5l.001 6Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-calendar-check {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M26 4h-4V2h-2v2h-8V2h-2v2H6a2.002 2.002 0 0 0-2 2v20a2.002 2.002 0 0 0 2 2h20a2.002 2.002 0 0 0 2-2V6a2.002 2.002 0 0 0-2-2ZM6 6h4v2h2V6h8v2h2V6h4v4H6Zm0 6h5v6H6Zm13 14h-6v-6h6Zm0-8h-6v-6h6Zm2 8v-6h5l.001 6Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-check-circle-2 {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:none%7D%3C/style%3E%3C/defs%3E%3Cpath d='M16 2a14 14 0 1 0 14 14A14 14 0 0 0 16 2Zm-2 19.59-5-5L10.59 15 14 18.41 21.41 11l1.596 1.586Z'/%3E%3Cpath id='inner-path' d='m14 21.591-5-5L10.591 15 14 18.409 21.41 11l1.595 1.585L14 21.591z' class='cls-1'/%3E%3Cpath id='_Transparent_Rectangle_' d='M0 0h32v32H0z' class='cls-1' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.check-small {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='m14 21.414-5-5.001L10.413 15 14 18.586 21.585 11 23 12.415l-9 8.999z'/%3E%3Cpath d='M16 2a14 14 0 1 0 14 14A14 14 0 0 0 16 2Zm0 26a12 12 0 1 1 12-12 12 12 0 0 1-12 12Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-check-square {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M26 4H6a2 2 0 0 0-2 2v20a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2ZM6 26V6h20v20Z'/%3E%3Cpath d='m14 21.5-5-4.96L10.59 15 14 18.35 21.41 11 23 12.58l-9 8.92z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-check {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='m13 24-9-9 1.414-1.414L13 21.171 26.586 7.586 28 9 13 24z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-clock {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M15 19h2v2h-2zm0 4h2v2h-2z'/%3E%3Cpath d='M23 11.67V4h3V2H6v2h3v7.67a2 2 0 0 0 .4 1.2L11.75 16 9.4 19.13a2 2 0 0 0-.4 1.2V28H6v2h20v-2h-3v-7.67a2 2 0 0 0-.4-1.2L20.25 16l2.35-3.13a2 2 0 0 0 .4-1.2ZM21 4v7H11V4Zm0 16.33V28H11v-7.67L14.25 16 12 13h8l-2.25 3Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.clock {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M16 30a14 14 0 1 1 14-14 14 14 0 0 1-14 14Zm0-26a12 12 0 1 0 12 12A12 12 0 0 0 16 4Z'/%3E%3Cpath d='M20.59 22 15 16.41V7h2v8.58l5 5.01L20.59 22z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-cloud {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M16 7a7.66 7.66 0 0 1 1.51.15 8 8 0 0 1 6.35 6.34l.26 1.35 1.35.24a5.5 5.5 0 0 1-1 10.92H7.5a5.5 5.5 0 0 1-1-10.92l1.34-.24.26-1.35A8 8 0 0 1 16 7m0-2a10 10 0 0 0-9.83 8.12A7.5 7.5 0 0 0 7.49 28h17a7.5 7.5 0 0 0 1.32-14.88 10 10 0 0 0-7.94-7.94A10.27 10.27 0 0 0 16 5Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-code-2 {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='m31 16-7 7-1.41-1.41L28.17 16l-5.58-5.59L24 9l7 7zM1 16l7-7 1.41 1.41L3.83 16l5.58 5.59L8 23l-7-7zm11.419 9.484L17.639 6l1.932.518L14.35 26z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;' transform='rotate(-90 16 16)'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-terminal-square {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M26 4.01H6a2 2 0 0 0-2 2v20a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2v-20a2 2 0 0 0-2-2Zm0 2v4H6v-4Zm-20 20v-14h20v14Z'/%3E%3Cpath d='m10.76 16.18 2.82 2.83-2.82 2.83 1.41 1.41 4.24-4.24-4.24-4.24-1.41 1.41z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-minimize-2 {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M4 18v2h6.586L2 28.582 3.414 30 12 21.414V28h2V18H4zM30 3.416 28.592 2 20 10.586V4h-2v10h10v-2h-6.586L30 3.416z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;' transform='rotate(-180 16 16)'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-edit {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M30 24h-4v-4h-2v4h-4v2h4v4h2v-4h4v-2z'/%3E%3Cpath d='M16 28H8V4h8v6a2.006 2.006 0 0 0 2 2h6v4h2v-6a.91.91 0 0 0-.3-.7l-7-7A.909.909 0 0 0 18 2H8a2.006 2.006 0 0 0-2 2v24a2.006 2.006 0 0 0 2 2h8Zm2-23.6 5.6 5.6H18Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-x-square {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' xml:space='preserve'%3E%3Cpath d='M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14 14-6.2 14-14S23.8 2 16 2zm0 26C9.4 28 4 22.6 4 16S9.4 4 16 4s12 5.4 12 12-5.4 12-12 12z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3Cpath d='M21.4 23 16 17.6 10.6 23 9 21.4l5.4-5.4L9 10.6 10.6 9l5.4 5.4L21.4 9l1.6 1.6-5.4 5.4 5.4 5.4z'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-x {
  transform: scale(1.4) translateY(1px);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M24 9.4 22.6 8 16 14.6 9.4 8 8 9.4l6.6 6.6L8 22.6 9.4 24l6.6-6.6 6.6 6.6 1.4-1.4-6.6-6.6L24 9.4z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-star-off {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M11.45 11.22 1.28 12.7l7.36 7.17L6.9 30l9.1-4.78V2l-4.55 9.22z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.dice-glyph {
  -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 100 100"><path fill="red" d="M79.167 12.5H20.833c-4.596 0 -8.333 3.738 -8.333 8.333v58.333c0 4.596 3.738 8.333 8.333 8.333h58.333c4.596 0 8.333 -3.738 8.333 -8.333V20.833c0 -4.596 -3.738 -8.333 -8.333 -8.333zM20.833 79.167V20.833h58.333l0.008 58.333H20.833z M39.583 33.333A6.25 6.25 0 0 1 33.333 39.583A6.25 6.25 0 0 1 27.083 33.333A6.25 6.25 0 0 1 39.583 33.333z M56.25 50A6.25 6.25 0 0 1 50 56.25A6.25 6.25 0 0 1 43.75 50A6.25 6.25 0 0 1 56.25 50z M72.917 66.667A6.25 6.25 0 0 1 66.667 72.917A6.25 6.25 0 0 1 60.417 66.667A6.25 6.25 0 0 1 72.917 66.667z"></path></svg>');
}
body:not(.no-sanctum-icons) svg.dice {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M22.59 19.41 26.17 23h-6.62l-4.37-7 4.37-7h6.62l-3.58 3.59L24 14l6-6-6-6-1.41 1.41L26.17 7h-6.62a2 2 0 0 0-1.69.94L14 14.11l-3.86-6.17A2 2 0 0 0 8.45 7H2v2h6.45l4.37 7-4.37 7H2v2h6.45a2 2 0 0 0 1.69-.94L14 17.89l3.86 6.17a2 2 0 0 0 1.69.94h6.62l-3.58 3.59L24 30l6-6-6-6Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
  transform: scale(0.9);
}
body:not(.no-sanctum-icons) svg.lucide-file {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' xml:space='preserve'%3E%3Cpath d='m25.7 9.3-7-7c-.2-.2-.4-.3-.7-.3H8c-1.1 0-2 .9-2 2v24c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V10c0-.3-.1-.5-.3-.7zM18 4.4l5.6 5.6H18V4.4zM24 28H8V4h8v6c0 1.1.9 2 2 2h6v16z'/%3E%3Cpath d='M10 22h12v2H10zm0-6h12v2H10z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-files {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28 10v18H10V10h18m0-2H10a2 2 0 0 0-2 2v18a2 2 0 0 0 2 2h18a2 2 0 0 0 2-2V10a2 2 0 0 0-2-2Z'/%3E%3Cpath d='M4 18H2V4a2 2 0 0 1 2-2h14v2H4Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-git-fork {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M26 22a3.955 3.955 0 0 0-2.02.567l-3.813-3.814a4.965 4.965 0 0 0 0-5.506l2.547-2.547A3.028 3.028 0 1 0 21.3 9.286l-2.547 2.547a4.965 4.965 0 0 0-5.506 0L9.433 8.019A3.955 3.955 0 0 0 10 6a4 4 0 1 0-4 4 3.955 3.955 0 0 0 2.02-.567l3.813 3.814a4.965 4.965 0 0 0 0 5.506l-3.814 3.814A3.956 3.956 0 0 0 6 22a4 4 0 1 0 4 4 3.955 3.955 0 0 0-.567-2.02l3.814-3.813a4.969 4.969 0 0 0 1.753.732v3.285a3 3 0 1 0 2 0v-3.285a4.969 4.969 0 0 0 1.753-.732l3.814 3.814A3.955 3.955 0 0 0 22 26a4 4 0 1 0 4-4Zm-10-9a3 3 0 1 1-3 3 3.003 3.003 0 0 1 3-3ZM4 6a2 2 0 1 1 2 2 2.002 2.002 0 0 1-2-2Zm2 22a2 2 0 1 1 2-2 2.002 2.002 0 0 1-2 2Zm20 0a2 2 0 1 1 2-2 2.003 2.003 0 0 1-2 2Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-chevrons-down {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' xml:space='preserve'%3E%3Cpath d='M16 18 6 8l1.4-1.4 8.6 8.6 8.6-8.6L26 8zM4 22h24v2H4z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-chevrons-up {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' xml:space='preserve'%3E%3Cpath d='M16 14 6 24l1.4 1.4 8.6-8.6 8.6 8.6L26 24zM4 8h24v2H4z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-arrow-down {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M24.59 16.59 17 24.17V4h-2v20.17l-7.59-7.58L6 18l10 10 10-10-1.41-1.41z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-chevron-down {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' xml:space='preserve'%3E%3Cpath d='M16 22 6 12l1.4-1.4 8.6 8.6 8.6-8.6L26 12z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-corner-right-down {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M13.414 17.586 18 22.172V8H8V6h10a2.002 2.002 0 0 1 2 2v14.172l4.586-4.586L26 19l-7 7-7-7Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-copy {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' xml:space='preserve'%3E%3Cpath d='M16 4c6.6 0 12 5.4 12 12s-5.4 12-12 12S4 22.6 4 16 9.4 4 16 4m0-2C8.3 2 2 8.3 2 16s6.3 14 14 14 14-6.3 14-14S23.7 2 16 2z'/%3E%3Cpath d='M24 15h-7V8h-2v7H8v2h7v7h2v-7h7z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-maximize-2 {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M20 2v2h6.586L18 12.582 19.414 14 28 5.414V12h2V2H20zm-6 17.416L12.592 18 4 26.586V20H2v10h10v-2H5.414L14 19.416z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;' transform='rotate(-180 16 16)'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-log-in {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M26 30H14a2 2 0 0 1-2-2v-3h2v3h12V4H14v3h-2V4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v24a2 2 0 0 1-2 2Z'/%3E%3Cpath d='M14.59 20.59 18.17 17H4v-2h14.17l-3.58-3.59L16 10l6 6-6 6-1.41-1.41z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-minimize {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M4 18v2h6.586L2 28.582 3.414 30 12 21.414V28h2V18H4zM30 3.416 28.592 2 20 10.586V4h-2v10h10v-2h-6.586L30 3.416z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;' transform='rotate(-180 16 16)'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-move-vertical {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M20 26h6v2h-6zm0-8h8v2h-8zm0-8h10v2H20zm-5-6h2v24h-2zm-4.414-.041L7 7.249 3.412 3.958 2 5.373 7 10l5-4.627-1.414-1.414z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-files {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28 10v18H10V10h18m0-2H10a2 2 0 0 0-2 2v18a2 2 0 0 0 2 2h18a2 2 0 0 0 2-2V10a2 2 0 0 0-2-2Z'/%3E%3Cpath d='M4 18H2V4a2 2 0 0 1 2-2h14v2H4Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-pin {
  width: 16px;
  height: 16px;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28.59 13.31 30 11.9 20 2l-1.31 1.42 1.18 1.18-11.49 9.72-1.72-1.71L5.25 14l5.66 5.68L2 28.58 3.41 30l8.91-8.91L18 26.75l1.39-1.42-1.71-1.71 9.72-11.49ZM16.26 22.2 9.8 15.74 21.29 6 26 10.71Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-folder-plus {
  -webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M26 20h-2v4h-4v2h4v4h2v-4h4v-2h-4z'/%3E%3Cpath d='M28 8H16l-3.4-3.4c-.4-.4-.9-.6-1.4-.6H4c-1.1 0-2 .9-2 2v20c0 1.1.9 2 2 2h14v-2H4V6h7.2l3.4 3.4.6.6H28v8h2v-8c0-1.1-.9-2-2-2z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-folder-open {
  -webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='m11.17 6 3.42 3.41.58.59H28v16H4V6h7.17m0-2H4a2 2 0 0 0-2 2v20a2 2 0 0 0 2 2h24a2 2 0 0 0 2-2V10a2 2 0 0 0-2-2H16l-3.41-3.41A2 2 0 0 0 11.17 4Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-folder-closed {
  -webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='m11.17 6 3.42 3.41.58.59H28v16H4V6h7.17m0-2H4a2 2 0 0 0-2 2v20a2 2 0 0 0 2 2h24a2 2 0 0 0 2-2V10a2 2 0 0 0-2-2H16l-3.41-3.41A2 2 0 0 0 11.17 4Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-forward {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M13.414 17.586 18 22.172V8H8V6h10a2.002 2.002 0 0 1 2 2v14.172l4.586-4.586L26 19l-7 7-7-7Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
  transform: rotate(0.75turn);
}
body:not(.no-sanctum-icons) svg.lucide-maximize {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M20 2v2h6.586L18 12.582 19.414 14 28 5.414V12h2V2H20zm-6 17.416L12.592 18 4 26.586V20H2v10h10v-2H5.414L14 19.416z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;' transform='rotate(-180 16 16)'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-settings {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M27 16.76v-1.53l1.92-1.68A2 2 0 0 0 29.3 11l-2.36-4a2 2 0 0 0-1.73-1 2 2 0 0 0-.64.1l-2.43.82a11.35 11.35 0 0 0-1.31-.75l-.51-2.52a2 2 0 0 0-2-1.61h-4.68a2 2 0 0 0-2 1.61l-.51 2.52a11.48 11.48 0 0 0-1.32.75l-2.38-.86A2 2 0 0 0 6.79 6a2 2 0 0 0-1.73 1L2.7 11a2 2 0 0 0 .41 2.51L5 15.24v1.53l-1.89 1.68A2 2 0 0 0 2.7 21l2.36 4a2 2 0 0 0 1.73 1 2 2 0 0 0 .64-.1l2.43-.82a11.35 11.35 0 0 0 1.31.75l.51 2.52a2 2 0 0 0 2 1.61h4.72a2 2 0 0 0 2-1.61l.51-2.52a11.48 11.48 0 0 0 1.32-.75l2.42.82a2 2 0 0 0 .64.1 2 2 0 0 0 1.73-1l2.28-4a2 2 0 0 0-.41-2.51ZM25.21 24l-3.43-1.16a8.86 8.86 0 0 1-2.71 1.57L18.36 28h-4.72l-.71-3.55a9.36 9.36 0 0 1-2.7-1.57L6.79 24l-2.36-4 2.72-2.4a8.9 8.9 0 0 1 0-3.13L4.43 12l2.36-4 3.43 1.16a8.86 8.86 0 0 1 2.71-1.57L13.64 4h4.72l.71 3.55a9.36 9.36 0 0 1 2.7 1.57L25.21 8l2.36 4-2.72 2.4a8.9 8.9 0 0 1 0 3.13L27.57 20Z'/%3E%3Cpath d='M16 22a6 6 0 1 1 6-6 5.94 5.94 0 0 1-6 6Zm0-10a3.91 3.91 0 0 0-4 4 3.91 3.91 0 0 0 4 4 3.91 3.91 0 0 0 4-4 3.91 3.91 0 0 0-4-4Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-file-code {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='m18.83 26 2.58-2.58L20 22l-4 4 4 4 1.42-1.41L18.83 26zm8.34 0-2.58 2.58L26 30l4-4-4-4-1.42 1.41L27.17 26z'/%3E%3Cpath d='M14 28H8V4h8v6a2.006 2.006 0 0 0 2 2h6v6h2v-8a.91.91 0 0 0-.3-.7l-7-7A.909.909 0 0 0 18 2H8a2.006 2.006 0 0 0-2 2v24a2.006 2.006 0 0 0 2 2h6Zm4-23.6 5.6 5.6H18Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-file-input {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M13 21h13.17l-2.58 2.59L25 25l5-5-5-5-1.41 1.41L26.17 19H13v2z'/%3E%3Cpath d='M22 14v-4a1 1 0 0 0-.29-.71l-7-7A1 1 0 0 0 14 2H4a2 2 0 0 0-2 2v24a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2h-2v2H4V4h8v6a2 2 0 0 0 2 2h6v2Zm-8-4V4.41L19.59 10Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-git-fork {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M26 22a3.955 3.955 0 0 0-2.02.567l-3.813-3.814a4.965 4.965 0 0 0 0-5.506l2.547-2.547A3.028 3.028 0 1 0 21.3 9.286l-2.547 2.547a4.965 4.965 0 0 0-5.506 0L9.433 8.019A3.955 3.955 0 0 0 10 6a4 4 0 1 0-4 4 3.955 3.955 0 0 0 2.02-.567l3.813 3.814a4.965 4.965 0 0 0 0 5.506l-3.814 3.814A3.956 3.956 0 0 0 6 22a4 4 0 1 0 4 4 3.955 3.955 0 0 0-.567-2.02l3.814-3.813a4.969 4.969 0 0 0 1.753.732v3.285a3 3 0 1 0 2 0v-3.285a4.969 4.969 0 0 0 1.753-.732l3.814 3.814A3.955 3.955 0 0 0 22 26a4 4 0 1 0 4-4Zm-10-9a3 3 0 1 1-3 3 3.003 3.003 0 0 1 3-3ZM4 6a2 2 0 1 1 2 2 2.002 2.002 0 0 1-2-2Zm2 22a2 2 0 1 1 2-2 2.002 2.002 0 0 1-2 2Zm20 0a2 2 0 1 1 2-2 2.003 2.003 0 0 1-2 2Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-hash {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28 12v-2h-6V4h-2v6h-8V4h-2v6H4v2h6v8H4v2h6v6h2v-6h8v6h2v-6h6v-2h-6v-8Zm-8 8h-8v-8h8Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.heading-glyph {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M17 8V6h-4V2h-2v4H8V2H6v4H2v2h4v3H2v2h4v4h2v-4h3v4h2v-4h4v-2h-4V8Zm-6 3H8V8h3Zm19 10v-2h-4v-4h-2v4h-3v-4h-2v4h-4v2h4v3h-4v2h4v4h2v-4h3v4h2v-4h4v-2h-4v-3Zm-6 3h-3v-3h3Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.help {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M16 2a14 14 0 1 0 14 14A14 14 0 0 0 16 2Zm0 26a12 12 0 1 1 12-12 12 12 0 0 1-12 12Z'/%3E%3Ccircle cx='16' cy='23.5' r='1.5'/%3E%3Cpath d='M17 8h-1.5a4.49 4.49 0 0 0-4.5 4.5v.5h2v-.5a2.5 2.5 0 0 1 2.5-2.5H17a2.5 2.5 0 0 1 0 5h-2v4.5h2V17a4.5 4.5 0 0 0 0-9Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-highlighter {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M12 15H5a3 3 0 0 1-3-3v-2a3 3 0 0 1 3-3h5V5a1 1 0 0 0-1-1H3V2h6a3 3 0 0 1 3 3ZM5 9a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h5V9Zm15 14v2a1 1 0 0 0 1 1h5v-4h-5a1 1 0 0 0-1 1Z'/%3E%3Cpath d='M2 30h28V2Zm26-2h-7a3 3 0 0 1-3-3v-2a3 3 0 0 1 3-3h5v-2a1 1 0 0 0-1-1h-6v-2h6a3 3 0 0 1 3 3Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-separator-horizontal {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M4 20h11v6.17l-2.59-2.58L11 25l5 5 5-5-1.41-1.41L17 26.17V20h11v-2H4v2zm7-13 1.41 1.41L15 5.83V12H4v2h24v-2H17V5.83l2.59 2.58L21 7l-5-5-5 5z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-image {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M19 14a3 3 0 1 0-3-3 3 3 0 0 0 3 3Zm0-4a1 1 0 1 1-1 1 1 1 0 0 1 1-1Z'/%3E%3Cpath d='M26 4H6a2 2 0 0 0-2 2v20a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2Zm0 22H6v-6l5-5 5.59 5.59a2 2 0 0 0 2.82 0L21 19l5 5Zm0-4.83-3.59-3.59a2 2 0 0 0-2.82 0L18 19.17l-5.59-5.59a2 2 0 0 0-2.82 0L6 17.17V6h20Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-paperclip {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M19 14a3 3 0 1 0-3-3 3 3 0 0 0 3 3Zm0-4a1 1 0 1 1-1 1 1 1 0 0 1 1-1Z'/%3E%3Cpath d='M26 4H6a2 2 0 0 0-2 2v20a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2Zm0 22H6v-6l5-5 5.59 5.59a2 2 0 0 0 2.82 0L21 19l5 5Zm0-4.83-3.59-3.59a2 2 0 0 0-2.82 0L18 19.17l-5.59-5.59a2 2 0 0 0-2.82 0L6 17.17V6h20Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-download {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28 19H14.83l2.58-2.59L16 15l-5 5 5 5 1.41-1.41L14.83 21H28v-2z'/%3E%3Cpath d='M24 14v-4a1 1 0 0 0-.29-.71l-7-7A1 1 0 0 0 16 2H6a2 2 0 0 0-2 2v24a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2h-2v2H6V4h8v6a2 2 0 0 0 2 2h6v2Zm-8-4V4.41L21.59 10Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-indent {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M14 6h14v2H14zm0 6h14v2H14zm-7 6h21v2H7zm0 6h21v2H7zM4 13.59 7.29 10 4 6.41 5.42 5l4.62 5-4.62 5L4 13.59z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-info {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M17 22v-8h-4v2h2v6h-3v2h8v-2h-3zM16 8a1.5 1.5 0 1 0 1.5 1.5A1.5 1.5 0 0 0 16 8Z'/%3E%3Cpath d='M16 30a14 14 0 1 1 14-14 14 14 0 0 1-14 14Zm0-26a12 12 0 1 0 12 12A12 12 0 0 0 16 4Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-download-cloud {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M23.5 22H23v-2h.5a4.5 4.5 0 0 0 .36-9H23l-.1-.82a7 7 0 0 0-13.88 0L9 11h-.86a4.5 4.5 0 0 0 .36 9H9v2h-.5A6.5 6.5 0 0 1 7.2 9.14a9 9 0 0 1 17.6 0A6.5 6.5 0 0 1 23.5 22Z'/%3E%3Cpath d='M17 26.17V14h-2v12.17l-2.59-2.58L11 25l5 5 5-5-1.41-1.41L17 26.17z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-italic {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M25 9V7H12v2h5.14l-4.37 14H7v2h13v-2h-5.14l4.37-14H25z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-keyboard {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28 26H4a2 2 0 0 1-2-2V10a2 2 0 0 1 2-2h24a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2ZM4 10v14h24V10Z'/%3E%3Cpath d='M10 20h11v2H10zm-4-8h2v2H6zm4 0h2v2h-2zm4 0h2v2h-2zm4 0h2v2h-2zM6 20h2v2H6zm0-4h2v2H6zm4 0h2v2h-2zm4 0h2v2h-2zm8-4h4v2h-4zm0 4h4v2h-4zm-4 0h2v2h-2zm5 4h3v2h-3z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='Transparent Rectangle'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-languages {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M27.85 29H30l-6-15h-2.35l-6 15h2.15l1.6-4h6.85Zm-7.65-6 2.62-6.56L25.45 23ZM18 7V5h-7V2H9v3H2v2h10.74a14.71 14.71 0 0 1-3.19 6.18A13.5 13.5 0 0 1 7.26 9h-2.1a16.47 16.47 0 0 0 3 5.58A16.84 16.84 0 0 1 3 18l.75 1.86A18.47 18.47 0 0 0 9.53 16a16.92 16.92 0 0 0 5.76 3.84L16 18a14.48 14.48 0 0 1-5.12-3.37A17.64 17.64 0 0 0 14.8 7Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-arrow-left {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' xml:space='preserve'%3E%3Cpath d='M10 16 20 6l1.4 1.4-8.6 8.6 8.6 8.6L20 26z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.left-arrow {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' xml:space='preserve'%3E%3Cpath d='M10 16 20 6l1.4 1.4-8.6 8.6 8.6 8.6L20 26z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-chevron-left {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' xml:space='preserve'%3E%3Cpath d='M10 16 20 6l1.4 1.4-8.6 8.6 8.6 8.6L20 26z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-book-open {
  transform: translateY(-1px);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M30.94 15.66A16.69 16.69 0 0 0 16 5 16.69 16.69 0 0 0 1.06 15.66a1 1 0 0 0 0 .68A16.69 16.69 0 0 0 16 27a16.69 16.69 0 0 0 14.94-10.66 1 1 0 0 0 0-.68ZM16 25c-5.3 0-10.9-3.93-12.93-9C5.1 10.93 10.7 7 16 7s10.9 3.93 12.93 9C26.9 21.07 21.3 25 16 25Z'/%3E%3Cpath d='M16 10a6 6 0 1 0 6 6 6 6 0 0 0-6-6Zm0 10a4 4 0 1 1 4-4 4 4 0 0 1-4 4Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-align-left {
  transform: translateY(-1px);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M30.94 15.66A16.69 16.69 0 0 0 16 5 16.69 16.69 0 0 0 1.06 15.66a1 1 0 0 0 0 .68A16.69 16.69 0 0 0 16 27a16.69 16.69 0 0 0 14.94-10.66 1 1 0 0 0 0-.68ZM16 25c-5.3 0-10.9-3.93-12.93-9C5.1 10.93 10.7 7 16 7s10.9 3.93 12.93 9C26.9 21.07 21.3 25 16 25Z'/%3E%3Cpath d='M16 10a6 6 0 1 0 6 6 6 6 0 0 0-6-6Zm0 10a4 4 0 1 1 4-4 4 4 0 0 1-4 4Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
.view-action > svg.lucide-align-left {
  margin-right: 1px;
}
body:not(.no-sanctum-icons) svg.link-glyph {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M29.25 6.76a6 6 0 0 0-8.5 0l1.42 1.42a4 4 0 1 1 5.67 5.67l-8 8a4 4 0 1 1-5.67-5.66l1.41-1.42-1.41-1.42-1.42 1.42a6 6 0 0 0 0 8.5A6 6 0 0 0 17 25a6 6 0 0 0 4.27-1.76l8-8a6 6 0 0 0-.02-8.48Z'/%3E%3Cpath d='M4.19 24.82a4 4 0 0 1 0-5.67l8-8a4 4 0 0 1 5.67 0A3.94 3.94 0 0 1 19 14a4 4 0 0 1-1.17 2.85L15.71 19l1.42 1.42 2.12-2.12a6 6 0 0 0-8.51-8.51l-8 8a6 6 0 0 0 0 8.51A6 6 0 0 0 7 28a6.07 6.07 0 0 0 4.28-1.76l-1.42-1.42a4 4 0 0 1-5.67 0Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.link {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M29.25 6.76a6 6 0 0 0-8.5 0l1.42 1.42a4 4 0 1 1 5.67 5.67l-8 8a4 4 0 1 1-5.67-5.66l1.41-1.42-1.41-1.42-1.42 1.42a6 6 0 0 0 0 8.5A6 6 0 0 0 17 25a6 6 0 0 0 4.27-1.76l8-8a6 6 0 0 0-.02-8.48Z'/%3E%3Cpath d='M4.19 24.82a4 4 0 0 1 0-5.67l8-8a4 4 0 0 1 5.67 0A3.94 3.94 0 0 1 19 14a4 4 0 0 1-1.17 2.85L15.71 19l1.42 1.42 2.12-2.12a6 6 0 0 0-8.51-8.51l-8 8a6 6 0 0 0 0 8.51A6 6 0 0 0 7 28a6.07 6.07 0 0 0 4.28-1.76l-1.42-1.42a4 4 0 0 1-5.67 0Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-link {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M29.25 6.76a6 6 0 0 0-8.5 0l1.42 1.42a4 4 0 1 1 5.67 5.67l-8 8a4 4 0 1 1-5.67-5.66l1.41-1.42-1.41-1.42-1.42 1.42a6 6 0 0 0 0 8.5A6 6 0 0 0 17 25a6 6 0 0 0 4.27-1.76l8-8a6 6 0 0 0-.02-8.48Z'/%3E%3Cpath d='M4.19 24.82a4 4 0 0 1 0-5.67l8-8a4 4 0 0 1 5.67 0A3.94 3.94 0 0 1 19 14a4 4 0 0 1-1.17 2.85L15.71 19l1.42 1.42 2.12-2.12a6 6 0 0 0-8.51-8.51l-8 8a6 6 0 0 0 0 8.51A6 6 0 0 0 7 28a6.07 6.07 0 0 0 4.28-1.76l-1.42-1.42a4 4 0 0 1-5.67 0Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.links-coming-in {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M26 30H14a2 2 0 0 1-2-2v-3h2v3h12V4H14v3h-2V4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v24a2 2 0 0 1-2 2Z'/%3E%3Cpath d='M14.59 20.59 18.17 17H4v-2h14.17l-3.58-3.59L16 10l6 6-6 6-1.41-1.41z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.links-going-out {
  transform: translateX(2px);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M6 30h12a2.002 2.002 0 0 0 2-2v-3h-2v3H6V4h12v3h2V4a2.002 2.002 0 0 0-2-2H6a2.002 2.002 0 0 0-2 2v24a2.002 2.002 0 0 0 2 2Z'/%3E%3Cpath d='M20.586 20.586 24.172 17H10v-2h14.172l-3.586-3.586L22 10l6 6-6 6-1.414-1.414z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-search {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='m29 27.586-7.552-7.552a11.018 11.018 0 1 0-1.414 1.414L27.586 29ZM4 13a9 9 0 1 1 9 9 9.01 9.01 0 0 1-9-9Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-file-search {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='m29 27.586-7.552-7.552a11.018 11.018 0 1 0-1.414 1.414L27.586 29ZM4 13a9 9 0 1 1 9 9 9.01 9.01 0 0 1-9-9Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-git-merge {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28 6H18V4a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v20a2 2 0 0 0 2 2h10v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2ZM4 15h6.17l-2.58 2.59L9 19l5-5-5-5-1.41 1.41L10.17 13H4V4h12v20H4Zm12 13v-2a2 2 0 0 0 2-2V8h10v9h-6.17l2.58-2.59L23 13l-5 5 5 5 1.41-1.41L21.83 19H28v9Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.merge-files {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28 6H18V4a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v20a2 2 0 0 0 2 2h10v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2ZM4 15h6.17l-2.58 2.59L9 19l5-5-5-5-1.41 1.41L10.17 13H4V4h12v20H4Zm12 13v-2a2 2 0 0 0 2-2V8h10v9h-6.17l2.58-2.59L23 13l-5 5 5 5 1.41-1.41L21.83 19H28v9Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-mic {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M23 14v3a7 7 0 0 1-14 0v-3H7v3a9 9 0 0 0 8 8.94V28h-4v2h10v-2h-4v-2.06A9 9 0 0 0 25 17v-3Z'/%3E%3Cpath d='M16 22a5 5 0 0 0 5-5V7a5 5 0 0 0-10 0v10a5 5 0 0 0 5 5ZM13 7a3 3 0 0 1 6 0v10a3 3 0 0 1-6 0Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.microphone {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M23 14v3a7 7 0 0 1-14 0v-3H7v3a9 9 0 0 0 8 8.94V28h-4v2h10v-2h-4v-2.06A9 9 0 0 0 25 17v-3Z'/%3E%3Cpath d='M16 22a5 5 0 0 0 5-5V7a5 5 0 0 0-10 0v10a5 5 0 0 0 5 5ZM13 7a3 3 0 0 1 6 0v10a3 3 0 0 1-6 0Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-minus-circle {
  -webkit-mask-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='utf-8'%3F%3E%3Csvg version='1.1' id='icon' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 32 32' style='enable-background:new 0 0 32 32;' xml:space='preserve'%3E%3Cstyle type='text/css'%3E .st0%7Bfill:none;%7D%0A%3C/style%3E%3Cpath d='M16,4c6.6,0,12,5.4,12,12s-5.4,12-12,12S4,22.6,4,16S9.4,4,16,4 M16,2C8.3,2,2,8.3,2,16s6.3,14,14,14s14-6.3,14-14 S23.7,2,16,2z'/%3E%3Crect x='8' y='15' width='16' height='2'/%3E%3Crect id='_Transparent_Rectangle_' class='st0' width='32' height='32'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-navigation {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M22.707 9.293a1 1 0 0 0-1.023-.242l-9 3a1.001 1.001 0 0 0-.633.633l-3 9a1 1 0 0 0 1.265 1.265l9-3a1.001 1.001 0 0 0 .633-.633l3-9a1 1 0 0 0-.242-1.023ZM11.581 20.42l2.21-6.628 4.419 4.419Z'/%3E%3Cpath d='M16 30a14 14 0 1 1 14-14 14.016 14.016 0 0 1-14 14Zm0-26a12 12 0 1 0 12 12A12.014 12.014 0 0 0 16 4Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-sticky-note {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M8 10h10v2H8zm0 5h6v2H8z'/%3E%3Cpath d='M29 29H3V3h26ZM5 27h22V5H5Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-list-ordered {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M16 22h14v2H16zm0-14h14v2H16zm-8 4V4H6v1H4v2h2v5H4v2h6v-2H8zm2 16H4v-4a2 2 0 0 1 2-2h2v-2H4v-2h4a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2H6v2h4Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-arrow-up-right {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M26 28H6a2.003 2.003 0 0 1-2-2V6a2.003 2.003 0 0 1 2-2h10v2H6v20h20V16h2v10a2.003 2.003 0 0 1-2 2Z'/%3E%3Cpath d='M20 2v2h6.586L18 12.586 19.414 14 28 5.414V12h2V2H20z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.open-vault {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M27 24a2.96 2.96 0 0 0-1.285.3l-4.3-4.3H18v2h2.586l3.715 3.715A2.966 2.966 0 0 0 24 27a3 3 0 1 0 3-3Zm0 4a1 1 0 1 1 1-1 1 1 0 0 1-1 1ZM27 13a2.995 2.995 0 0 0-2.816 2H18v2h6.184A2.995 2.995 0 1 0 27 13Zm0 4a1 1 0 1 1 1-1 1 1 0 0 1-1 1ZM27 2a3.003 3.003 0 0 0-3 3 2.966 2.966 0 0 0 .348 1.373L20.596 10H18v2h3.404l4.4-4.252A2.999 2.999 0 1 0 27 2Zm0 4a1 1 0 1 1 1-1 1 1 0 0 1-1 1Z'/%3E%3Cpath d='M18 6h2V4h-2a3.976 3.976 0 0 0-3 1.382A3.976 3.976 0 0 0 12 4h-1a9.01 9.01 0 0 0-9 9v6a9.01 9.01 0 0 0 9 9h1a3.976 3.976 0 0 0 3-1.382A3.976 3.976 0 0 0 18 28h2v-2h-2a2.002 2.002 0 0 1-2-2V8a2.002 2.002 0 0 1 2-2Zm-6 20h-1a7.005 7.005 0 0 1-6.92-6H6v-2H4v-4h3a3.003 3.003 0 0 0 3-3V9H8v2a1 1 0 0 1-1 1H4.08A7.005 7.005 0 0 1 11 6h1a2.002 2.002 0 0 1 2 2v4h-2v2h2v4h-2a3.003 3.003 0 0 0-3 3v2h2v-2a1 1 0 0 1 1-1h2v4a2.002 2.002 0 0 1-2 2Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-layout {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M30 24v-2h-2.101a4.968 4.968 0 0 0-.732-1.753l1.49-1.49-1.414-1.414-1.49 1.49A4.968 4.968 0 0 0 24 18.101V16h-2v2.101a4.968 4.968 0 0 0-1.753.732l-1.49-1.49-1.414 1.414 1.49 1.49A4.968 4.968 0 0 0 18.101 22H16v2h2.101a4.968 4.968 0 0 0 .732 1.753l-1.49 1.49 1.414 1.414 1.49-1.49a4.968 4.968 0 0 0 1.753.732V30h2v-2.101a4.968 4.968 0 0 0 1.753-.732l1.49 1.49 1.414-1.414-1.49-1.49A4.968 4.968 0 0 0 27.899 24Zm-7 2a3 3 0 1 1 3-3 3.003 3.003 0 0 1-3 3Z'/%3E%3Cpath d='M28 4H4a2.002 2.002 0 0 0-2 2v20a2.002 2.002 0 0 0 2 2h10v-2H4V12h24v3h2V6a2.002 2.002 0 0 0-2-2Zm0 6H4V6h24Z'/%3E%3Ccircle cx='20' cy='8' r='1'/%3E%3Ccircle cx='23' cy='8' r='1'/%3E%3Ccircle cx='26' cy='8' r='1'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-send {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M27.71 4.29a1 1 0 0 0-1.05-.23l-22 8a1 1 0 0 0 0 1.87l9.6 3.84 3.84 9.6a1 1 0 0 0 .9.63 1 1 0 0 0 .92-.66l8-22a1 1 0 0 0-.21-1.05ZM19 24.2l-2.79-7L21 12.41 19.59 11l-4.83 4.83L7.8 13l17.53-6.33Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.paper-plane {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M27.71 4.29a1 1 0 0 0-1.05-.23l-22 8a1 1 0 0 0 0 1.87l9.6 3.84 3.84 9.6a1 1 0 0 0 .9.63 1 1 0 0 0 .92-.66l8-22a1 1 0 0 0-.21-1.05ZM19 24.2l-2.79-7L21 12.41 19.59 11l-4.83 4.83L7.8 13l17.53-6.33Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
  vertical-align: -1px;
}
body:not(.no-sanctum-icons) svg.lucide-clipboard-type {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M31 24h-4v-4h-2v4h-4v2h4v4h2v-4h4v-2z'/%3E%3Cpath d='M25 5h-3V4a2.006 2.006 0 0 0-2-2h-8a2.006 2.006 0 0 0-2 2v1H7a2.006 2.006 0 0 0-2 2v21a2.006 2.006 0 0 0 2 2h10v-2H7V7h3v3h12V7h3v9h2V7a2.006 2.006 0 0 0-2-2Zm-5 3h-8V4h8Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-clipboard-check {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='m22 27.18-2.59-2.59L18 26l4 4 8-8-1.41-1.41L22 27.18z'/%3E%3Cpath d='M25 5h-3V4a2.006 2.006 0 0 0-2-2h-8a2.006 2.006 0 0 0-2 2v1H7a2.006 2.006 0 0 0-2 2v21a2.006 2.006 0 0 0 2 2h9v-2H7V7h3v3h12V7h3v11h2V7a2.006 2.006 0 0 0-2-2Zm-5 3h-8V4h8Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.paused {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M12 6h-2a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h2a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2Zm10 0h-2a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h2a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-file-down,
body:not(.no-sanctum-icons) svg.lucide-file-text {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M30 18v-2h-6v10h2v-4h3v-2h-3v-2h4zm-11 8h-4V16h4a3.003 3.003 0 0 1 3 3v4a3.003 3.003 0 0 1-3 3Zm-2-2h2a1.001 1.001 0 0 0 1-1v-4a1.001 1.001 0 0 0-1-1h-2Zm-6-8H6v10h2v-3h3a2.003 2.003 0 0 0 2-2v-3a2.002 2.002 0 0 0-2-2Zm-3 5v-3h3l.001 3Z'/%3E%3Cpath d='M22 14v-4a.91.91 0 0 0-.3-.7l-7-7A.909.909 0 0 0 14 2H4a2.006 2.006 0 0 0-2 2v24a2 2 0 0 0 2 2h16v-2H4V4h8v6a2.006 2.006 0 0 0 2 2h6v2Zm-8-4V4.4l5.6 5.6Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-edit-3 {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28.828 3.172a4.094 4.094 0 0 0-5.656 0L4.05 22.292A6.954 6.954 0 0 0 2 27.242V30h2.756a6.952 6.952 0 0 0 4.95-2.05L28.828 8.829a3.999 3.999 0 0 0 0-5.657ZM10.91 18.26l2.829 2.829-2.122 2.121-2.828-2.828Zm-2.619 8.276A4.966 4.966 0 0 1 4.756 28H4v-.759a4.967 4.967 0 0 1 1.464-3.535l1.91-1.91 2.829 2.828ZM27.415 7.414l-12.261 12.26-2.829-2.828 12.262-12.26a2.047 2.047 0 0 1 2.828 0 2 2 0 0 1 0 2.828Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
  transform: scale(1);
}
.view-action > svg.lucide-edit-3 {
  margin-right: 1px;
}
body:not(.no-sanctum-icons) svg.lucide-percent {
  transform: scale(0.9);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M9 14a5 5 0 1 1 5-5 5.005 5.005 0 0 1-5 5Zm0-8a3 3 0 1 0 3 3 3.003 3.003 0 0 0-3-3ZM4 26.586 26.585 4 28 5.415 5.414 28zM23 28a5 5 0 1 1 5-5 5.005 5.005 0 0 1-5 5Zm0-8a3 3 0 1 0 3 3 3.003 3.003 0 0 0-3-3Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.pin {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28.59 13.31 30 11.9 20 2l-1.31 1.42 1.18 1.18-11.49 9.72-1.72-1.71L5.25 14l5.66 5.68L2 28.58 3.41 30l8.91-8.91L18 26.75l1.39-1.42-1.71-1.71 9.72-11.49ZM16.26 22.2 9.8 15.74 21.29 6 26 10.71Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-play-circle {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M7 28a1 1 0 0 1-1-1V5a1 1 0 0 1 1.482-.876l20 11a1 1 0 0 1 0 1.752l-20 11A1 1 0 0 1 7 28Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-diff {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M14 8h-4V4H8v4H4v2h4v4h2v-4h4V8zM4 19h10v2H4zm0 5h10v2H4zM18 8h10v2H18zm6.41 14L28 18.41 26.59 17 23 20.59 19.41 17 18 18.41 21.59 22 18 25.59 19.41 27 23 23.41 26.59 27 28 25.59 24.41 22z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.plus-with-circle {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' xml:space='preserve'%3E%3Cpath d='M16 4c6.6 0 12 5.4 12 12s-5.4 12-12 12S4 22.6 4 16 9.4 4 16 4m0-2C8.3 2 2 8.3 2 16s6.3 14 14 14 14-6.3 14-14S23.7 2 16 2z'/%3E%3Cpath d='M24 15h-7V8h-2v7H8v2h7v7h2v-7h7z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.popup-open {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28 4H10a2.006 2.006 0 0 0-2 2v14a2.006 2.006 0 0 0 2 2h18a2.006 2.006 0 0 0 2-2V6a2.006 2.006 0 0 0-2-2Zm0 16H10V6h18Z'/%3E%3Cpath d='M18 26H4V16h2v-2H4a2.006 2.006 0 0 0-2 2v10a2.006 2.006 0 0 0 2 2h14a2.006 2.006 0 0 0 2-2v-2h-2Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-monitor {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M15 10h2v8h-2zm5 4h2v4h-2zm-10-2h2v6h-2z'/%3E%3Cpath d='M25 4h-8V2h-2v2H7a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h8v6h-4v2h10v-2h-4v-6h8a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2Zm0 16H7V6h18Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
  transform: scale(1.1);
}
body:not(.no-sanctum-icons) svg.presentation {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M15 10h2v8h-2zm5 4h2v4h-2zm-10-2h2v6h-2z'/%3E%3Cpath d='M25 4h-8V2h-2v2H7a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h8v6h-4v2h10v-2h-4v-6h8a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2Zm0 16H7V6h18Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
  transform: scale(1.1);
}
body:not(.no-sanctum-icons) svg.lucide-tag {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28 12v-2h-6V4h-2v6h-8V4h-2v6H4v2h6v8H4v2h6v6h2v-6h8v6h2v-6h6v-2h-6v-8Zm-8 8h-8v-8h8Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.question-mark-glyph {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M16 2a14 14 0 1 0 14 14A14 14 0 0 0 16 2Zm0 26a12 12 0 1 1 12-12 12 12 0 0 1-12 12Z'/%3E%3Ccircle cx='16' cy='23.5' r='1.5'/%3E%3Cpath d='M17 8h-1.5a4.49 4.49 0 0 0-4.5 4.5v.5h2v-.5a2.5 2.5 0 0 1 2.5-2.5H17a2.5 2.5 0 0 1 0 5h-2v4.5h2V17a4.5 4.5 0 0 0 0-9Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-quote {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M12 15H6.11A9 9 0 0 1 10 8.86l1.79-1.2L10.69 6 8.9 7.2A11 11 0 0 0 4 16.35V23a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2v-6a2 2 0 0 0-2-2Zm14 0h-5.89A9 9 0 0 1 24 8.86l1.79-1.2L24.7 6l-1.8 1.2a11 11 0 0 0-4.9 9.15V23a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2v-6a2 2 0 0 0-2-2Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-redo-2 {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M12 10h12.185l-3.587-3.586L22 5l6 6-6 6-1.402-1.415L24.182 12H12a6 6 0 0 0 0 12h8v2h-8a8 8 0 0 1 0-16Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-rotate-ccw {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M18 28A12 12 0 1 0 6 16v6.2l-3.6-3.6L1 20l6 6 6-6-1.4-1.4L8 22.2V16a10 10 0 1 1 10 10Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.restore-file-glyph {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M20.59 22 15 16.41V7h2v8.58l5 5.01L20.59 22z'/%3E%3Cpath d='M16 2A13.94 13.94 0 0 0 6 6.23V2H4v8h8V8H7.08A12 12 0 1 1 4 16H2A14 14 0 1 0 16 2Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-arrow-right {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' xml:space='preserve'%3E%3Cpath d='M22 16 12 26l-1.4-1.4 8.6-8.6-8.6-8.6L12 6z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.right-arrow {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' xml:space='preserve'%3E%3Cpath d='M22 16 12 26l-1.4-1.4 8.6-8.6-8.6-8.6L12 6z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-chevron-right {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' xml:space='preserve'%3E%3Cpath d='M22 16 12 26l-1.4-1.4 8.6-8.6-8.6-8.6L12 6z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
}
svg.right-triangle {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' xml:space='preserve'%3E%3Cpath d='M16 22 6 12l1.4-1.4 8.6 8.6 8.6-8.6L26 12z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
  -webkit-mask-repeat: no-repeat;
}
body:not(.no-sanctum-icons) svg.lucide-terminal {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M26 4.01H6a2 2 0 0 0-2 2v20a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2v-20a2 2 0 0 0-2-2Zm0 2v4H6v-4Zm-20 20v-14h20v14Z'/%3E%3Cpath d='m10.76 16.18 2.82 2.83-2.82 2.83 1.41 1.41 4.24-4.24-4.24-4.24-1.41 1.41z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-scissors {
  transform: rotate(0.5turn);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M26.5 19.63 20.24 16l6.26-3.63a5 5 0 0 0-1.21-9.2A5.19 5.19 0 0 0 24 3a5 5 0 0 0-4.33 7.53 5 5 0 0 0 2.39 2.1l-3.82 2.21L4 6.6 3 8.34 16.24 16 3 23.68l1 1.74 14.24-8.26 3.82 2.21a5 5 0 0 0-2.39 2.1A5 5 0 0 0 24 29a5.19 5.19 0 0 0 1.29-.17 5 5 0 0 0 1.21-9.2Zm-5.1-10.1a3 3 0 0 1 1.1-4.12 3 3 0 0 1 4.1 1.11 3 3 0 0 1-1.1 4.11 3 3 0 0 1-4.1-1.1Zm5.2 16a3 3 0 0 1-4.1 1.11 3 3 0 0 1-1.1-4.12 3 3 0 0 1 4.1-1.1 3 3 0 0 1 1.1 4.06Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.scissors {
  transform: rotate(0.5turn);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M26.5 19.63 20.24 16l6.26-3.63a5 5 0 0 0-1.21-9.2A5.19 5.19 0 0 0 24 3a5 5 0 0 0-4.33 7.53 5 5 0 0 0 2.39 2.1l-3.82 2.21L4 6.6 3 8.34 16.24 16 3 23.68l1 1.74 14.24-8.26 3.82 2.21a5 5 0 0 0-2.39 2.1A5 5 0 0 0 24 29a5.19 5.19 0 0 0 1.29-.17 5 5 0 0 0 1.21-9.2Zm-5.1-10.1a3 3 0 0 1 1.1-4.12 3 3 0 0 1 4.1 1.11 3 3 0 0 1-1.1 4.11 3 3 0 0 1-4.1-1.1Zm5.2 16a3 3 0 0 1-4.1 1.11 3 3 0 0 1-1.1-4.12 3 3 0 0 1 4.1-1.1 3 3 0 0 1 1.1 4.06Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.search-glyph {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='m29 27.586-7.552-7.552a11.018 11.018 0 1 0-1.414 1.414L27.586 29ZM4 13a9 9 0 1 1 9 9 9.01 9.01 0 0 1-9-9Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.search {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='m29 27.586-7.552-7.552a11.018 11.018 0 1 0-1.414 1.414L27.586 29ZM4 13a9 9 0 1 1 9 9 9.01 9.01 0 0 1-9-9Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-box-select {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M12 6H8V2H6v4H2v2h4v4h2V8h4V6zm4 0h4v2h-4zm8 0v2h4v4h2V8a2 2 0 0 0-2-2ZM6 16h2v4H6zm2 12v-4H6v4a2 2 0 0 0 2 2h4v-2Zm20-12h2v4h-2zM16 28h4v2h-4zm12-4v4h-4v2h4a2 2 0 0 0 2-2v-4Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.sheets-in-box {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28 28H4a2.002 2.002 0 0 1-2-2v-5h2v5h24v-5h2v5a2.002 2.002 0 0 1-2 2Z'/%3E%3Cpath d='M7 21h18v2H7zm0-5h18v2H7zm0-5h18v2H7zm0-5h18v2H7z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-git-branch-plus {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M26 28H6a2.003 2.003 0 0 1-2-2V6a2.003 2.003 0 0 1 2-2h10v2H6v20h20V16h2v10a2.003 2.003 0 0 1-2 2Z'/%3E%3Cpath d='M20 2v2h6.586L18 12.586 19.414 14 28 5.414V12h2V2H20z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.spreadsheet {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28 2H10a2.002 2.002 0 0 0-2 2v3H4a2.002 2.002 0 0 0-2 2v19a2.002 2.002 0 0 0 2 2h24a2.002 2.002 0 0 0 2-2V4a2.002 2.002 0 0 0-2-2ZM10 4h18v3H10Zm18 10h-8V9h8Zm-18 7v-5h8v5Zm8 2v5h-8v-5ZM8 21H4v-5h4ZM18 9v5h-8V9Zm2 7h8v5h-8ZM8 9v5H4V9ZM4 23h4v5H4Zm16 5v-5h8v5Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-folder-tree {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M12 10h14a2.002 2.002 0 0 0 2-2V4a2.002 2.002 0 0 0-2-2H12a2.002 2.002 0 0 0-2 2v1H6V2H4v23a2.002 2.002 0 0 0 2 2h4v1a2.002 2.002 0 0 0 2 2h14a2.002 2.002 0 0 0 2-2v-4a2.002 2.002 0 0 0-2-2H12a2.002 2.002 0 0 0-2 2v1H6v-8h4v1a2.002 2.002 0 0 0 2 2h14a2.002 2.002 0 0 0 2-2v-4a2.002 2.002 0 0 0-2-2H12a2.002 2.002 0 0 0-2 2v1H6V7h4v1a2.002 2.002 0 0 0 2 2Zm0-6h14l.001 4H12Zm0 20h14l.001 4H12Zm0-10h14l.001 4H12Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-star {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='m16 6.52 2.76 5.58.46 1 1 .15 6.16.89-4.38 4.3-.75.73.18 1 1.05 6.13-5.51-2.89L16 23l-.93.49-5.51 2.85 1-6.13.18-1-.74-.77-4.42-4.35 6.16-.89 1-.15.46-1L16 6.52M16 2l-4.55 9.22-10.17 1.47 7.36 7.18L6.9 30l9.1-4.78L25.1 30l-1.74-10.13 7.36-7.17-10.17-1.48Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.star-list {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M18 26h8v2h-8zm0-4h12v2H18zm0-4h12v2H18z'/%3E%3Cpath d='M20.549 11.217 16 2l-4.549 9.217L1.28 12.695l7.36 7.175L6.902 30 14 26.269v-2.26l-4.441 2.335 1.052-6.136.178-1.037-.753-.733-4.458-4.347 6.161-.895 1.04-.151.466-.943L16 6.519l2.755 5.583.466.943 1.04.151 7.454 1.085L28 12.3l-7.451-1.083z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.star {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='m16 6.52 2.76 5.58.46 1 1 .15 6.16.89-4.38 4.3-.75.73.18 1 1.05 6.13-5.51-2.89L16 23l-.93.49-5.51 2.85 1-6.13.18-1-.74-.77-4.42-4.35 6.16-.89 1-.15.46-1L16 6.52M16 2l-4.55 9.22-10.17 1.47 7.36 7.18L6.9 30l9.1-4.78L25.1 30l-1.74-10.13 7.36-7.17-10.17-1.48Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-stop-circle {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M24 6H8a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-strikethrough {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28 15H17.956a39.769 39.769 0 0 0-1.338-.335c-2.808-.664-4.396-1.15-4.396-3.423a2.868 2.868 0 0 1 .787-2.145 4.789 4.789 0 0 1 3.013-1.09c2.83-.07 4.135.89 5.202 2.35l1.615-1.18a7.473 7.473 0 0 0-6.83-3.17 6.773 6.773 0 0 0-4.4 1.661 4.827 4.827 0 0 0-1.386 3.574A4.372 4.372 0 0 0 11.957 15H4v2h13.652c1.967.57 3.143 1.312 3.173 3.358a3.119 3.119 0 0 1-.862 2.393A5.824 5.824 0 0 1 16.243 24a6.634 6.634 0 0 1-5.145-2.691l-1.533 1.284A8.526 8.526 0 0 0 16.212 26h.1a7.67 7.67 0 0 0 5.048-1.819 5.078 5.078 0 0 0 1.465-3.853A4.952 4.952 0 0 0 21.675 17H28Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-repeat {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M11.41 26.59 7.83 23H28v-2H7.83l3.58-3.59L10 16l-6 6 6 6 1.41-1.41zM28 10l-6-6-1.41 1.41L24.17 9H4v2h20.17l-3.58 3.59L22 16l6-6z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;' transform='rotate(90 16 16)'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.sync-small {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M12 10H6.78A11 11 0 0 1 27 16h2A13 13 0 0 0 6 7.68V4H4v8h8Zm8 12h5.22A11 11 0 0 1 5 16H3a13 13 0 0 0 23 8.32V28h2v-8h-8Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-refresh-cw {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M12 10H6.78A11 11 0 0 1 27 16h2A13 13 0 0 0 6 7.68V4H4v8h8Zm8 12h5.22A11 11 0 0 1 5 16H3a13 13 0 0 0 23 8.32V28h2v-8h-8Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.tag-glyph {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28 12v-2h-6V4h-2v6h-8V4h-2v6H4v2h6v8H4v2h6v6h2v-6h8v6h2v-6h6v-2h-6v-8Zm-8 8h-8v-8h8Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-menu {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M4 6h24v2H4zm0 18h24v2H4zm0-12h24v2H4zm0 6h24v2H4z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-calendar-plus {
  -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 100 100"><path fill="red" d="M33.333 62.5L45.833 62.5 45.833 75 54.167 75 54.167 62.5 66.667 62.5 66.667 54.167 54.167 54.167 54.167 41.667 45.833 41.667 45.833 54.167 33.333 54.167z M79.167 16.667h-8.333V8.333h-8.333v8.333H37.5V8.333H29.167v8.333H20.833C16.238 16.667 12.5 20.404 12.5 25v8.333v50c0 4.596 3.738 8.333 8.333 8.333h58.333c4.596 0 8.333 -3.738 8.333 -8.333V33.333V25C87.5 20.404 83.763 16.667 79.167 16.667zM79.175 83.333H20.833V33.333h58.333L79.175 83.333z"></path></svg>');
}
body:not(.no-sanctum-icons) svg.lucide-calendar {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M26 4h-4V2h-2v2h-8V2h-2v2H6c-1.1 0-2 .9-2 2v20c0 1.1.9 2 2 2h20c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 22H6V12h20v14zm0-16H6V6h4v2h2V6h8v2h2V6h4v4z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-trash-2 {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M12 12h2v12h-2zm6 0h2v12h-2z'/%3E%3Cpath d='M4 6v2h2v20a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8h2V6Zm4 22V8h16v20Zm4-26h8v2h-8z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-copy {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28 10v18H10V10h18m0-2H10a2 2 0 0 0-2 2v18a2 2 0 0 0 2 2h18a2 2 0 0 0 2-2V10a2 2 0 0 0-2-2Z'/%3E%3Cpath d='M4 18H2V4a2 2 0 0 1 2-2h14v2H4Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-undo-2 {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M20 10H7.815l3.587-3.586L10 5l-6 6 6 6 1.402-1.415L7.818 12H20a6 6 0 0 1 0 12h-8v2h8a8 8 0 0 0 0-16Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-outdent {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M2 6h10v2H2zm3 6h7v2H5zm-3 6h10v2H2zm3 6h7v2H5zM16 4h2v24h-2zm12.15 19.5 1.41-1.38L23.27 16l6.29-6.12-1.41-1.38-7.71 7.5 7.71 7.5z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-move-vertical {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M27.6 20.6 24 24.2V4h-2v20.2l-3.6-3.6L17 22l6 6 6-6zM9 4l-6 6 1.4 1.4L8 7.8V28h2V7.8l3.6 3.6L15 10z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-arrow-up {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M16 4 6 14l1.41 1.41L15 7.83V28h2V7.83l7.59 7.58L26 14 16 4z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-chevron-up {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='m16 10 10 10-1.4 1.4-8.6-8.6-8.6 8.6L6 20z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-corner-right-up {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M13.414 17.586 18 22.172V8H8V6h10a2.002 2.002 0 0 1 2 2v14.172l4.586-4.586L26 19l-7 7-7-7Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
  transform: scaleY(-1);
}
body:not(.no-sanctum-icons) svg.uppercase-lowercase-a {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M23 13h-5v2h5v2h-4a2 2 0 0 0-2 2v2a2 2 0 0 0 2 2h6v-8a2 2 0 0 0-2-2Zm0 8h-4v-2h4ZM13 9H9a2 2 0 0 0-2 2v12h2v-5h4v5h2V11a2 2 0 0 0-2-2Zm-4 7v-5h4v5Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
  transform: scale(1.2);
}
body:not(.no-sanctum-icons) svg.vault {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M30 13A11 11 0 0 0 19 2h-8a9 9 0 0 0-9 9v3a5 5 0 0 0 5 5h1.1a5 5 0 0 0 4.9 4h1.38l4 7 1.73-1-4-6.89A2 2 0 0 0 14.38 21H13a3 3 0 0 1 0-6h1v-2h-1a5 5 0 0 0-4.9 4H7a3 3 0 0 1-3-3v-2h2a3 3 0 0 0 3-3V8H7v1a1 1 0 0 1-1 1H4.08A7 7 0 0 1 11 4h6v2a1 1 0 0 1-1 1h-2v2h2a3 3 0 0 0 3-3V4a9 9 0 0 1 8.05 5H26a3 3 0 0 0-3 3v1h2v-1a1 1 0 0 1 1-1h1.77a8.76 8.76 0 0 1 .23 2v1a5 5 0 0 1-5 5h-3v2h3a7 7 0 0 0 3-.68V21a3 3 0 0 1-3 3h-1v2h1a5 5 0 0 0 5-5v-2.11A7 7 0 0 0 30 14Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-separator-vertical {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M12 4v11H5.83l2.58-2.59L7 11l-5 5 5 5 1.41-1.41L5.83 17H12v11h2V4h-2zm13 7-1.41 1.41L26.17 15H20V4h-2v24h2V17h6.17l-2.58 2.59L25 21l5-5-5-5z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-more-vertical {
  transform: translateY(0px) rotate(90deg);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Ccircle cx='8' cy='16' r='2'/%3E%3Ccircle cx='16' cy='16' r='2'/%3E%3Ccircle cx='24' cy='16' r='2'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-wand {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M29.414 24 12 6.586a2.048 2.048 0 0 0-2.828 0L6.586 9.172a2.002 2.002 0 0 0 0 2.828l17.413 17.414a2.002 2.002 0 0 0 2.828 0l2.587-2.586a2 2 0 0 0 0-2.828ZM8 10.586 10.586 8l5 5-2.587 2.587-5-5ZM2 16l2-2 2 2-2 2zM14 4l2-2 2 2-2 2zM2 4l2-2 2 2-2 2z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-wand-2 {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M29.414 24 12 6.586a2.048 2.048 0 0 0-2.828 0L6.586 9.172a2.002 2.002 0 0 0 0 2.828l17.413 17.414a2.002 2.002 0 0 0 2.828 0l2.587-2.586a2 2 0 0 0 0-2.828ZM8 10.586 10.586 8l5 5-2.587 2.587-5-5ZM2 16l2-2 2 2-2 2zM14 4l2-2 2 2-2 2zM2 4l2-2 2 2-2 2z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.workspace-glyph {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M16 17v8H6v-8h10m0-2H6a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-8a2 2 0 0 0-2-2Zm11-9v5H17V6h10m0-2H17a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2Zm0 13v5h-5v-5h5m0-2h-5a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h5a2 2 0 0 0 2-2v-5a2 2 0 0 0-2-2ZM11 6v5H6V6h5m0-2H6a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h5a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-wrench {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M12.1 2a9.8 9.8 0 0 0-5.4 1.6l6.4 6.4a2.1 2.1 0 0 1 .2 3 2.1 2.1 0 0 1-3-.2L3.7 6.4A9.84 9.84 0 0 0 2 12.1a10.14 10.14 0 0 0 10.1 10.1 10.9 10.9 0 0 0 2.6-.3l6.7 6.7a5 5 0 0 0 7.1-7.1l-6.7-6.7a10.9 10.9 0 0 0 .3-2.6A10 10 0 0 0 12.1 2Zm8 10.1a7.61 7.61 0 0 1-.3 2.1l-.3 1.1.8.8 6.7 6.7a2.88 2.88 0 0 1 .9 2.1A2.72 2.72 0 0 1 27 27a2.9 2.9 0 0 1-4.2 0l-6.7-6.7-.8-.8-1.1.3a7.61 7.61 0 0 1-2.1.3 8.27 8.27 0 0 1-5.7-2.3A7.63 7.63 0 0 1 4 12.1a8.33 8.33 0 0 1 .3-2.2l4.4 4.4a4.14 4.14 0 0 0 5.9.2 4.14 4.14 0 0 0-.2-5.9L10 4.2a6.45 6.45 0 0 1 2-.3 8.27 8.27 0 0 1 5.7 2.3 8.49 8.49 0 0 1 2.4 5.9Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.lucide-calendar-minus {
  -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 100 100"><path fill="red" d="M33.333 54.167H66.667V62.5H33.333z M79.167 16.667h-8.333V8.333h-8.333v8.333H37.5V8.333H29.167v8.333H20.833C16.238 16.667 12.5 20.404 12.5 25v8.333v50c0 4.596 3.738 8.333 8.333 8.333h58.333c4.596 0 8.333 -3.738 8.333 -8.333V33.333V25C87.5 20.404 83.763 16.667 79.167 16.667zM79.175 83.333H20.833V33.333h58.333L79.175 83.333z"></path></svg>');
}
body:not(.no-sanctum-icons) svg.lucide-glasses {
  transform: translateY(1px) scale(1.1) translateX(-1.5px);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M30.94 15.66A16.69 16.69 0 0 0 16 5 16.69 16.69 0 0 0 1.06 15.66a1 1 0 0 0 0 .68A16.69 16.69 0 0 0 16 27a16.69 16.69 0 0 0 14.94-10.66 1 1 0 0 0 0-.68ZM16 25c-5.3 0-10.9-3.93-12.93-9C5.1 10.93 10.7 7 16 7s10.9 3.93 12.93 9C26.9 21.07 21.3 25 16 25Z'/%3E%3Cpath d='M16 10a6 6 0 1 0 6 6 6 6 0 0 0-6-6Zm0 10a4 4 0 1 1 4-4 4 4 0 0 1-4 4Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.fa-copy {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg id='icon' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:none;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3Ecopy%3C/title%3E%3Cpath d='M28,10V28H10V10H28m0-2H10a2,2,0,0,0-2,2V28a2,2,0,0,0,2,2H28a2,2,0,0,0,2-2V10a2,2,0,0,0-2-2Z' transform='translate(0)'/%3E%3Cpath d='M4,18H2V4A2,2,0,0,1,4,2H18V4H4Z' transform='translate(0)'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E");
  height: 20px;
  width: 20px;
}
body:not(.no-sanctum-icons) svg.bar-graph {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg id='icon' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:none;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3Eordinal%3C/title%3E%3Cpath d='M26,26V4H18v6H12v6H6V26H2v2H30V26ZM8,26V18h4v8Zm6,0V12h4V26Zm6,0V6h4V26Z'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.BC-trail-icon {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg id='icon' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E .cls-1 %7B fill: none; %7D %3C/style%3E%3C/defs%3E%3Cpath d='M30,12V4H22V7H18a2.0023,2.0023,0,0,0-2,2v6H10V12H2v8h8V17h6v6a2.0023,2.0023,0,0,0,2,2h4v3h8V20H22v3H18V9h4v3ZM8,18H4V14H8Zm16,4h4v4H24ZM24,6h4v4H24Z'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E%0A");
}
body:not(.no-sanctum-icons) svg.longform {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg id='icon' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:none;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3Ecatalog%3C/title%3E%3Cpath d='M26,2H8A2,2,0,0,0,6,4V8H4v2H6v5H4v2H6v5H4v2H6v4a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V4A2,2,0,0,0,26,2Zm0,26H8V24h2V22H8V17h2V15H8V10h2V8H8V4H26Z' transform='translate(0 0)'/%3E%3Crect x='14' y='8' width='8' height='2'/%3E%3Crect x='14' y='15' width='8' height='2'/%3E%3Crect x='14' y='22' width='8' height='2'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.sweep {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg id='icon' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E .cls-1 %7B fill: none; %7D %3C/style%3E%3C/defs%3E%3Ctitle%3Eclean%3C/title%3E%3Crect x='20' y='18' width='6' height='2' transform='translate(46 38) rotate(-180)'/%3E%3Crect x='24' y='26' width='6' height='2' transform='translate(54 54) rotate(-180)'/%3E%3Crect x='22' y='22' width='6' height='2' transform='translate(50 46) rotate(-180)'/%3E%3Cpath d='M17.0029,20a4.8952,4.8952,0,0,0-2.4044-4.1729L22,3,20.2691,2,12.6933,15.126A5.6988,5.6988,0,0,0,7.45,16.6289C3.7064,20.24,3.9963,28.6821,4.01,29.04a1,1,0,0,0,1,.96H20.0012a1,1,0,0,0,.6-1.8C17.0615,25.5439,17.0029,20.0537,17.0029,20ZM11.93,16.9971A3.11,3.11,0,0,1,15.0041,20c0,.0381.0019.208.0168.4688L9.1215,17.8452A3.8,3.8,0,0,1,11.93,16.9971ZM15.4494,28A5.2,5.2,0,0,1,14,25H12a6.4993,6.4993,0,0,0,.9684,3H10.7451A16.6166,16.6166,0,0,1,10,24H8a17.3424,17.3424,0,0,0,.6652,4H6c.031-1.8364.29-5.8921,1.8027-8.5527l7.533,3.35A13.0253,13.0253,0,0,0,17.5968,28Z'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E%0A");
}
body:not(.no-sanctum-icons) svg.excalidraw-icon {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg id='icon' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E .cls-1 %7B fill: none; %7D %3C/style%3E%3C/defs%3E%3Cpath d='M29.707,5.293l-3-3a.9994.9994,0,0,0-1.414,0L19.5859,8H17.0947A11.0118,11.0118,0,0,0,6.7124,15.3662L2.0562,28.67a1,1,0,0,0,1.2744,1.2739l13.3037-4.6562A11.012,11.012,0,0,0,24,14.9053V12.4141L29.707,6.707A.9994.9994,0,0,0,29.707,5.293Zm-7.414,6A1,1,0,0,0,22,12v2.9053A9.01,9.01,0,0,1,15.9731,23.4l-9.1677,3.209L16,17.4141,14.5859,16,5.3914,25.1948,8.6,16.0269A9.01,9.01,0,0,1,17.0947,10H20a1,1,0,0,0,.707-.293L26,4.4141,27.5859,6Z'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E%0A");
}
body:not(.no-sanctum-icons) svg.save-png,
body:not(.no-sanctum-icons) svg.save-svg,
body:not(.no-sanctum-icons) svg.disk {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg id='icon' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:none;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3Esave%3C/title%3E%3Cpath d='M27.71,9.29l-5-5A1,1,0,0,0,22,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V10A1,1,0,0,0,27.71,9.29ZM12,6h8v4H12Zm8,20H12V18h8Zm2,0V18a2,2,0,0,0-2-2H12a2,2,0,0,0-2,2v8H6V6h4v4a2,2,0,0,0,2,2h8a2,2,0,0,0,2-2V6.41l4,4V26Z'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.fa-Images {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg id='icon' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E .cls-1 %7B fill: none; %7D %3C/style%3E%3C/defs%3E%3Cpath d='M4,22H2V4A2.0023,2.0023,0,0,1,4,2H22V4H4Z'/%3E%3Cpath d='M21,17a3,3,0,1,0-3-3A3.0033,3.0033,0,0,0,21,17Zm0-4a1,1,0,1,1-1,1A1.0009,1.0009,0,0,1,21,13Z'/%3E%3Cpath d='M28,7H9A2.0025,2.0025,0,0,0,7,9V28a2.0025,2.0025,0,0,0,2,2H28a2.0025,2.0025,0,0,0,2-2V9A2.0025,2.0025,0,0,0,28,7Zm0,21H9v-6l4-3.9971,5.5859,5.586a2,2,0,0,0,2.8282,0L23,22.0034,28,27Zm0-3.8281-3.5859-3.586a2,2,0,0,0-2.8282,0L20,22.1719l-5.5859-5.586a2,2,0,0,0-2.8282,0L9,19.1719V9H28Z'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E%0A");
}
body:not(.no-sanctum-icons) svg.fa-search {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg id='icon' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E .cls-1 %7B fill: none; %7D %3C/style%3E%3C/defs%3E%3Cpath d='M29,27.5859l-7.5521-7.5521a11.0177,11.0177,0,1,0-1.4141,1.4141L27.5859,29ZM4,13a9,9,0,1,1,9,9A9.01,9.01,0,0,1,4,13Z' transform='translate(0 0)'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E%0A");
}
body:not(.no-sanctum-icons) svg.chart {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg id='icon' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E .cls-1 %7B fill: none; %7D %3C/style%3E%3C/defs%3E%3Cpath d='M27,28V6H19V28H15V14H7V28H4V2H2V28a2,2,0,0,0,2,2H30V28ZM13,28H9V16h4Zm12,0H21V8h4Z'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E%0A");
}
body:not(.no-sanctum-icons) svg.changelog {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg id='icon' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E .cls-1 %7B fill: none; %7D %3C/style%3E%3C/defs%3E%3Cpath d='M16,21a1.5,1.5,0,1,0,1.5,1.5A1.5,1.5,0,0,0,16,21Z'/%3E%3Crect x='15' y='8' width='2' height='10'/%3E%3Cpath d='M23,29H9a1,1,0,0,1-.8638-.4961l-7-12a1,1,0,0,1,0-1.0078l7-12A1,1,0,0,1,9,3H23a1,1,0,0,1,.8638.4961l7,12a1,1,0,0,1,0,1.0078l-7,12A1,1,0,0,1,23,29ZM9.5742,27H22.4258l6.4165-11L22.4258,5H9.5742L3.1577,16Z'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E%0A");
}
body:not(.no-sanctum-icons) svg.fantasy-calendar-reveal {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg id='icon' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cdefs%3E%3Cstyle%3E .cls-1 %7B fill: none; %7D %3C/style%3E%3C/defs%3E%3Cpath d='M26,4H22V2H20V4H12V2H10V4H6A2.0025,2.0025,0,0,0,4,6V26a2.0025,2.0025,0,0,0,2,2H26a2.0025,2.0025,0,0,0,2-2V6A2.0025,2.0025,0,0,0,26,4ZM6,6h4V8h2V6h8V8h2V6h4l0,4H6Zm0,6h5v6H6ZM19,26H13V20h6Zm0-8H13V12h6Zm2,8V20h5l.0012,6Z'/%3E%3Crect id='_Transparent_Rectangle_' data-name='&lt;Transparent Rectangle&gt;' class='cls-1' width='32' height='32'/%3E%3C/svg%3E%0A");
}
body:not(.no-sanctum-icons) svg.BratIcon {
  -webkit-mask-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='utf-8'%3F%3E%3C!-- Generator: Adobe Illustrator 25.2.1, SVG Export Plug-In . SVG Version: 6.00 Build 0) --%3E%3Csvg version='1.1' id='icon' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 32 32' style='enable-background:new 0 0 32 32;' xml:space='preserve'%3E%3Cstyle type='text/css'%3E .st0%7Bfill:none;%7D .st1%7Bfill:none;stroke:%23000000;stroke-width:1.5;stroke-miterlimit:10;%7D%0A%3C/style%3E%3Cpath d='M16,2C8.3,2,2,8.3,2,16s6.3,14,14,14s14-6.3,14-14S23.7,2,16,2z M16,28C9.4,28,4,22.6,4,16S9.4,4,16,4s12,5.4,12,12 S22.6,28,16,28z'/%3E%3Cpath d='M20.5,11c-1.4,0-2.5,1.1-2.5,2.5s1.1,2.5,2.5,2.5s2.5-1.1,2.5-2.5S21.9,11,20.5,11z'/%3E%3Crect x='8' y='13' width='6' height='2'/%3E%3Cpath d='M16,24c2.8,0,5.4-1.5,6.9-3.9l-1.7-1c-1.7,2.8-5.4,3.8-8.2,2c-0.8-0.5-1.5-1.2-2-2l-1.7,1C10.6,22.5,13.2,24,16,24z'/%3E%3Crect id='_Transparent_Rectangle_' class='st0' width='32' height='32'/%3E%3Cpath class='st1' d='M13.3,3.8c-0.4,1.3-0.1,2.8,1.1,3.6c1.4,1.1,3.5,0.8,4.6-0.6'/%3E%3C/svg%3E%0A");
}
body:not(.no-sanctum-icons) svg.running-man {
  -webkit-mask-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='utf-8'%3F%3E%3C!-- Generator: Adobe Illustrator 25.2.1, SVG Export Plug-In . SVG Version: 6.00 Build 0) --%3E%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 32 32' style='enable-background:new 0 0 32 32;' xml:space='preserve'%3E%3Cstyle type='text/css'%3E .st0%7Bfill:none;%7D .st1%7Bfill:none;stroke:%23000000;stroke-width:2;stroke-miterlimit:10;%7D .st2%7Bdisplay:none;%7D .st3%7Bdisplay:inline;%7D%0A%3C/style%3E%3Cg id='icon'%3E%3Cpath d='M19.1,9.3c-1.9,0-3.5-1.6-3.5-3.5s1.6-3.5,3.5-3.5s3.5,1.6,3.5,3.5C22.6,7.7,21,9.3,19.1,9.3z M19.1,4.3 c-0.8,0-1.5,0.7-1.5,1.5s0.7,1.5,1.5,1.5s1.5-0.7,1.5-1.5C20.6,5,19.9,4.3,19.1,4.3z'/%3E%3Crect id='_Transparent_Rectangle_' class='st0' width='32' height='32'/%3E%3Cpolyline class='st1' points='16.8,18 18.8,24.3 17.8,29.6 '/%3E%3Cpath class='st1' d='M15.8,11'/%3E%3Cpath d='M6.9,28.1l-1.1-1.7l4.8-3.1l-0.1-5.5l3.4-8.1c0.2-0.5,0.8-0.8,1.3-0.5l5.1,2.2c0.4,0.2,0.7,0.5,0.8,0.9l1.1,3.1l3.9-1 l0.5,1.9L21,17.7l-1.5-4c-0.1-0.4-0.4-0.7-0.8-0.9l-3.4-1.5l-2.8,6.8l0.1,6.2L6.9,28.1z'/%3E%3Cpolygon points='9.1,18 7.1,18 7.1,12.3 14.4,9.2 15.2,11 9.1,13.7 '/%3E%3C/g%3E%3Cg id='Layer_2' class='st2'%3E%3Cpath class='st3' d='M22.3,15.6l-1.1-3c-0.2-0.8-1.6-1.4-2.8-1.9l-3.6-1.4c-0.5,0-1.1,0.1-1.5,0.4l-6.1,2.7V18h2v-4.4l4.1-1.5 L10.5,18v5.7l-4.6,2.8l0.9,1.6l5.6-3.8V18l2.8-6.7l3.2,1.3c0.4,0.2,0.7,0.3,0.9,0.7l1.7,4.4l5.7-1.5l-0.5-1.7L22.3,15.6z'/%3E%3C/g%3E%3C/svg%3E%0A");
  transform: scale(1.1);
}
body:not(.no-sanctum-icons) svg.feather-tv {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28 4H4a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h8v4H8v2h16v-2h-4v-4h8a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2ZM18 28h-4v-4h4Zm10-6H4V6h24Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.sidebar-right {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28 4H4a2 2 0 0 0-2 2v20a2 2 0 0 0 2 2h24a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2ZM4 6h16v20H4Zm24 20h-6V6h6Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-icons) svg.sidebar-left {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M28 4H4a2 2 0 0 0-2 2v20a2 2 0 0 0 2 2h24a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2ZM4 6h6v20H4Zm24 20H12V6h16Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
body:not(.no-sanctum-ions) svg.lucide-history {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M20.59 22 15 16.41V7h2v8.58l5 5.01L20.59 22z'/%3E%3Cpath d='M16 2A13.94 13.94 0 0 0 6 6.23V2H4v8h8V8H7.08A12 12 0 1 1 4 16H2A14 14 0 1 0 16 2Z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z' data-name='&lt;Transparent Rectangle&gt;'/%3E%3C/svg%3E");
}
.callout[data-callout=grid] {
  background: transparent;
  border: 0;
  margin: 0;
  box-shadow: none;
}
.callout[data-callout=grid] .callout-title {
  display: none;
}
.callout[data-callout=grid] .callout-content {
  display: block;
  width: 100%;
  padding: 0;
}
.callout[data-callout=grid] .callout-content p {
  display: flex;
  flex-wrap: wrap;
  margin-block-start: 0;
  margin-block-end: 0;
  justify-content: center;
  gap: 4px;
}
.callout[data-callout=grid] .callout-content img {
  max-height: 300px;
}
.callout[data-callout=grid][data-callout-metadata="100"] .callout-content img {
  max-height: 100px;
}
.callout[data-callout=grid][data-callout-metadata="150"] .callout-content img {
  max-height: 150px;
}
.callout[data-callout=grid][data-callout-metadata="200"] .callout-content img {
  max-height: 200px;
}
.callout[data-callout=grid][data-callout-metadata="250"] .callout-content img {
  max-height: 250px;
}
.callout[data-callout=grid][data-callout-metadata="300"] .callout-content img {
  max-height: 300px;
}
.callout[data-callout=grid][data-callout-metadata="350"] .callout-content img {
  max-height: 350px;
}
.callout[data-callout=grid][data-callout-metadata="400"] .callout-content img {
  max-height: 400px;
}
.callout[data-callout=grid][data-callout-metadata="450"] .callout-content img {
  max-height: 450px;
}
.callout[data-callout=grid][data-callout-metadata="500"] .callout-content img {
  max-height: 500px;
}
.nord.theme-light p > code,
.nord.theme-dark p > code,
.nord.theme-light pre:not(pre.frontmatter),
.nord.theme-dark pre:not(pre.frontmatter),
.nord.theme-dark .HyperMD-codeblock,
.nord.theme-light .HyperMD-codeblock {
  --code-background: #2E3440;
  --code-normal: #E5E9F0;
  --text-muted: #81A1C1;
  --text-faint: #81A1C1;
}
.nord.theme-light p > code .cm-comment,
.nord.theme-light p > code .cm-meta,
.nord.theme-light p > code .token.comment,
.nord.theme-light p > code .token.prolog,
.nord.theme-light p > code .token.doctype,
.nord.theme-light p > code .token.cdata,
.nord.theme-dark p > code .cm-comment,
.nord.theme-dark p > code .cm-meta,
.nord.theme-dark p > code .token.comment,
.nord.theme-dark p > code .token.prolog,
.nord.theme-dark p > code .token.doctype,
.nord.theme-dark p > code .token.cdata,
.nord.theme-light pre:not(pre.frontmatter) .cm-comment,
.nord.theme-light pre:not(pre.frontmatter) .cm-meta,
.nord.theme-light pre:not(pre.frontmatter) .token.comment,
.nord.theme-light pre:not(pre.frontmatter) .token.prolog,
.nord.theme-light pre:not(pre.frontmatter) .token.doctype,
.nord.theme-light pre:not(pre.frontmatter) .token.cdata,
.nord.theme-dark pre:not(pre.frontmatter) .cm-comment,
.nord.theme-dark pre:not(pre.frontmatter) .cm-meta,
.nord.theme-dark pre:not(pre.frontmatter) .token.comment,
.nord.theme-dark pre:not(pre.frontmatter) .token.prolog,
.nord.theme-dark pre:not(pre.frontmatter) .token.doctype,
.nord.theme-dark pre:not(pre.frontmatter) .token.cdata,
.nord.theme-dark .HyperMD-codeblock .cm-comment,
.nord.theme-dark .HyperMD-codeblock .cm-meta,
.nord.theme-dark .HyperMD-codeblock .token.comment,
.nord.theme-dark .HyperMD-codeblock .token.prolog,
.nord.theme-dark .HyperMD-codeblock .token.doctype,
.nord.theme-dark .HyperMD-codeblock .token.cdata,
.nord.theme-light .HyperMD-codeblock .cm-comment,
.nord.theme-light .HyperMD-codeblock .cm-meta,
.nord.theme-light .HyperMD-codeblock .token.comment,
.nord.theme-light .HyperMD-codeblock .token.prolog,
.nord.theme-light .HyperMD-codeblock .token.doctype,
.nord.theme-light .HyperMD-codeblock .token.cdata {
  color: #636f88;
}
.nord.theme-light p > code .cm-punctuation,
.nord.theme-light p > code .token.punctuation,
.nord.theme-dark p > code .cm-punctuation,
.nord.theme-dark p > code .token.punctuation,
.nord.theme-light pre:not(pre.frontmatter) .cm-punctuation,
.nord.theme-light pre:not(pre.frontmatter) .token.punctuation,
.nord.theme-dark pre:not(pre.frontmatter) .cm-punctuation,
.nord.theme-dark pre:not(pre.frontmatter) .token.punctuation,
.nord.theme-dark .HyperMD-codeblock .cm-punctuation,
.nord.theme-dark .HyperMD-codeblock .token.punctuation,
.nord.theme-light .HyperMD-codeblock .cm-punctuation,
.nord.theme-light .HyperMD-codeblock .token.punctuation {
  color: #81A1C1;
}
.nord.theme-light p > code .namespace,
.nord.theme-dark p > code .namespace,
.nord.theme-light pre:not(pre.frontmatter) .namespace,
.nord.theme-dark pre:not(pre.frontmatter) .namespace,
.nord.theme-dark .HyperMD-codeblock .namespace,
.nord.theme-light .HyperMD-codeblock .namespace {
  opacity: var(--text-muted);
}
.nord.theme-light p > code .cm-bracket.cm-tag,
.nord.theme-light p > code .cm-hr,
.nord.theme-light p > code .cm-tag,
.nord.theme-light p > code .cm-property,
.nord.theme-light p > code .token.property,
.nord.theme-light p > code .token.tag,
.nord.theme-light p > code .token.constant,
.nord.theme-light p > code .token.symbol,
.nord.theme-light p > code .token.deleted,
.nord.theme-dark p > code .cm-bracket.cm-tag,
.nord.theme-dark p > code .cm-hr,
.nord.theme-dark p > code .cm-tag,
.nord.theme-dark p > code .cm-property,
.nord.theme-dark p > code .token.property,
.nord.theme-dark p > code .token.tag,
.nord.theme-dark p > code .token.constant,
.nord.theme-dark p > code .token.symbol,
.nord.theme-dark p > code .token.deleted,
.nord.theme-light pre:not(pre.frontmatter) .cm-bracket.cm-tag,
.nord.theme-light pre:not(pre.frontmatter) .cm-hr,
.nord.theme-light pre:not(pre.frontmatter) .cm-tag,
.nord.theme-light pre:not(pre.frontmatter) .cm-property,
.nord.theme-light pre:not(pre.frontmatter) .token.property,
.nord.theme-light pre:not(pre.frontmatter) .token.tag,
.nord.theme-light pre:not(pre.frontmatter) .token.constant,
.nord.theme-light pre:not(pre.frontmatter) .token.symbol,
.nord.theme-light pre:not(pre.frontmatter) .token.deleted,
.nord.theme-dark pre:not(pre.frontmatter) .cm-bracket.cm-tag,
.nord.theme-dark pre:not(pre.frontmatter) .cm-hr,
.nord.theme-dark pre:not(pre.frontmatter) .cm-tag,
.nord.theme-dark pre:not(pre.frontmatter) .cm-property,
.nord.theme-dark pre:not(pre.frontmatter) .token.property,
.nord.theme-dark pre:not(pre.frontmatter) .token.tag,
.nord.theme-dark pre:not(pre.frontmatter) .token.constant,
.nord.theme-dark pre:not(pre.frontmatter) .token.symbol,
.nord.theme-dark pre:not(pre.frontmatter) .token.deleted,
.nord.theme-dark .HyperMD-codeblock .cm-bracket.cm-tag,
.nord.theme-dark .HyperMD-codeblock .cm-hr,
.nord.theme-dark .HyperMD-codeblock .cm-tag,
.nord.theme-dark .HyperMD-codeblock .cm-property,
.nord.theme-dark .HyperMD-codeblock .token.property,
.nord.theme-dark .HyperMD-codeblock .token.tag,
.nord.theme-dark .HyperMD-codeblock .token.constant,
.nord.theme-dark .HyperMD-codeblock .token.symbol,
.nord.theme-dark .HyperMD-codeblock .token.deleted,
.nord.theme-light .HyperMD-codeblock .cm-bracket.cm-tag,
.nord.theme-light .HyperMD-codeblock .cm-hr,
.nord.theme-light .HyperMD-codeblock .cm-tag,
.nord.theme-light .HyperMD-codeblock .cm-property,
.nord.theme-light .HyperMD-codeblock .token.property,
.nord.theme-light .HyperMD-codeblock .token.tag,
.nord.theme-light .HyperMD-codeblock .token.constant,
.nord.theme-light .HyperMD-codeblock .token.symbol,
.nord.theme-light .HyperMD-codeblock .token.deleted {
  color: #81A1C1;
}
.nord.theme-light p > code .cm-number,
.nord.theme-light p > code .token.number,
.nord.theme-dark p > code .cm-number,
.nord.theme-dark p > code .token.number,
.nord.theme-light pre:not(pre.frontmatter) .cm-number,
.nord.theme-light pre:not(pre.frontmatter) .token.number,
.nord.theme-dark pre:not(pre.frontmatter) .cm-number,
.nord.theme-dark pre:not(pre.frontmatter) .token.number,
.nord.theme-dark .HyperMD-codeblock .cm-number,
.nord.theme-dark .HyperMD-codeblock .token.number,
.nord.theme-light .HyperMD-codeblock .cm-number,
.nord.theme-light .HyperMD-codeblock .token.number {
  color: #B48EAD;
}
.nord.theme-light p > code .token.boolean,
.nord.theme-dark p > code .token.boolean,
.nord.theme-light pre:not(pre.frontmatter) .token.boolean,
.nord.theme-dark pre:not(pre.frontmatter) .token.boolean,
.nord.theme-dark .HyperMD-codeblock .token.boolean,
.nord.theme-light .HyperMD-codeblock .token.boolean {
  color: #81A1C1;
}
.nord.theme-light p > code .cm-builtin,
.nord.theme-light p > code .cm-string-2,
.nord.theme-light p > code .cm-qualifier,
.nord.theme-light p > code .token.selector,
.nord.theme-light p > code .token.attr-name,
.nord.theme-light p > code .token.string,
.nord.theme-light p > code .token.char,
.nord.theme-light p > code .token.builtin,
.nord.theme-light p > code .token.inserted,
.nord.theme-dark p > code .cm-builtin,
.nord.theme-dark p > code .cm-string-2,
.nord.theme-dark p > code .cm-qualifier,
.nord.theme-dark p > code .token.selector,
.nord.theme-dark p > code .token.attr-name,
.nord.theme-dark p > code .token.string,
.nord.theme-dark p > code .token.char,
.nord.theme-dark p > code .token.builtin,
.nord.theme-dark p > code .token.inserted,
.nord.theme-light pre:not(pre.frontmatter) .cm-builtin,
.nord.theme-light pre:not(pre.frontmatter) .cm-string-2,
.nord.theme-light pre:not(pre.frontmatter) .cm-qualifier,
.nord.theme-light pre:not(pre.frontmatter) .token.selector,
.nord.theme-light pre:not(pre.frontmatter) .token.attr-name,
.nord.theme-light pre:not(pre.frontmatter) .token.string,
.nord.theme-light pre:not(pre.frontmatter) .token.char,
.nord.theme-light pre:not(pre.frontmatter) .token.builtin,
.nord.theme-light pre:not(pre.frontmatter) .token.inserted,
.nord.theme-dark pre:not(pre.frontmatter) .cm-builtin,
.nord.theme-dark pre:not(pre.frontmatter) .cm-string-2,
.nord.theme-dark pre:not(pre.frontmatter) .cm-qualifier,
.nord.theme-dark pre:not(pre.frontmatter) .token.selector,
.nord.theme-dark pre:not(pre.frontmatter) .token.attr-name,
.nord.theme-dark pre:not(pre.frontmatter) .token.string,
.nord.theme-dark pre:not(pre.frontmatter) .token.char,
.nord.theme-dark pre:not(pre.frontmatter) .token.builtin,
.nord.theme-dark pre:not(pre.frontmatter) .token.inserted,
.nord.theme-dark .HyperMD-codeblock .cm-builtin,
.nord.theme-dark .HyperMD-codeblock .cm-string-2,
.nord.theme-dark .HyperMD-codeblock .cm-qualifier,
.nord.theme-dark .HyperMD-codeblock .token.selector,
.nord.theme-dark .HyperMD-codeblock .token.attr-name,
.nord.theme-dark .HyperMD-codeblock .token.string,
.nord.theme-dark .HyperMD-codeblock .token.char,
.nord.theme-dark .HyperMD-codeblock .token.builtin,
.nord.theme-dark .HyperMD-codeblock .token.inserted,
.nord.theme-light .HyperMD-codeblock .cm-builtin,
.nord.theme-light .HyperMD-codeblock .cm-string-2,
.nord.theme-light .HyperMD-codeblock .cm-qualifier,
.nord.theme-light .HyperMD-codeblock .token.selector,
.nord.theme-light .HyperMD-codeblock .token.attr-name,
.nord.theme-light .HyperMD-codeblock .token.string,
.nord.theme-light .HyperMD-codeblock .token.char,
.nord.theme-light .HyperMD-codeblock .token.builtin,
.nord.theme-light .HyperMD-codeblock .token.inserted {
  color: #A3BE8C;
}
.nord.theme-light p > code .cm-variable,
.nord.theme-light p > code .cm-variable-2,
.nord.theme-light p > code .cm-variable-3,
.nord.theme-light p > code .cm-operator,
.nord.theme-light p > code .cm-link,
.nord.theme-light p > code .token.operator,
.nord.theme-light p > code .token.entity,
.nord.theme-light p > code .token.url,
.nord.theme-light p > code .language-css .token.string,
.nord.theme-light p > code .style .token.string,
.nord.theme-light p > code .token.variable,
.nord.theme-dark p > code .cm-variable,
.nord.theme-dark p > code .cm-variable-2,
.nord.theme-dark p > code .cm-variable-3,
.nord.theme-dark p > code .cm-operator,
.nord.theme-dark p > code .cm-link,
.nord.theme-dark p > code .token.operator,
.nord.theme-dark p > code .token.entity,
.nord.theme-dark p > code .token.url,
.nord.theme-dark p > code .language-css .token.string,
.nord.theme-dark p > code .style .token.string,
.nord.theme-dark p > code .token.variable,
.nord.theme-light pre:not(pre.frontmatter) .cm-variable,
.nord.theme-light pre:not(pre.frontmatter) .cm-variable-2,
.nord.theme-light pre:not(pre.frontmatter) .cm-variable-3,
.nord.theme-light pre:not(pre.frontmatter) .cm-operator,
.nord.theme-light pre:not(pre.frontmatter) .cm-link,
.nord.theme-light pre:not(pre.frontmatter) .token.operator,
.nord.theme-light pre:not(pre.frontmatter) .token.entity,
.nord.theme-light pre:not(pre.frontmatter) .token.url,
.nord.theme-light pre:not(pre.frontmatter) .language-css .token.string,
.nord.theme-light pre:not(pre.frontmatter) .style .token.string,
.nord.theme-light pre:not(pre.frontmatter) .token.variable,
.nord.theme-dark pre:not(pre.frontmatter) .cm-variable,
.nord.theme-dark pre:not(pre.frontmatter) .cm-variable-2,
.nord.theme-dark pre:not(pre.frontmatter) .cm-variable-3,
.nord.theme-dark pre:not(pre.frontmatter) .cm-operator,
.nord.theme-dark pre:not(pre.frontmatter) .cm-link,
.nord.theme-dark pre:not(pre.frontmatter) .token.operator,
.nord.theme-dark pre:not(pre.frontmatter) .token.entity,
.nord.theme-dark pre:not(pre.frontmatter) .token.url,
.nord.theme-dark pre:not(pre.frontmatter) .language-css .token.string,
.nord.theme-dark pre:not(pre.frontmatter) .style .token.string,
.nord.theme-dark pre:not(pre.frontmatter) .token.variable,
.nord.theme-dark .HyperMD-codeblock .cm-variable,
.nord.theme-dark .HyperMD-codeblock .cm-variable-2,
.nord.theme-dark .HyperMD-codeblock .cm-variable-3,
.nord.theme-dark .HyperMD-codeblock .cm-operator,
.nord.theme-dark .HyperMD-codeblock .cm-link,
.nord.theme-dark .HyperMD-codeblock .token.operator,
.nord.theme-dark .HyperMD-codeblock .token.entity,
.nord.theme-dark .HyperMD-codeblock .token.url,
.nord.theme-dark .HyperMD-codeblock .language-css .token.string,
.nord.theme-dark .HyperMD-codeblock .style .token.string,
.nord.theme-dark .HyperMD-codeblock .token.variable,
.nord.theme-light .HyperMD-codeblock .cm-variable,
.nord.theme-light .HyperMD-codeblock .cm-variable-2,
.nord.theme-light .HyperMD-codeblock .cm-variable-3,
.nord.theme-light .HyperMD-codeblock .cm-operator,
.nord.theme-light .HyperMD-codeblock .cm-link,
.nord.theme-light .HyperMD-codeblock .token.operator,
.nord.theme-light .HyperMD-codeblock .token.entity,
.nord.theme-light .HyperMD-codeblock .token.url,
.nord.theme-light .HyperMD-codeblock .language-css .token.string,
.nord.theme-light .HyperMD-codeblock .style .token.string,
.nord.theme-light .HyperMD-codeblock .token.variable {
  color: #81A1C1;
}
.nord.theme-light p > code .cm-string,
.nord.theme-light p > code .cm-attribute,
.nord.theme-light p > code .token.atrule,
.nord.theme-light p > code .token.attr-value,
.nord.theme-light p > code .token.function,
.nord.theme-light p > code .token.class-name,
.nord.theme-dark p > code .cm-string,
.nord.theme-dark p > code .cm-attribute,
.nord.theme-dark p > code .token.atrule,
.nord.theme-dark p > code .token.attr-value,
.nord.theme-dark p > code .token.function,
.nord.theme-dark p > code .token.class-name,
.nord.theme-light pre:not(pre.frontmatter) .cm-string,
.nord.theme-light pre:not(pre.frontmatter) .cm-attribute,
.nord.theme-light pre:not(pre.frontmatter) .token.atrule,
.nord.theme-light pre:not(pre.frontmatter) .token.attr-value,
.nord.theme-light pre:not(pre.frontmatter) .token.function,
.nord.theme-light pre:not(pre.frontmatter) .token.class-name,
.nord.theme-dark pre:not(pre.frontmatter) .cm-string,
.nord.theme-dark pre:not(pre.frontmatter) .cm-attribute,
.nord.theme-dark pre:not(pre.frontmatter) .token.atrule,
.nord.theme-dark pre:not(pre.frontmatter) .token.attr-value,
.nord.theme-dark pre:not(pre.frontmatter) .token.function,
.nord.theme-dark pre:not(pre.frontmatter) .token.class-name,
.nord.theme-dark .HyperMD-codeblock .cm-string,
.nord.theme-dark .HyperMD-codeblock .cm-attribute,
.nord.theme-dark .HyperMD-codeblock .token.atrule,
.nord.theme-dark .HyperMD-codeblock .token.attr-value,
.nord.theme-dark .HyperMD-codeblock .token.function,
.nord.theme-dark .HyperMD-codeblock .token.class-name,
.nord.theme-light .HyperMD-codeblock .cm-string,
.nord.theme-light .HyperMD-codeblock .cm-attribute,
.nord.theme-light .HyperMD-codeblock .token.atrule,
.nord.theme-light .HyperMD-codeblock .token.attr-value,
.nord.theme-light .HyperMD-codeblock .token.function,
.nord.theme-light .HyperMD-codeblock .token.class-name {
  color: #88C0D0;
}
.nord.theme-light p > code .cm-keyword,
.nord.theme-light p > code .token.keyword,
.nord.theme-dark p > code .cm-keyword,
.nord.theme-dark p > code .token.keyword,
.nord.theme-light pre:not(pre.frontmatter) .cm-keyword,
.nord.theme-light pre:not(pre.frontmatter) .token.keyword,
.nord.theme-dark pre:not(pre.frontmatter) .cm-keyword,
.nord.theme-dark pre:not(pre.frontmatter) .token.keyword,
.nord.theme-dark .HyperMD-codeblock .cm-keyword,
.nord.theme-dark .HyperMD-codeblock .token.keyword,
.nord.theme-light .HyperMD-codeblock .cm-keyword,
.nord.theme-light .HyperMD-codeblock .token.keyword {
  color: #81A1C1;
}
.nord.theme-light p > code .token.regex,
.nord.theme-light p > code .token.important,
.nord.theme-dark p > code .token.regex,
.nord.theme-dark p > code .token.important,
.nord.theme-light pre:not(pre.frontmatter) .token.regex,
.nord.theme-light pre:not(pre.frontmatter) .token.important,
.nord.theme-dark pre:not(pre.frontmatter) .token.regex,
.nord.theme-dark pre:not(pre.frontmatter) .token.important,
.nord.theme-dark .HyperMD-codeblock .token.regex,
.nord.theme-dark .HyperMD-codeblock .token.important,
.nord.theme-light .HyperMD-codeblock .token.regex,
.nord.theme-light .HyperMD-codeblock .token.important {
  color: #EBCB8B;
}
.nord.theme-light p > code .token.important,
.nord.theme-light p > code .token.bold,
.nord.theme-dark p > code .token.important,
.nord.theme-dark p > code .token.bold,
.nord.theme-light pre:not(pre.frontmatter) .token.important,
.nord.theme-light pre:not(pre.frontmatter) .token.bold,
.nord.theme-dark pre:not(pre.frontmatter) .token.important,
.nord.theme-dark pre:not(pre.frontmatter) .token.bold,
.nord.theme-dark .HyperMD-codeblock .token.important,
.nord.theme-dark .HyperMD-codeblock .token.bold,
.nord.theme-light .HyperMD-codeblock .token.important,
.nord.theme-light .HyperMD-codeblock .token.bold {
  font-weight: bold;
}
.nord.theme-light p > code .token.italic,
.nord.theme-dark p > code .token.italic,
.nord.theme-light pre:not(pre.frontmatter) .token.italic,
.nord.theme-dark pre:not(pre.frontmatter) .token.italic,
.nord.theme-dark .HyperMD-codeblock .token.italic,
.nord.theme-light .HyperMD-codeblock .token.italic {
  font-style: italic;
}
.nord.theme-light p > code,
.nord.theme-light pre:not(pre.frontmatter),
.nord.theme-light .HyperMD-codeblock {
  --code-background: #E5E9F0;
  --code-normal: #3B4252;
}
.dracula.theme-light p > code,
.dracula.theme-dark p > code,
.dracula.theme-light pre:not(.frontmatter),
.dracula.theme-dark pre:not(.frontmatter),
.dracula.theme-light .HyperMD-codeblock,
.dracula.theme-dark .HyperMD-codeblock {
  --code-background: #282A36;
  --code-normal: #f8f8f2;
  --text-muted: #f8f8f2;
  --text-faint: #6272a4;
}
.dracula.theme-light p > code .cm-string-2,
.dracula.theme-light p > code .cm-atom,
.dracula.theme-light p > code .cm-comment,
.dracula.theme-light p > code .cm-meta,
.dracula.theme-light p > code .token.comment,
.dracula.theme-light p > code .token.prolog,
.dracula.theme-light p > code .token.doctype,
.dracula.theme-light p > code .token.cdata,
.dracula.theme-dark p > code .cm-string-2,
.dracula.theme-dark p > code .cm-atom,
.dracula.theme-dark p > code .cm-comment,
.dracula.theme-dark p > code .cm-meta,
.dracula.theme-dark p > code .token.comment,
.dracula.theme-dark p > code .token.prolog,
.dracula.theme-dark p > code .token.doctype,
.dracula.theme-dark p > code .token.cdata,
.dracula.theme-light pre:not(.frontmatter) .cm-string-2,
.dracula.theme-light pre:not(.frontmatter) .cm-atom,
.dracula.theme-light pre:not(.frontmatter) .cm-comment,
.dracula.theme-light pre:not(.frontmatter) .cm-meta,
.dracula.theme-light pre:not(.frontmatter) .token.comment,
.dracula.theme-light pre:not(.frontmatter) .token.prolog,
.dracula.theme-light pre:not(.frontmatter) .token.doctype,
.dracula.theme-light pre:not(.frontmatter) .token.cdata,
.dracula.theme-dark pre:not(.frontmatter) .cm-string-2,
.dracula.theme-dark pre:not(.frontmatter) .cm-atom,
.dracula.theme-dark pre:not(.frontmatter) .cm-comment,
.dracula.theme-dark pre:not(.frontmatter) .cm-meta,
.dracula.theme-dark pre:not(.frontmatter) .token.comment,
.dracula.theme-dark pre:not(.frontmatter) .token.prolog,
.dracula.theme-dark pre:not(.frontmatter) .token.doctype,
.dracula.theme-dark pre:not(.frontmatter) .token.cdata,
.dracula.theme-light .HyperMD-codeblock .cm-string-2,
.dracula.theme-light .HyperMD-codeblock .cm-atom,
.dracula.theme-light .HyperMD-codeblock .cm-comment,
.dracula.theme-light .HyperMD-codeblock .cm-meta,
.dracula.theme-light .HyperMD-codeblock .token.comment,
.dracula.theme-light .HyperMD-codeblock .token.prolog,
.dracula.theme-light .HyperMD-codeblock .token.doctype,
.dracula.theme-light .HyperMD-codeblock .token.cdata,
.dracula.theme-dark .HyperMD-codeblock .cm-string-2,
.dracula.theme-dark .HyperMD-codeblock .cm-atom,
.dracula.theme-dark .HyperMD-codeblock .cm-comment,
.dracula.theme-dark .HyperMD-codeblock .cm-meta,
.dracula.theme-dark .HyperMD-codeblock .token.comment,
.dracula.theme-dark .HyperMD-codeblock .token.prolog,
.dracula.theme-dark .HyperMD-codeblock .token.doctype,
.dracula.theme-dark .HyperMD-codeblock .token.cdata {
  color: #6272a4;
}
.dracula.theme-light p > code .cm-bracket.cm-tag,
.dracula.theme-light p > code .cm-hr,
.dracula.theme-light p > code .cm-punctuation,
.dracula.theme-light p > code .token.punctuation,
.dracula.theme-dark p > code .cm-bracket.cm-tag,
.dracula.theme-dark p > code .cm-hr,
.dracula.theme-dark p > code .cm-punctuation,
.dracula.theme-dark p > code .token.punctuation,
.dracula.theme-light pre:not(.frontmatter) .cm-bracket.cm-tag,
.dracula.theme-light pre:not(.frontmatter) .cm-hr,
.dracula.theme-light pre:not(.frontmatter) .cm-punctuation,
.dracula.theme-light pre:not(.frontmatter) .token.punctuation,
.dracula.theme-dark pre:not(.frontmatter) .cm-bracket.cm-tag,
.dracula.theme-dark pre:not(.frontmatter) .cm-hr,
.dracula.theme-dark pre:not(.frontmatter) .cm-punctuation,
.dracula.theme-dark pre:not(.frontmatter) .token.punctuation,
.dracula.theme-light .HyperMD-codeblock .cm-bracket.cm-tag,
.dracula.theme-light .HyperMD-codeblock .cm-hr,
.dracula.theme-light .HyperMD-codeblock .cm-punctuation,
.dracula.theme-light .HyperMD-codeblock .token.punctuation,
.dracula.theme-dark .HyperMD-codeblock .cm-bracket.cm-tag,
.dracula.theme-dark .HyperMD-codeblock .cm-hr,
.dracula.theme-dark .HyperMD-codeblock .cm-punctuation,
.dracula.theme-dark .HyperMD-codeblock .token.punctuation {
  color: #f8f8f2;
}
.dracula.theme-light p > code .cm-type,
.dracula.theme-light p > code .namespace,
.dracula.theme-dark p > code .cm-type,
.dracula.theme-dark p > code .namespace,
.dracula.theme-light pre:not(.frontmatter) .cm-type,
.dracula.theme-light pre:not(.frontmatter) .namespace,
.dracula.theme-dark pre:not(.frontmatter) .cm-type,
.dracula.theme-dark pre:not(.frontmatter) .namespace,
.dracula.theme-light .HyperMD-codeblock .cm-type,
.dracula.theme-light .HyperMD-codeblock .namespace,
.dracula.theme-dark .HyperMD-codeblock .cm-type,
.dracula.theme-dark .HyperMD-codeblock .namespace {
  opacity: 0.7;
}
.dracula.theme-light p > code .cm-def,
.dracula.theme-light p > code .cm-tag,
.dracula.theme-light p > code .cm-property-access,
.dracula.theme-light p > code .cm-property,
.dracula.theme-light p > code .token.property,
.dracula.theme-light p > code .token.tag,
.dracula.theme-light p > code .token.constant,
.dracula.theme-light p > code .token.symbol,
.dracula.theme-light p > code .token.deleted,
.dracula.theme-dark p > code .cm-def,
.dracula.theme-dark p > code .cm-tag,
.dracula.theme-dark p > code .cm-property-access,
.dracula.theme-dark p > code .cm-property,
.dracula.theme-dark p > code .token.property,
.dracula.theme-dark p > code .token.tag,
.dracula.theme-dark p > code .token.constant,
.dracula.theme-dark p > code .token.symbol,
.dracula.theme-dark p > code .token.deleted,
.dracula.theme-light pre:not(.frontmatter) .cm-def,
.dracula.theme-light pre:not(.frontmatter) .cm-tag,
.dracula.theme-light pre:not(.frontmatter) .cm-property-access,
.dracula.theme-light pre:not(.frontmatter) .cm-property,
.dracula.theme-light pre:not(.frontmatter) .token.property,
.dracula.theme-light pre:not(.frontmatter) .token.tag,
.dracula.theme-light pre:not(.frontmatter) .token.constant,
.dracula.theme-light pre:not(.frontmatter) .token.symbol,
.dracula.theme-light pre:not(.frontmatter) .token.deleted,
.dracula.theme-dark pre:not(.frontmatter) .cm-def,
.dracula.theme-dark pre:not(.frontmatter) .cm-tag,
.dracula.theme-dark pre:not(.frontmatter) .cm-property-access,
.dracula.theme-dark pre:not(.frontmatter) .cm-property,
.dracula.theme-dark pre:not(.frontmatter) .token.property,
.dracula.theme-dark pre:not(.frontmatter) .token.tag,
.dracula.theme-dark pre:not(.frontmatter) .token.constant,
.dracula.theme-dark pre:not(.frontmatter) .token.symbol,
.dracula.theme-dark pre:not(.frontmatter) .token.deleted,
.dracula.theme-light .HyperMD-codeblock .cm-def,
.dracula.theme-light .HyperMD-codeblock .cm-tag,
.dracula.theme-light .HyperMD-codeblock .cm-property-access,
.dracula.theme-light .HyperMD-codeblock .cm-property,
.dracula.theme-light .HyperMD-codeblock .token.property,
.dracula.theme-light .HyperMD-codeblock .token.tag,
.dracula.theme-light .HyperMD-codeblock .token.constant,
.dracula.theme-light .HyperMD-codeblock .token.symbol,
.dracula.theme-light .HyperMD-codeblock .token.deleted,
.dracula.theme-dark .HyperMD-codeblock .cm-def,
.dracula.theme-dark .HyperMD-codeblock .cm-tag,
.dracula.theme-dark .HyperMD-codeblock .cm-property-access,
.dracula.theme-dark .HyperMD-codeblock .cm-property,
.dracula.theme-dark .HyperMD-codeblock .token.property,
.dracula.theme-dark .HyperMD-codeblock .token.tag,
.dracula.theme-dark .HyperMD-codeblock .token.constant,
.dracula.theme-dark .HyperMD-codeblock .token.symbol,
.dracula.theme-dark .HyperMD-codeblock .token.deleted {
  color: #ff79c6;
}
.dracula.theme-light p > code .cm-atom,
.dracula.theme-light p > code .cm-number,
.dracula.theme-light p > code .token.boolean,
.dracula.theme-light p > code .token.number,
.dracula.theme-dark p > code .cm-atom,
.dracula.theme-dark p > code .cm-number,
.dracula.theme-dark p > code .token.boolean,
.dracula.theme-dark p > code .token.number,
.dracula.theme-light pre:not(.frontmatter) .cm-atom,
.dracula.theme-light pre:not(.frontmatter) .cm-number,
.dracula.theme-light pre:not(.frontmatter) .token.boolean,
.dracula.theme-light pre:not(.frontmatter) .token.number,
.dracula.theme-dark pre:not(.frontmatter) .cm-atom,
.dracula.theme-dark pre:not(.frontmatter) .cm-number,
.dracula.theme-dark pre:not(.frontmatter) .token.boolean,
.dracula.theme-dark pre:not(.frontmatter) .token.number,
.dracula.theme-light .HyperMD-codeblock .cm-atom,
.dracula.theme-light .HyperMD-codeblock .cm-number,
.dracula.theme-light .HyperMD-codeblock .token.boolean,
.dracula.theme-light .HyperMD-codeblock .token.number,
.dracula.theme-dark .HyperMD-codeblock .cm-atom,
.dracula.theme-dark .HyperMD-codeblock .cm-number,
.dracula.theme-dark .HyperMD-codeblock .token.boolean,
.dracula.theme-dark .HyperMD-codeblock .token.number {
  color: #bd93f9;
}
.dracula.theme-light p > code .cm-attribute,
.dracula.theme-light p > code .cm-variable-2,
.dracula.theme-light p > code .cm-variable-3,
.dracula.theme-light p > code .token.selector,
.dracula.theme-light p > code .token.attr-name,
.dracula.theme-light p > code .token.string,
.dracula.theme-light p > code .token.char,
.dracula.theme-light p > code .token.builtin,
.dracula.theme-light p > code .token.inserted,
.dracula.theme-dark p > code .cm-attribute,
.dracula.theme-dark p > code .cm-variable-2,
.dracula.theme-dark p > code .cm-variable-3,
.dracula.theme-dark p > code .token.selector,
.dracula.theme-dark p > code .token.attr-name,
.dracula.theme-dark p > code .token.string,
.dracula.theme-dark p > code .token.char,
.dracula.theme-dark p > code .token.builtin,
.dracula.theme-dark p > code .token.inserted,
.dracula.theme-light pre:not(.frontmatter) .cm-attribute,
.dracula.theme-light pre:not(.frontmatter) .cm-variable-2,
.dracula.theme-light pre:not(.frontmatter) .cm-variable-3,
.dracula.theme-light pre:not(.frontmatter) .token.selector,
.dracula.theme-light pre:not(.frontmatter) .token.attr-name,
.dracula.theme-light pre:not(.frontmatter) .token.string,
.dracula.theme-light pre:not(.frontmatter) .token.char,
.dracula.theme-light pre:not(.frontmatter) .token.builtin,
.dracula.theme-light pre:not(.frontmatter) .token.inserted,
.dracula.theme-dark pre:not(.frontmatter) .cm-attribute,
.dracula.theme-dark pre:not(.frontmatter) .cm-variable-2,
.dracula.theme-dark pre:not(.frontmatter) .cm-variable-3,
.dracula.theme-dark pre:not(.frontmatter) .token.selector,
.dracula.theme-dark pre:not(.frontmatter) .token.attr-name,
.dracula.theme-dark pre:not(.frontmatter) .token.string,
.dracula.theme-dark pre:not(.frontmatter) .token.char,
.dracula.theme-dark pre:not(.frontmatter) .token.builtin,
.dracula.theme-dark pre:not(.frontmatter) .token.inserted,
.dracula.theme-light .HyperMD-codeblock .cm-attribute,
.dracula.theme-light .HyperMD-codeblock .cm-variable-2,
.dracula.theme-light .HyperMD-codeblock .cm-variable-3,
.dracula.theme-light .HyperMD-codeblock .token.selector,
.dracula.theme-light .HyperMD-codeblock .token.attr-name,
.dracula.theme-light .HyperMD-codeblock .token.string,
.dracula.theme-light .HyperMD-codeblock .token.char,
.dracula.theme-light .HyperMD-codeblock .token.builtin,
.dracula.theme-light .HyperMD-codeblock .token.inserted,
.dracula.theme-dark .HyperMD-codeblock .cm-attribute,
.dracula.theme-dark .HyperMD-codeblock .cm-variable-2,
.dracula.theme-dark .HyperMD-codeblock .cm-variable-3,
.dracula.theme-dark .HyperMD-codeblock .token.selector,
.dracula.theme-dark .HyperMD-codeblock .token.attr-name,
.dracula.theme-dark .HyperMD-codeblock .token.string,
.dracula.theme-dark .HyperMD-codeblock .token.char,
.dracula.theme-dark .HyperMD-codeblock .token.builtin,
.dracula.theme-dark .HyperMD-codeblock .token.inserted {
  color: #50fa7b;
}
.dracula.theme-light p > code .cm-operator,
.dracula.theme-light p > code .token.operator,
.dracula.theme-light p > code .token.entity,
.dracula.theme-light p > code .token.url,
.dracula.theme-light p > code .language-css .token.string,
.dracula.theme-light p > code .style .token.string,
.dracula.theme-light p > code .token.variable,
.dracula.theme-dark p > code .cm-operator,
.dracula.theme-dark p > code .token.operator,
.dracula.theme-dark p > code .token.entity,
.dracula.theme-dark p > code .token.url,
.dracula.theme-dark p > code .language-css .token.string,
.dracula.theme-dark p > code .style .token.string,
.dracula.theme-dark p > code .token.variable,
.dracula.theme-light pre:not(.frontmatter) .cm-operator,
.dracula.theme-light pre:not(.frontmatter) .token.operator,
.dracula.theme-light pre:not(.frontmatter) .token.entity,
.dracula.theme-light pre:not(.frontmatter) .token.url,
.dracula.theme-light pre:not(.frontmatter) .language-css .token.string,
.dracula.theme-light pre:not(.frontmatter) .style .token.string,
.dracula.theme-light pre:not(.frontmatter) .token.variable,
.dracula.theme-dark pre:not(.frontmatter) .cm-operator,
.dracula.theme-dark pre:not(.frontmatter) .token.operator,
.dracula.theme-dark pre:not(.frontmatter) .token.entity,
.dracula.theme-dark pre:not(.frontmatter) .token.url,
.dracula.theme-dark pre:not(.frontmatter) .language-css .token.string,
.dracula.theme-dark pre:not(.frontmatter) .style .token.string,
.dracula.theme-dark pre:not(.frontmatter) .token.variable,
.dracula.theme-light .HyperMD-codeblock .cm-operator,
.dracula.theme-light .HyperMD-codeblock .token.operator,
.dracula.theme-light .HyperMD-codeblock .token.entity,
.dracula.theme-light .HyperMD-codeblock .token.url,
.dracula.theme-light .HyperMD-codeblock .language-css .token.string,
.dracula.theme-light .HyperMD-codeblock .style .token.string,
.dracula.theme-light .HyperMD-codeblock .token.variable,
.dracula.theme-dark .HyperMD-codeblock .cm-operator,
.dracula.theme-dark .HyperMD-codeblock .token.operator,
.dracula.theme-dark .HyperMD-codeblock .token.entity,
.dracula.theme-dark .HyperMD-codeblock .token.url,
.dracula.theme-dark .HyperMD-codeblock .language-css .token.string,
.dracula.theme-dark .HyperMD-codeblock .style .token.string,
.dracula.theme-dark .HyperMD-codeblock .token.variable {
  color: #f8f8f2;
}
.dracula.theme-light p > code .cm-string,
.dracula.theme-light p > code .token.atrule,
.dracula.theme-light p > code .token.attr-value,
.dracula.theme-light p > code .token.function,
.dracula.theme-light p > code .token.class-name,
.dracula.theme-dark p > code .cm-string,
.dracula.theme-dark p > code .token.atrule,
.dracula.theme-dark p > code .token.attr-value,
.dracula.theme-dark p > code .token.function,
.dracula.theme-dark p > code .token.class-name,
.dracula.theme-light pre:not(.frontmatter) .cm-string,
.dracula.theme-light pre:not(.frontmatter) .token.atrule,
.dracula.theme-light pre:not(.frontmatter) .token.attr-value,
.dracula.theme-light pre:not(.frontmatter) .token.function,
.dracula.theme-light pre:not(.frontmatter) .token.class-name,
.dracula.theme-dark pre:not(.frontmatter) .cm-string,
.dracula.theme-dark pre:not(.frontmatter) .token.atrule,
.dracula.theme-dark pre:not(.frontmatter) .token.attr-value,
.dracula.theme-dark pre:not(.frontmatter) .token.function,
.dracula.theme-dark pre:not(.frontmatter) .token.class-name,
.dracula.theme-light .HyperMD-codeblock .cm-string,
.dracula.theme-light .HyperMD-codeblock .token.atrule,
.dracula.theme-light .HyperMD-codeblock .token.attr-value,
.dracula.theme-light .HyperMD-codeblock .token.function,
.dracula.theme-light .HyperMD-codeblock .token.class-name,
.dracula.theme-dark .HyperMD-codeblock .cm-string,
.dracula.theme-dark .HyperMD-codeblock .token.atrule,
.dracula.theme-dark .HyperMD-codeblock .token.attr-value,
.dracula.theme-dark .HyperMD-codeblock .token.function,
.dracula.theme-dark .HyperMD-codeblock .token.class-name {
  color: #f1fa8c;
}
.dracula.theme-light p > code .cm-keyword,
.dracula.theme-light p > code .token.keyword,
.dracula.theme-dark p > code .cm-keyword,
.dracula.theme-dark p > code .token.keyword,
.dracula.theme-light pre:not(.frontmatter) .cm-keyword,
.dracula.theme-light pre:not(.frontmatter) .token.keyword,
.dracula.theme-dark pre:not(.frontmatter) .cm-keyword,
.dracula.theme-dark pre:not(.frontmatter) .token.keyword,
.dracula.theme-light .HyperMD-codeblock .cm-keyword,
.dracula.theme-light .HyperMD-codeblock .token.keyword,
.dracula.theme-dark .HyperMD-codeblock .cm-keyword,
.dracula.theme-dark .HyperMD-codeblock .token.keyword {
  color: #8be9fd;
}
.dracula.theme-light p > code .cm-builtin,
.dracula.theme-light p > code .token.regex,
.dracula.theme-light p > code .token.important,
.dracula.theme-dark p > code .cm-builtin,
.dracula.theme-dark p > code .token.regex,
.dracula.theme-dark p > code .token.important,
.dracula.theme-light pre:not(.frontmatter) .cm-builtin,
.dracula.theme-light pre:not(.frontmatter) .token.regex,
.dracula.theme-light pre:not(.frontmatter) .token.important,
.dracula.theme-dark pre:not(.frontmatter) .cm-builtin,
.dracula.theme-dark pre:not(.frontmatter) .token.regex,
.dracula.theme-dark pre:not(.frontmatter) .token.important,
.dracula.theme-light .HyperMD-codeblock .cm-builtin,
.dracula.theme-light .HyperMD-codeblock .token.regex,
.dracula.theme-light .HyperMD-codeblock .token.important,
.dracula.theme-dark .HyperMD-codeblock .cm-builtin,
.dracula.theme-dark .HyperMD-codeblock .token.regex,
.dracula.theme-dark .HyperMD-codeblock .token.important {
  color: #ffb86c;
}
.dracula.theme-light p > code .token.important,
.dracula.theme-light p > code .token.bold,
.dracula.theme-dark p > code .token.important,
.dracula.theme-dark p > code .token.bold,
.dracula.theme-light pre:not(.frontmatter) .token.important,
.dracula.theme-light pre:not(.frontmatter) .token.bold,
.dracula.theme-dark pre:not(.frontmatter) .token.important,
.dracula.theme-dark pre:not(.frontmatter) .token.bold,
.dracula.theme-light .HyperMD-codeblock .token.important,
.dracula.theme-light .HyperMD-codeblock .token.bold,
.dracula.theme-dark .HyperMD-codeblock .token.important,
.dracula.theme-dark .HyperMD-codeblock .token.bold {
  font-weight: bold;
}
.dracula.theme-light p > code .token.italic,
.dracula.theme-dark p > code .token.italic,
.dracula.theme-light pre:not(.frontmatter) .token.italic,
.dracula.theme-dark pre:not(.frontmatter) .token.italic,
.dracula.theme-light .HyperMD-codeblock .token.italic,
.dracula.theme-dark .HyperMD-codeblock .token.italic {
  font-style: italic;
}
.dracula.theme-light p > code .token.entity,
.dracula.theme-dark p > code .token.entity,
.dracula.theme-light pre:not(.frontmatter) .token.entity,
.dracula.theme-dark pre:not(.frontmatter) .token.entity,
.dracula.theme-light .HyperMD-codeblock .token.entity,
.dracula.theme-dark .HyperMD-codeblock .token.entity {
  cursor: help;
}
.one-dark.theme-light p > code,
.one-dark.theme-light pre:not(.frontmatter),
.one-dark.theme-light .HyperMD-codeblock {
  --code-background: hsl(230, 1%, 98%);
  --code-normal: hsl(230, 8%, 24%);
  --text-muted:hsl(230, 8%, 24%);
  --text-faint:hsl(230, 4%, 64%);
}
.one-dark.theme-light p > code .cm-comment,
.one-dark.theme-light p > code .cm-meta,
.one-dark.theme-light p > code .cm-hr,
.one-dark.theme-light p > code .token.comment,
.one-dark.theme-light p > code .token.prolog,
.one-dark.theme-light p > code .token.cdata,
.one-dark.theme-light pre:not(.frontmatter) .cm-comment,
.one-dark.theme-light pre:not(.frontmatter) .cm-meta,
.one-dark.theme-light pre:not(.frontmatter) .cm-hr,
.one-dark.theme-light pre:not(.frontmatter) .token.comment,
.one-dark.theme-light pre:not(.frontmatter) .token.prolog,
.one-dark.theme-light pre:not(.frontmatter) .token.cdata,
.one-dark.theme-light .HyperMD-codeblock .cm-comment,
.one-dark.theme-light .HyperMD-codeblock .cm-meta,
.one-dark.theme-light .HyperMD-codeblock .cm-hr,
.one-dark.theme-light .HyperMD-codeblock .token.comment,
.one-dark.theme-light .HyperMD-codeblock .token.prolog,
.one-dark.theme-light .HyperMD-codeblock .token.cdata {
  color: hsl(230deg, 4%, 64%);
}
.one-dark.theme-light p > code .cm-punctuation,
.one-dark.theme-light p > code .cm-bracket.cm-tag,
.one-dark.theme-light p > code .cm-type,
.one-dark.theme-light p > code .token.doctype,
.one-dark.theme-light p > code .token.punctuation,
.one-dark.theme-light p > code .token.entity,
.one-dark.theme-light pre:not(.frontmatter) .cm-punctuation,
.one-dark.theme-light pre:not(.frontmatter) .cm-bracket.cm-tag,
.one-dark.theme-light pre:not(.frontmatter) .cm-type,
.one-dark.theme-light pre:not(.frontmatter) .token.doctype,
.one-dark.theme-light pre:not(.frontmatter) .token.punctuation,
.one-dark.theme-light pre:not(.frontmatter) .token.entity,
.one-dark.theme-light .HyperMD-codeblock .cm-punctuation,
.one-dark.theme-light .HyperMD-codeblock .cm-bracket.cm-tag,
.one-dark.theme-light .HyperMD-codeblock .cm-type,
.one-dark.theme-light .HyperMD-codeblock .token.doctype,
.one-dark.theme-light .HyperMD-codeblock .token.punctuation,
.one-dark.theme-light .HyperMD-codeblock .token.entity {
  color: hsl(230deg, 8%, 24%);
}
.one-dark.theme-light p > code .cm-number,
.one-dark.theme-light p > code .cm-attribute,
.one-dark.theme-light p > code .token.attr-name,
.one-dark.theme-light p > code .token.class-name,
.one-dark.theme-light p > code .token.boolean,
.one-dark.theme-light p > code .token.constant,
.one-dark.theme-light p > code .token.number,
.one-dark.theme-light p > code .token.atrule,
.one-dark.theme-light pre:not(.frontmatter) .cm-number,
.one-dark.theme-light pre:not(.frontmatter) .cm-attribute,
.one-dark.theme-light pre:not(.frontmatter) .token.attr-name,
.one-dark.theme-light pre:not(.frontmatter) .token.class-name,
.one-dark.theme-light pre:not(.frontmatter) .token.boolean,
.one-dark.theme-light pre:not(.frontmatter) .token.constant,
.one-dark.theme-light pre:not(.frontmatter) .token.number,
.one-dark.theme-light pre:not(.frontmatter) .token.atrule,
.one-dark.theme-light .HyperMD-codeblock .cm-number,
.one-dark.theme-light .HyperMD-codeblock .cm-attribute,
.one-dark.theme-light .HyperMD-codeblock .token.attr-name,
.one-dark.theme-light .HyperMD-codeblock .token.class-name,
.one-dark.theme-light .HyperMD-codeblock .token.boolean,
.one-dark.theme-light .HyperMD-codeblock .token.constant,
.one-dark.theme-light .HyperMD-codeblock .token.number,
.one-dark.theme-light .HyperMD-codeblock .token.atrule {
  color: hsl(35deg, 99%, 36%);
}
.one-dark.theme-light p > code .cm-qualifier,
.one-dark.theme-light p > code .cm-keyword,
.one-dark.theme-light p > code .token.keyword,
.one-dark.theme-light pre:not(.frontmatter) .cm-qualifier,
.one-dark.theme-light pre:not(.frontmatter) .cm-keyword,
.one-dark.theme-light pre:not(.frontmatter) .token.keyword,
.one-dark.theme-light .HyperMD-codeblock .cm-qualifier,
.one-dark.theme-light .HyperMD-codeblock .cm-keyword,
.one-dark.theme-light .HyperMD-codeblock .token.keyword {
  color: hsl(301deg, 63%, 40%);
}
.one-dark.theme-light p > code .cm-tag,
.one-dark.theme-light p > code .cm-property,
.one-dark.theme-light p > code .token.property,
.one-dark.theme-light p > code .token.tag,
.one-dark.theme-light p > code .token.symbol,
.one-dark.theme-light p > code .token.deleted,
.one-dark.theme-light p > code .token.important,
.one-dark.theme-light pre:not(.frontmatter) .cm-tag,
.one-dark.theme-light pre:not(.frontmatter) .cm-property,
.one-dark.theme-light pre:not(.frontmatter) .token.property,
.one-dark.theme-light pre:not(.frontmatter) .token.tag,
.one-dark.theme-light pre:not(.frontmatter) .token.symbol,
.one-dark.theme-light pre:not(.frontmatter) .token.deleted,
.one-dark.theme-light pre:not(.frontmatter) .token.important,
.one-dark.theme-light .HyperMD-codeblock .cm-tag,
.one-dark.theme-light .HyperMD-codeblock .cm-property,
.one-dark.theme-light .HyperMD-codeblock .token.property,
.one-dark.theme-light .HyperMD-codeblock .token.tag,
.one-dark.theme-light .HyperMD-codeblock .token.symbol,
.one-dark.theme-light .HyperMD-codeblock .token.deleted,
.one-dark.theme-light .HyperMD-codeblock .token.important {
  color: hsl(5deg, 74%, 59%);
}
.one-dark.theme-light p > code .cm-builtin,
.one-dark.theme-light p > code .cm-string,
.one-dark.theme-light p > code .cm-string-2,
.one-dark.theme-light p > code .token.selector,
.one-dark.theme-light p > code .token.string,
.one-dark.theme-light p > code .token.char,
.one-dark.theme-light p > code .token.builtin,
.one-dark.theme-light p > code .token.inserted,
.one-dark.theme-light p > code .token.regex,
.one-dark.theme-light p > code .token.attr-value,
.one-dark.theme-light p > code .token.attr-value > .token.punctuation,
.one-dark.theme-light pre:not(.frontmatter) .cm-builtin,
.one-dark.theme-light pre:not(.frontmatter) .cm-string,
.one-dark.theme-light pre:not(.frontmatter) .cm-string-2,
.one-dark.theme-light pre:not(.frontmatter) .token.selector,
.one-dark.theme-light pre:not(.frontmatter) .token.string,
.one-dark.theme-light pre:not(.frontmatter) .token.char,
.one-dark.theme-light pre:not(.frontmatter) .token.builtin,
.one-dark.theme-light pre:not(.frontmatter) .token.inserted,
.one-dark.theme-light pre:not(.frontmatter) .token.regex,
.one-dark.theme-light pre:not(.frontmatter) .token.attr-value,
.one-dark.theme-light pre:not(.frontmatter) .token.attr-value > .token.punctuation,
.one-dark.theme-light .HyperMD-codeblock .cm-builtin,
.one-dark.theme-light .HyperMD-codeblock .cm-string,
.one-dark.theme-light .HyperMD-codeblock .cm-string-2,
.one-dark.theme-light .HyperMD-codeblock .token.selector,
.one-dark.theme-light .HyperMD-codeblock .token.string,
.one-dark.theme-light .HyperMD-codeblock .token.char,
.one-dark.theme-light .HyperMD-codeblock .token.builtin,
.one-dark.theme-light .HyperMD-codeblock .token.inserted,
.one-dark.theme-light .HyperMD-codeblock .token.regex,
.one-dark.theme-light .HyperMD-codeblock .token.attr-value,
.one-dark.theme-light .HyperMD-codeblock .token.attr-value > .token.punctuation {
  color: hsl(119deg, 34%, 47%);
}
.one-dark.theme-light p > code .cm-operator,
.one-dark.theme-light p > code .cm-variable,
.one-dark.theme-light p > code .cm-variable-2,
.one-dark.theme-light p > code .cm-variable-3,
.one-dark.theme-light p > code .token.variable,
.one-dark.theme-light p > code .token.operator,
.one-dark.theme-light p > code .token.function,
.one-dark.theme-light pre:not(.frontmatter) .cm-operator,
.one-dark.theme-light pre:not(.frontmatter) .cm-variable,
.one-dark.theme-light pre:not(.frontmatter) .cm-variable-2,
.one-dark.theme-light pre:not(.frontmatter) .cm-variable-3,
.one-dark.theme-light pre:not(.frontmatter) .token.variable,
.one-dark.theme-light pre:not(.frontmatter) .token.operator,
.one-dark.theme-light pre:not(.frontmatter) .token.function,
.one-dark.theme-light .HyperMD-codeblock .cm-operator,
.one-dark.theme-light .HyperMD-codeblock .cm-variable,
.one-dark.theme-light .HyperMD-codeblock .cm-variable-2,
.one-dark.theme-light .HyperMD-codeblock .cm-variable-3,
.one-dark.theme-light .HyperMD-codeblock .token.variable,
.one-dark.theme-light .HyperMD-codeblock .token.operator,
.one-dark.theme-light .HyperMD-codeblock .token.function {
  color: hsl(221deg, 87%, 60%);
}
.one-dark.theme-light p > code .cm-link,
.one-dark.theme-light p > code .token.url,
.one-dark.theme-light pre:not(.frontmatter) .cm-link,
.one-dark.theme-light pre:not(.frontmatter) .token.url,
.one-dark.theme-light .HyperMD-codeblock .cm-link,
.one-dark.theme-light .HyperMD-codeblock .token.url {
  color: hsl(198deg, 99%, 37%);
}
.one-dark.theme-light p > code .token.attr-value > .token.punctuation.attr-equals,
.one-dark.theme-light p > code .token.special-attr > .token.attr-value > .token.value.css,
.one-dark.theme-light pre:not(.frontmatter) .token.attr-value > .token.punctuation.attr-equals,
.one-dark.theme-light pre:not(.frontmatter) .token.special-attr > .token.attr-value > .token.value.css,
.one-dark.theme-light .HyperMD-codeblock .token.attr-value > .token.punctuation.attr-equals,
.one-dark.theme-light .HyperMD-codeblock .token.special-attr > .token.attr-value > .token.value.css {
  color: hsl(230deg, 8%, 24%);
}
.one-dark.theme-light p > code .language-css .token.selector,
.one-dark.theme-light pre:not(.frontmatter) .language-css .token.selector,
.one-dark.theme-light .HyperMD-codeblock .language-css .token.selector {
  color: hsl(5deg, 74%, 59%);
}
.one-dark.theme-light p > code .language-css .token.property,
.one-dark.theme-light pre:not(.frontmatter) .language-css .token.property,
.one-dark.theme-light .HyperMD-codeblock .language-css .token.property {
  color: hsl(230deg, 8%, 24%);
}
.one-dark.theme-light p > code .language-css .token.function,
.one-dark.theme-light p > code .language-css .token.url > .token.function,
.one-dark.theme-light pre:not(.frontmatter) .language-css .token.function,
.one-dark.theme-light pre:not(.frontmatter) .language-css .token.url > .token.function,
.one-dark.theme-light .HyperMD-codeblock .language-css .token.function,
.one-dark.theme-light .HyperMD-codeblock .language-css .token.url > .token.function {
  color: hsl(198deg, 99%, 37%);
}
.one-dark.theme-light p > code .language-css .token.url > .token.string.url,
.one-dark.theme-light pre:not(.frontmatter) .language-css .token.url > .token.string.url,
.one-dark.theme-light .HyperMD-codeblock .language-css .token.url > .token.string.url {
  color: hsl(119deg, 34%, 47%);
}
.one-dark.theme-light p > code .language-css .token.important,
.one-dark.theme-light p > code .language-css .token.atrule .token.rule,
.one-dark.theme-light pre:not(.frontmatter) .language-css .token.important,
.one-dark.theme-light pre:not(.frontmatter) .language-css .token.atrule .token.rule,
.one-dark.theme-light .HyperMD-codeblock .language-css .token.important,
.one-dark.theme-light .HyperMD-codeblock .language-css .token.atrule .token.rule {
  color: hsl(301deg, 63%, 40%);
}
.one-dark.theme-light p > code .language-javascript .token.operator,
.one-dark.theme-light pre:not(.frontmatter) .language-javascript .token.operator,
.one-dark.theme-light .HyperMD-codeblock .language-javascript .token.operator {
  color: hsl(301deg, 63%, 40%);
}
.one-dark.theme-light p > code .language-javascript .token.template-string > .token.interpolation > .token.interpolation-punctuation.punctuation,
.one-dark.theme-light pre:not(.frontmatter) .language-javascript .token.template-string > .token.interpolation > .token.interpolation-punctuation.punctuation,
.one-dark.theme-light .HyperMD-codeblock .language-javascript .token.template-string > .token.interpolation > .token.interpolation-punctuation.punctuation {
  color: hsl(344deg, 84%, 43%);
}
.one-dark.theme-light p > code .language-json .token.operator,
.one-dark.theme-light pre:not(.frontmatter) .language-json .token.operator,
.one-dark.theme-light .HyperMD-codeblock .language-json .token.operator {
  color: hsl(230deg, 8%, 24%);
}
.one-dark.theme-light p > code .language-json .token.null.keyword,
.one-dark.theme-light pre:not(.frontmatter) .language-json .token.null.keyword,
.one-dark.theme-light .HyperMD-codeblock .language-json .token.null.keyword {
  color: hsl(35deg, 99%, 36%);
}
.one-dark.theme-light p > code .language-markdown .token.url,
.one-dark.theme-light p > code .language-markdown .token.url > .token.operator,
.one-dark.theme-light p > code .language-markdown .token.url-reference.url > .token.string,
.one-dark.theme-light pre:not(.frontmatter) .language-markdown .token.url,
.one-dark.theme-light pre:not(.frontmatter) .language-markdown .token.url > .token.operator,
.one-dark.theme-light pre:not(.frontmatter) .language-markdown .token.url-reference.url > .token.string,
.one-dark.theme-light .HyperMD-codeblock .language-markdown .token.url,
.one-dark.theme-light .HyperMD-codeblock .language-markdown .token.url > .token.operator,
.one-dark.theme-light .HyperMD-codeblock .language-markdown .token.url-reference.url > .token.string {
  color: hsl(230deg, 8%, 24%);
}
.one-dark.theme-light p > code .language-markdown .token.url > .token.content,
.one-dark.theme-light pre:not(.frontmatter) .language-markdown .token.url > .token.content,
.one-dark.theme-light .HyperMD-codeblock .language-markdown .token.url > .token.content {
  color: hsl(221deg, 87%, 60%);
}
.one-dark.theme-light p > code .language-markdown .token.url > .token.url,
.one-dark.theme-light p > code .language-markdown .token.url-reference.url,
.one-dark.theme-light pre:not(.frontmatter) .language-markdown .token.url > .token.url,
.one-dark.theme-light pre:not(.frontmatter) .language-markdown .token.url-reference.url,
.one-dark.theme-light .HyperMD-codeblock .language-markdown .token.url > .token.url,
.one-dark.theme-light .HyperMD-codeblock .language-markdown .token.url-reference.url {
  color: hsl(198deg, 99%, 37%);
}
.one-dark.theme-light p > code .language-markdown .token.blockquote.punctuation,
.one-dark.theme-light p > code .language-markdown .token.hr.punctuation,
.one-dark.theme-light pre:not(.frontmatter) .language-markdown .token.blockquote.punctuation,
.one-dark.theme-light pre:not(.frontmatter) .language-markdown .token.hr.punctuation,
.one-dark.theme-light .HyperMD-codeblock .language-markdown .token.blockquote.punctuation,
.one-dark.theme-light .HyperMD-codeblock .language-markdown .token.hr.punctuation {
  color: hsl(230deg, 4%, 64%);
  font-style: italic;
}
.one-dark.theme-light p > code .language-markdown .token.code-snippet,
.one-dark.theme-light pre:not(.frontmatter) .language-markdown .token.code-snippet,
.one-dark.theme-light .HyperMD-codeblock .language-markdown .token.code-snippet {
  color: hsl(119deg, 34%, 47%);
}
.one-dark.theme-light p > code .language-markdown .token.bold .token.content,
.one-dark.theme-light pre:not(.frontmatter) .language-markdown .token.bold .token.content,
.one-dark.theme-light .HyperMD-codeblock .language-markdown .token.bold .token.content {
  color: hsl(35deg, 99%, 36%);
}
.one-dark.theme-light p > code .language-markdown .token.italic .token.content,
.one-dark.theme-light pre:not(.frontmatter) .language-markdown .token.italic .token.content,
.one-dark.theme-light .HyperMD-codeblock .language-markdown .token.italic .token.content {
  color: hsl(301deg, 63%, 40%);
}
.one-dark.theme-light p > code .language-markdown .token.strike .token.content,
.one-dark.theme-light p > code .language-markdown .token.strike .token.punctuation,
.one-dark.theme-light p > code .language-markdown .token.list.punctuation,
.one-dark.theme-light p > code .language-markdown .token.title.important > .token.punctuation,
.one-dark.theme-light pre:not(.frontmatter) .language-markdown .token.strike .token.content,
.one-dark.theme-light pre:not(.frontmatter) .language-markdown .token.strike .token.punctuation,
.one-dark.theme-light pre:not(.frontmatter) .language-markdown .token.list.punctuation,
.one-dark.theme-light pre:not(.frontmatter) .language-markdown .token.title.important > .token.punctuation,
.one-dark.theme-light .HyperMD-codeblock .language-markdown .token.strike .token.content,
.one-dark.theme-light .HyperMD-codeblock .language-markdown .token.strike .token.punctuation,
.one-dark.theme-light .HyperMD-codeblock .language-markdown .token.list.punctuation,
.one-dark.theme-light .HyperMD-codeblock .language-markdown .token.title.important > .token.punctuation {
  color: hsl(5deg, 74%, 59%);
}
.one-dark.theme-light p > code .token.bold,
.one-dark.theme-light pre:not(.frontmatter) .token.bold,
.one-dark.theme-light .HyperMD-codeblock .token.bold {
  font-weight: bold;
}
.one-dark.theme-light p > code .token.comment,
.one-dark.theme-light p > code .token.italic,
.one-dark.theme-light pre:not(.frontmatter) .token.comment,
.one-dark.theme-light pre:not(.frontmatter) .token.italic,
.one-dark.theme-light .HyperMD-codeblock .token.comment,
.one-dark.theme-light .HyperMD-codeblock .token.italic {
  font-style: italic;
}
.one-dark.theme-light p > code .token.entity,
.one-dark.theme-light pre:not(.frontmatter) .token.entity,
.one-dark.theme-light .HyperMD-codeblock .token.entity {
  cursor: help;
}
.one-dark.theme-light p > code .token.namespace,
.one-dark.theme-light pre:not(.frontmatter) .token.namespace,
.one-dark.theme-light .HyperMD-codeblock .token.namespace {
  opacity: 0.8;
}
.one-dark.theme-dark p > code,
.one-dark.theme-dark pre:not(.frontmatter),
.one-dark.theme-dark .HyperMD-codeblock {
  --code-background: hsl(220, 13%, 18%);
  --code-normal: hsl(220, 14%, 71%);
  --text-muted:hsl(220, 14%, 71%);
  --text-faint:hsl(220, 10%, 40%);
}
.one-dark.theme-dark p > code .cm-hr,
.one-dark.theme-dark p > code .cm-comment,
.one-dark.theme-dark p > code .cm-meta,
.one-dark.theme-dark p > code .token.comment,
.one-dark.theme-dark p > code .token.prolog,
.one-dark.theme-dark p > code .token.cdata,
.one-dark.theme-dark pre:not(.frontmatter) .cm-hr,
.one-dark.theme-dark pre:not(.frontmatter) .cm-comment,
.one-dark.theme-dark pre:not(.frontmatter) .cm-meta,
.one-dark.theme-dark pre:not(.frontmatter) .token.comment,
.one-dark.theme-dark pre:not(.frontmatter) .token.prolog,
.one-dark.theme-dark pre:not(.frontmatter) .token.cdata,
.one-dark.theme-dark .HyperMD-codeblock .cm-hr,
.one-dark.theme-dark .HyperMD-codeblock .cm-comment,
.one-dark.theme-dark .HyperMD-codeblock .cm-meta,
.one-dark.theme-dark .HyperMD-codeblock .token.comment,
.one-dark.theme-dark .HyperMD-codeblock .token.prolog,
.one-dark.theme-dark .HyperMD-codeblock .token.cdata {
  color: hsl(220deg, 10%, 40%);
}
.one-dark.theme-dark p > code .cm-bracket.cm-tag,
.one-dark.theme-dark p > code .cm-punctuation,
.one-dark.theme-dark p > code .cm-type,
.one-dark.theme-dark p > code .token.doctype,
.one-dark.theme-dark p > code .token.punctuation,
.one-dark.theme-dark p > code .token.entity,
.one-dark.theme-dark pre:not(.frontmatter) .cm-bracket.cm-tag,
.one-dark.theme-dark pre:not(.frontmatter) .cm-punctuation,
.one-dark.theme-dark pre:not(.frontmatter) .cm-type,
.one-dark.theme-dark pre:not(.frontmatter) .token.doctype,
.one-dark.theme-dark pre:not(.frontmatter) .token.punctuation,
.one-dark.theme-dark pre:not(.frontmatter) .token.entity,
.one-dark.theme-dark .HyperMD-codeblock .cm-bracket.cm-tag,
.one-dark.theme-dark .HyperMD-codeblock .cm-punctuation,
.one-dark.theme-dark .HyperMD-codeblock .cm-type,
.one-dark.theme-dark .HyperMD-codeblock .token.doctype,
.one-dark.theme-dark .HyperMD-codeblock .token.punctuation,
.one-dark.theme-dark .HyperMD-codeblock .token.entity {
  color: hsl(220deg, 14%, 71%);
}
.one-dark.theme-dark p > code .cm-number,
.one-dark.theme-dark p > code .cm-attribute,
.one-dark.theme-dark p > code .token.attr-name,
.one-dark.theme-dark p > code .token.class-name,
.one-dark.theme-dark p > code .token.boolean,
.one-dark.theme-dark p > code .token.constant,
.one-dark.theme-dark p > code .token.number,
.one-dark.theme-dark p > code .token.atrule,
.one-dark.theme-dark pre:not(.frontmatter) .cm-number,
.one-dark.theme-dark pre:not(.frontmatter) .cm-attribute,
.one-dark.theme-dark pre:not(.frontmatter) .token.attr-name,
.one-dark.theme-dark pre:not(.frontmatter) .token.class-name,
.one-dark.theme-dark pre:not(.frontmatter) .token.boolean,
.one-dark.theme-dark pre:not(.frontmatter) .token.constant,
.one-dark.theme-dark pre:not(.frontmatter) .token.number,
.one-dark.theme-dark pre:not(.frontmatter) .token.atrule,
.one-dark.theme-dark .HyperMD-codeblock .cm-number,
.one-dark.theme-dark .HyperMD-codeblock .cm-attribute,
.one-dark.theme-dark .HyperMD-codeblock .token.attr-name,
.one-dark.theme-dark .HyperMD-codeblock .token.class-name,
.one-dark.theme-dark .HyperMD-codeblock .token.boolean,
.one-dark.theme-dark .HyperMD-codeblock .token.constant,
.one-dark.theme-dark .HyperMD-codeblock .token.number,
.one-dark.theme-dark .HyperMD-codeblock .token.atrule {
  color: hsl(29deg, 54%, 61%);
}
.one-dark.theme-dark p > code .cm-keyword,
.one-dark.theme-dark p > code .token.keyword,
.one-dark.theme-dark pre:not(.frontmatter) .cm-keyword,
.one-dark.theme-dark pre:not(.frontmatter) .token.keyword,
.one-dark.theme-dark .HyperMD-codeblock .cm-keyword,
.one-dark.theme-dark .HyperMD-codeblock .token.keyword {
  color: hsl(286deg, 60%, 67%);
}
.one-dark.theme-dark p > code .cm-qualifier,
.one-dark.theme-dark p > code .cm-tag,
.one-dark.theme-dark p > code .cm-property,
.one-dark.theme-dark p > code .token.property,
.one-dark.theme-dark p > code .token.tag,
.one-dark.theme-dark p > code .token.symbol,
.one-dark.theme-dark p > code .token.deleted,
.one-dark.theme-dark p > code .token.important,
.one-dark.theme-dark pre:not(.frontmatter) .cm-qualifier,
.one-dark.theme-dark pre:not(.frontmatter) .cm-tag,
.one-dark.theme-dark pre:not(.frontmatter) .cm-property,
.one-dark.theme-dark pre:not(.frontmatter) .token.property,
.one-dark.theme-dark pre:not(.frontmatter) .token.tag,
.one-dark.theme-dark pre:not(.frontmatter) .token.symbol,
.one-dark.theme-dark pre:not(.frontmatter) .token.deleted,
.one-dark.theme-dark pre:not(.frontmatter) .token.important,
.one-dark.theme-dark .HyperMD-codeblock .cm-qualifier,
.one-dark.theme-dark .HyperMD-codeblock .cm-tag,
.one-dark.theme-dark .HyperMD-codeblock .cm-property,
.one-dark.theme-dark .HyperMD-codeblock .token.property,
.one-dark.theme-dark .HyperMD-codeblock .token.tag,
.one-dark.theme-dark .HyperMD-codeblock .token.symbol,
.one-dark.theme-dark .HyperMD-codeblock .token.deleted,
.one-dark.theme-dark .HyperMD-codeblock .token.important {
  color: hsl(355deg, 65%, 65%);
}
.one-dark.theme-dark p > code .cm-string,
.one-dark.theme-dark p > code .cm-string-2,
.one-dark.theme-dark p > code .cm-builtin,
.one-dark.theme-dark p > code .token.selector,
.one-dark.theme-dark p > code .token.string,
.one-dark.theme-dark p > code .token.char,
.one-dark.theme-dark p > code .token.builtin,
.one-dark.theme-dark p > code .token.inserted,
.one-dark.theme-dark p > code .token.regex,
.one-dark.theme-dark p > code .token.attr-value,
.one-dark.theme-dark p > code .token.attr-value > .token.punctuation,
.one-dark.theme-dark pre:not(.frontmatter) .cm-string,
.one-dark.theme-dark pre:not(.frontmatter) .cm-string-2,
.one-dark.theme-dark pre:not(.frontmatter) .cm-builtin,
.one-dark.theme-dark pre:not(.frontmatter) .token.selector,
.one-dark.theme-dark pre:not(.frontmatter) .token.string,
.one-dark.theme-dark pre:not(.frontmatter) .token.char,
.one-dark.theme-dark pre:not(.frontmatter) .token.builtin,
.one-dark.theme-dark pre:not(.frontmatter) .token.inserted,
.one-dark.theme-dark pre:not(.frontmatter) .token.regex,
.one-dark.theme-dark pre:not(.frontmatter) .token.attr-value,
.one-dark.theme-dark pre:not(.frontmatter) .token.attr-value > .token.punctuation,
.one-dark.theme-dark .HyperMD-codeblock .cm-string,
.one-dark.theme-dark .HyperMD-codeblock .cm-string-2,
.one-dark.theme-dark .HyperMD-codeblock .cm-builtin,
.one-dark.theme-dark .HyperMD-codeblock .token.selector,
.one-dark.theme-dark .HyperMD-codeblock .token.string,
.one-dark.theme-dark .HyperMD-codeblock .token.char,
.one-dark.theme-dark .HyperMD-codeblock .token.builtin,
.one-dark.theme-dark .HyperMD-codeblock .token.inserted,
.one-dark.theme-dark .HyperMD-codeblock .token.regex,
.one-dark.theme-dark .HyperMD-codeblock .token.attr-value,
.one-dark.theme-dark .HyperMD-codeblock .token.attr-value > .token.punctuation {
  color: hsl(95deg, 38%, 62%);
}
.one-dark.theme-dark p > code .cm-variable,
.one-dark.theme-dark p > code .cm-variable-2,
.one-dark.theme-dark p > code .cm-variable-3,
.one-dark.theme-dark p > code .cm-operator,
.one-dark.theme-dark p > code .token.variable,
.one-dark.theme-dark p > code .token.operator,
.one-dark.theme-dark p > code .token.function,
.one-dark.theme-dark pre:not(.frontmatter) .cm-variable,
.one-dark.theme-dark pre:not(.frontmatter) .cm-variable-2,
.one-dark.theme-dark pre:not(.frontmatter) .cm-variable-3,
.one-dark.theme-dark pre:not(.frontmatter) .cm-operator,
.one-dark.theme-dark pre:not(.frontmatter) .token.variable,
.one-dark.theme-dark pre:not(.frontmatter) .token.operator,
.one-dark.theme-dark pre:not(.frontmatter) .token.function,
.one-dark.theme-dark .HyperMD-codeblock .cm-variable,
.one-dark.theme-dark .HyperMD-codeblock .cm-variable-2,
.one-dark.theme-dark .HyperMD-codeblock .cm-variable-3,
.one-dark.theme-dark .HyperMD-codeblock .cm-operator,
.one-dark.theme-dark .HyperMD-codeblock .token.variable,
.one-dark.theme-dark .HyperMD-codeblock .token.operator,
.one-dark.theme-dark .HyperMD-codeblock .token.function {
  color: hsl(207deg, 82%, 66%);
}
.one-dark.theme-dark p > code .cm-link,
.one-dark.theme-dark p > code .token.url,
.one-dark.theme-dark pre:not(.frontmatter) .cm-link,
.one-dark.theme-dark pre:not(.frontmatter) .token.url,
.one-dark.theme-dark .HyperMD-codeblock .cm-link,
.one-dark.theme-dark .HyperMD-codeblock .token.url {
  color: hsl(187deg, 47%, 55%);
}
.one-dark.theme-dark p > code .token.attr-value > .token.punctuation.attr-equals,
.one-dark.theme-dark p > code .token.special-attr > .token.attr-value > .token.value.css,
.one-dark.theme-dark pre:not(.frontmatter) .token.attr-value > .token.punctuation.attr-equals,
.one-dark.theme-dark pre:not(.frontmatter) .token.special-attr > .token.attr-value > .token.value.css,
.one-dark.theme-dark .HyperMD-codeblock .token.attr-value > .token.punctuation.attr-equals,
.one-dark.theme-dark .HyperMD-codeblock .token.special-attr > .token.attr-value > .token.value.css {
  color: hsl(220deg, 14%, 71%);
}
.one-dark.theme-dark p > code .language-css .token.selector,
.one-dark.theme-dark pre:not(.frontmatter) .language-css .token.selector,
.one-dark.theme-dark .HyperMD-codeblock .language-css .token.selector {
  color: hsl(355deg, 65%, 65%);
}
.one-dark.theme-dark p > code .language-css .token.property,
.one-dark.theme-dark pre:not(.frontmatter) .language-css .token.property,
.one-dark.theme-dark .HyperMD-codeblock .language-css .token.property {
  color: hsl(220deg, 14%, 71%);
}
.one-dark.theme-dark p > code .language-css .token.function,
.one-dark.theme-dark p > code .language-css .token.url > .token.function,
.one-dark.theme-dark pre:not(.frontmatter) .language-css .token.function,
.one-dark.theme-dark pre:not(.frontmatter) .language-css .token.url > .token.function,
.one-dark.theme-dark .HyperMD-codeblock .language-css .token.function,
.one-dark.theme-dark .HyperMD-codeblock .language-css .token.url > .token.function {
  color: hsl(187deg, 47%, 55%);
}
.one-dark.theme-dark p > code .language-css .token.url > .token.string.url,
.one-dark.theme-dark pre:not(.frontmatter) .language-css .token.url > .token.string.url,
.one-dark.theme-dark .HyperMD-codeblock .language-css .token.url > .token.string.url {
  color: hsl(95deg, 38%, 62%);
}
.one-dark.theme-dark p > code .language-css .token.important,
.one-dark.theme-dark p > code .language-css .token.atrule .token.rule,
.one-dark.theme-dark pre:not(.frontmatter) .language-css .token.important,
.one-dark.theme-dark pre:not(.frontmatter) .language-css .token.atrule .token.rule,
.one-dark.theme-dark .HyperMD-codeblock .language-css .token.important,
.one-dark.theme-dark .HyperMD-codeblock .language-css .token.atrule .token.rule {
  color: hsl(286deg, 60%, 67%);
}
.one-dark.theme-dark p > code .language-javascript .token.operator,
.one-dark.theme-dark pre:not(.frontmatter) .language-javascript .token.operator,
.one-dark.theme-dark .HyperMD-codeblock .language-javascript .token.operator {
  color: hsl(286deg, 60%, 67%);
}
.one-dark.theme-dark p > code .language-javascript .token.template-string > .token.interpolation > .token.interpolation-punctuation.punctuation,
.one-dark.theme-dark pre:not(.frontmatter) .language-javascript .token.template-string > .token.interpolation > .token.interpolation-punctuation.punctuation,
.one-dark.theme-dark .HyperMD-codeblock .language-javascript .token.template-string > .token.interpolation > .token.interpolation-punctuation.punctuation {
  color: hsl(5deg, 48%, 51%);
}
.one-dark.theme-dark p > code .language-json .token.operator,
.one-dark.theme-dark pre:not(.frontmatter) .language-json .token.operator,
.one-dark.theme-dark .HyperMD-codeblock .language-json .token.operator {
  color: hsl(220deg, 14%, 71%);
}
.one-dark.theme-dark p > code .language-json .token.null.keyword,
.one-dark.theme-dark pre:not(.frontmatter) .language-json .token.null.keyword,
.one-dark.theme-dark .HyperMD-codeblock .language-json .token.null.keyword {
  color: hsl(29deg, 54%, 61%);
}
.one-dark.theme-dark p > code .language-markdown .token.url,
.one-dark.theme-dark p > code .language-markdown .token.url > .token.operator,
.one-dark.theme-dark p > code .language-markdown .token.url-reference.url > .token.string,
.one-dark.theme-dark pre:not(.frontmatter) .language-markdown .token.url,
.one-dark.theme-dark pre:not(.frontmatter) .language-markdown .token.url > .token.operator,
.one-dark.theme-dark pre:not(.frontmatter) .language-markdown .token.url-reference.url > .token.string,
.one-dark.theme-dark .HyperMD-codeblock .language-markdown .token.url,
.one-dark.theme-dark .HyperMD-codeblock .language-markdown .token.url > .token.operator,
.one-dark.theme-dark .HyperMD-codeblock .language-markdown .token.url-reference.url > .token.string {
  color: hsl(220deg, 14%, 71%);
}
.one-dark.theme-dark p > code .language-markdown .token.url > .token.content,
.one-dark.theme-dark pre:not(.frontmatter) .language-markdown .token.url > .token.content,
.one-dark.theme-dark .HyperMD-codeblock .language-markdown .token.url > .token.content {
  color: hsl(207deg, 82%, 66%);
}
.one-dark.theme-dark p > code .language-markdown .token.url > .token.url,
.one-dark.theme-dark p > code .language-markdown .token.url-reference.url,
.one-dark.theme-dark pre:not(.frontmatter) .language-markdown .token.url > .token.url,
.one-dark.theme-dark pre:not(.frontmatter) .language-markdown .token.url-reference.url,
.one-dark.theme-dark .HyperMD-codeblock .language-markdown .token.url > .token.url,
.one-dark.theme-dark .HyperMD-codeblock .language-markdown .token.url-reference.url {
  color: hsl(187deg, 47%, 55%);
}
.one-dark.theme-dark p > code .language-markdown .token.blockquote.punctuation,
.one-dark.theme-dark p > code .language-markdown .token.hr.punctuation,
.one-dark.theme-dark pre:not(.frontmatter) .language-markdown .token.blockquote.punctuation,
.one-dark.theme-dark pre:not(.frontmatter) .language-markdown .token.hr.punctuation,
.one-dark.theme-dark .HyperMD-codeblock .language-markdown .token.blockquote.punctuation,
.one-dark.theme-dark .HyperMD-codeblock .language-markdown .token.hr.punctuation {
  color: hsl(220deg, 10%, 40%);
  font-style: italic;
}
.one-dark.theme-dark p > code .language-markdown .token.code-snippet,
.one-dark.theme-dark pre:not(.frontmatter) .language-markdown .token.code-snippet,
.one-dark.theme-dark .HyperMD-codeblock .language-markdown .token.code-snippet {
  color: hsl(95deg, 38%, 62%);
}
.one-dark.theme-dark p > code .language-markdown .token.bold .token.content,
.one-dark.theme-dark pre:not(.frontmatter) .language-markdown .token.bold .token.content,
.one-dark.theme-dark .HyperMD-codeblock .language-markdown .token.bold .token.content {
  color: hsl(29deg, 54%, 61%);
}
.one-dark.theme-dark p > code .language-markdown .token.italic .token.content,
.one-dark.theme-dark pre:not(.frontmatter) .language-markdown .token.italic .token.content,
.one-dark.theme-dark .HyperMD-codeblock .language-markdown .token.italic .token.content {
  color: hsl(286deg, 60%, 67%);
}
.one-dark.theme-dark p > code .language-markdown .token.strike .token.content,
.one-dark.theme-dark p > code .language-markdown .token.strike .token.punctuation,
.one-dark.theme-dark p > code .language-markdown .token.list.punctuation,
.one-dark.theme-dark p > code .language-markdown .token.title.important > .token.punctuation,
.one-dark.theme-dark pre:not(.frontmatter) .language-markdown .token.strike .token.content,
.one-dark.theme-dark pre:not(.frontmatter) .language-markdown .token.strike .token.punctuation,
.one-dark.theme-dark pre:not(.frontmatter) .language-markdown .token.list.punctuation,
.one-dark.theme-dark pre:not(.frontmatter) .language-markdown .token.title.important > .token.punctuation,
.one-dark.theme-dark .HyperMD-codeblock .language-markdown .token.strike .token.content,
.one-dark.theme-dark .HyperMD-codeblock .language-markdown .token.strike .token.punctuation,
.one-dark.theme-dark .HyperMD-codeblock .language-markdown .token.list.punctuation,
.one-dark.theme-dark .HyperMD-codeblock .language-markdown .token.title.important > .token.punctuation {
  color: hsl(355deg, 65%, 65%);
}
.one-dark.theme-dark p > code .token.bold,
.one-dark.theme-dark pre:not(.frontmatter) .token.bold,
.one-dark.theme-dark .HyperMD-codeblock .token.bold {
  font-weight: bold;
}
.one-dark.theme-dark p > code .token.comment,
.one-dark.theme-dark p > code .token.italic,
.one-dark.theme-dark pre:not(.frontmatter) .token.comment,
.one-dark.theme-dark pre:not(.frontmatter) .token.italic,
.one-dark.theme-dark .HyperMD-codeblock .token.comment,
.one-dark.theme-dark .HyperMD-codeblock .token.italic {
  font-style: italic;
}
.one-dark.theme-dark p > code .token.entity,
.one-dark.theme-dark pre:not(.frontmatter) .token.entity,
.one-dark.theme-dark .HyperMD-codeblock .token.entity {
  cursor: help;
}
.one-dark.theme-dark p > code .token.namespace,
.one-dark.theme-dark pre:not(.frontmatter) .token.namespace,
.one-dark.theme-dark .HyperMD-codeblock .token.namespace {
  opacity: 0.8;
}
.solarized.theme-light p > code,
.solarized.theme-light pre:not(.frontmatter),
.solarized.theme-light .HyperMD-codeblock {
  --code-background: #eee8d5;
  --code-normal: #657b83;
  --text-muted: #586e75;
  --text-faint: #93a1a1;
}
.solarized.theme-light p > code .cm-hr,
.solarized.theme-light p > code .cm-comment,
.solarized.theme-light p > code .cm-meta,
.solarized.theme-light p > code .cm-type,
.solarized.theme-light p > code .token.comment,
.solarized.theme-light p > code .token.prolog,
.solarized.theme-light p > code .token.doctype,
.solarized.theme-light p > code .token.cdata,
.solarized.theme-light pre:not(.frontmatter) .cm-hr,
.solarized.theme-light pre:not(.frontmatter) .cm-comment,
.solarized.theme-light pre:not(.frontmatter) .cm-meta,
.solarized.theme-light pre:not(.frontmatter) .cm-type,
.solarized.theme-light pre:not(.frontmatter) .token.comment,
.solarized.theme-light pre:not(.frontmatter) .token.prolog,
.solarized.theme-light pre:not(.frontmatter) .token.doctype,
.solarized.theme-light pre:not(.frontmatter) .token.cdata,
.solarized.theme-light .HyperMD-codeblock .cm-hr,
.solarized.theme-light .HyperMD-codeblock .cm-comment,
.solarized.theme-light .HyperMD-codeblock .cm-meta,
.solarized.theme-light .HyperMD-codeblock .cm-type,
.solarized.theme-light .HyperMD-codeblock .token.comment,
.solarized.theme-light .HyperMD-codeblock .token.prolog,
.solarized.theme-light .HyperMD-codeblock .token.doctype,
.solarized.theme-light .HyperMD-codeblock .token.cdata {
  color: #93a1a1;
}
.solarized.theme-light p > code .cm-operator,
.solarized.theme-light p > code .cm-punctuation,
.solarized.theme-light p > code .cm-bracket.cm-tag,
.solarized.theme-light p > code .token.punctuation,
.solarized.theme-light pre:not(.frontmatter) .cm-operator,
.solarized.theme-light pre:not(.frontmatter) .cm-punctuation,
.solarized.theme-light pre:not(.frontmatter) .cm-bracket.cm-tag,
.solarized.theme-light pre:not(.frontmatter) .token.punctuation,
.solarized.theme-light .HyperMD-codeblock .cm-operator,
.solarized.theme-light .HyperMD-codeblock .cm-punctuation,
.solarized.theme-light .HyperMD-codeblock .cm-bracket.cm-tag,
.solarized.theme-light .HyperMD-codeblock .token.punctuation {
  color: #586e75;
}
.solarized.theme-light p > code .token.namespace,
.solarized.theme-light pre:not(.frontmatter) .token.namespace,
.solarized.theme-light .HyperMD-codeblock .token.namespace {
  opacity: 0.7;
}
.solarized.theme-light p > code .cm-number,
.solarized.theme-light p > code .cm-tag,
.solarized.theme-light p > code .cm-property,
.solarized.theme-light p > code .token.property,
.solarized.theme-light p > code .token.tag,
.solarized.theme-light p > code .token.boolean,
.solarized.theme-light p > code .token.number,
.solarized.theme-light p > code .token.constant,
.solarized.theme-light p > code .token.symbol,
.solarized.theme-light p > code .token.deleted,
.solarized.theme-light pre:not(.frontmatter) .cm-number,
.solarized.theme-light pre:not(.frontmatter) .cm-tag,
.solarized.theme-light pre:not(.frontmatter) .cm-property,
.solarized.theme-light pre:not(.frontmatter) .token.property,
.solarized.theme-light pre:not(.frontmatter) .token.tag,
.solarized.theme-light pre:not(.frontmatter) .token.boolean,
.solarized.theme-light pre:not(.frontmatter) .token.number,
.solarized.theme-light pre:not(.frontmatter) .token.constant,
.solarized.theme-light pre:not(.frontmatter) .token.symbol,
.solarized.theme-light pre:not(.frontmatter) .token.deleted,
.solarized.theme-light .HyperMD-codeblock .cm-number,
.solarized.theme-light .HyperMD-codeblock .cm-tag,
.solarized.theme-light .HyperMD-codeblock .cm-property,
.solarized.theme-light .HyperMD-codeblock .token.property,
.solarized.theme-light .HyperMD-codeblock .token.tag,
.solarized.theme-light .HyperMD-codeblock .token.boolean,
.solarized.theme-light .HyperMD-codeblock .token.number,
.solarized.theme-light .HyperMD-codeblock .token.constant,
.solarized.theme-light .HyperMD-codeblock .token.symbol,
.solarized.theme-light .HyperMD-codeblock .token.deleted {
  color: #268bd2;
}
.solarized.theme-light p > code .cm-string-2,
.solarized.theme-light p > code .cm-link,
.solarized.theme-light p > code .cm-builtin,
.solarized.theme-light p > code .cm-attribute,
.solarized.theme-light p > code .token.selector,
.solarized.theme-light p > code .token.attr-name,
.solarized.theme-light p > code .token.string,
.solarized.theme-light p > code .token.char,
.solarized.theme-light p > code .token.builtin,
.solarized.theme-light p > code .token.url,
.solarized.theme-light p > code .token.inserted,
.solarized.theme-light pre:not(.frontmatter) .cm-string-2,
.solarized.theme-light pre:not(.frontmatter) .cm-link,
.solarized.theme-light pre:not(.frontmatter) .cm-builtin,
.solarized.theme-light pre:not(.frontmatter) .cm-attribute,
.solarized.theme-light pre:not(.frontmatter) .token.selector,
.solarized.theme-light pre:not(.frontmatter) .token.attr-name,
.solarized.theme-light pre:not(.frontmatter) .token.string,
.solarized.theme-light pre:not(.frontmatter) .token.char,
.solarized.theme-light pre:not(.frontmatter) .token.builtin,
.solarized.theme-light pre:not(.frontmatter) .token.url,
.solarized.theme-light pre:not(.frontmatter) .token.inserted,
.solarized.theme-light .HyperMD-codeblock .cm-string-2,
.solarized.theme-light .HyperMD-codeblock .cm-link,
.solarized.theme-light .HyperMD-codeblock .cm-builtin,
.solarized.theme-light .HyperMD-codeblock .cm-attribute,
.solarized.theme-light .HyperMD-codeblock .token.selector,
.solarized.theme-light .HyperMD-codeblock .token.attr-name,
.solarized.theme-light .HyperMD-codeblock .token.string,
.solarized.theme-light .HyperMD-codeblock .token.char,
.solarized.theme-light .HyperMD-codeblock .token.builtin,
.solarized.theme-light .HyperMD-codeblock .token.url,
.solarized.theme-light .HyperMD-codeblock .token.inserted {
  color: #2aa198;
}
.solarized.theme-light p > code .token.entity,
.solarized.theme-light pre:not(.frontmatter) .token.entity,
.solarized.theme-light .HyperMD-codeblock .token.entity {
  color: #657b83;
  background: #eee8d5;
}
.solarized.theme-light p > code .cm-string,
.solarized.theme-light p > code .cm-keyword,
.solarized.theme-light p > code .token.atrule,
.solarized.theme-light p > code .token.attr-value,
.solarized.theme-light p > code .token.keyword,
.solarized.theme-light pre:not(.frontmatter) .cm-string,
.solarized.theme-light pre:not(.frontmatter) .cm-keyword,
.solarized.theme-light pre:not(.frontmatter) .token.atrule,
.solarized.theme-light pre:not(.frontmatter) .token.attr-value,
.solarized.theme-light pre:not(.frontmatter) .token.keyword,
.solarized.theme-light .HyperMD-codeblock .cm-string,
.solarized.theme-light .HyperMD-codeblock .cm-keyword,
.solarized.theme-light .HyperMD-codeblock .token.atrule,
.solarized.theme-light .HyperMD-codeblock .token.attr-value,
.solarized.theme-light .HyperMD-codeblock .token.keyword {
  color: #859900;
}
.solarized.theme-light p > code .cm-qualifier,
.solarized.theme-light p > code .token.function,
.solarized.theme-light p > code .token.class-name,
.solarized.theme-light pre:not(.frontmatter) .cm-qualifier,
.solarized.theme-light pre:not(.frontmatter) .token.function,
.solarized.theme-light pre:not(.frontmatter) .token.class-name,
.solarized.theme-light .HyperMD-codeblock .cm-qualifier,
.solarized.theme-light .HyperMD-codeblock .token.function,
.solarized.theme-light .HyperMD-codeblock .token.class-name {
  color: #b58900;
}
.solarized.theme-light p > code .cm-variable,
.solarized.theme-light p > code .cm-variable-2,
.solarized.theme-light p > code .cm-variable-3,
.solarized.theme-light p > code .token.regex,
.solarized.theme-light p > code .token.important,
.solarized.theme-light p > code .token.variable,
.solarized.theme-light pre:not(.frontmatter) .cm-variable,
.solarized.theme-light pre:not(.frontmatter) .cm-variable-2,
.solarized.theme-light pre:not(.frontmatter) .cm-variable-3,
.solarized.theme-light pre:not(.frontmatter) .token.regex,
.solarized.theme-light pre:not(.frontmatter) .token.important,
.solarized.theme-light pre:not(.frontmatter) .token.variable,
.solarized.theme-light .HyperMD-codeblock .cm-variable,
.solarized.theme-light .HyperMD-codeblock .cm-variable-2,
.solarized.theme-light .HyperMD-codeblock .cm-variable-3,
.solarized.theme-light .HyperMD-codeblock .token.regex,
.solarized.theme-light .HyperMD-codeblock .token.important,
.solarized.theme-light .HyperMD-codeblock .token.variable {
  color: #cb4b16;
}
.solarized.theme-light p > code .token.important,
.solarized.theme-light p > code .token.bold,
.solarized.theme-light pre:not(.frontmatter) .token.important,
.solarized.theme-light pre:not(.frontmatter) .token.bold,
.solarized.theme-light .HyperMD-codeblock .token.important,
.solarized.theme-light .HyperMD-codeblock .token.bold {
  font-weight: bold;
}
.solarized.theme-light p > code .token.italic,
.solarized.theme-light pre:not(.frontmatter) .token.italic,
.solarized.theme-light .HyperMD-codeblock .token.italic {
  font-style: italic;
}
.solarized.theme-dark p > code,
.solarized.theme-dark pre:not(.frontmatter),
.solarized.theme-dark .HyperMD-codeblock {
  --code-background: #002b36;
  --code-normal: #839496;
  --text-muted:#93a1a1;
  --text-faint:#586e75;
}
.solarized.theme-dark p > code .cm-hr,
.solarized.theme-dark p > code .cm-comment,
.solarized.theme-dark p > code .cm-meta,
.solarized.theme-dark p > code .cm-type,
.solarized.theme-dark p > code .token.comment,
.solarized.theme-dark p > code .token.prolog,
.solarized.theme-dark p > code .token.doctype,
.solarized.theme-dark p > code .token.cdata,
.solarized.theme-dark pre:not(.frontmatter) .cm-hr,
.solarized.theme-dark pre:not(.frontmatter) .cm-comment,
.solarized.theme-dark pre:not(.frontmatter) .cm-meta,
.solarized.theme-dark pre:not(.frontmatter) .cm-type,
.solarized.theme-dark pre:not(.frontmatter) .token.comment,
.solarized.theme-dark pre:not(.frontmatter) .token.prolog,
.solarized.theme-dark pre:not(.frontmatter) .token.doctype,
.solarized.theme-dark pre:not(.frontmatter) .token.cdata,
.solarized.theme-dark .HyperMD-codeblock .cm-hr,
.solarized.theme-dark .HyperMD-codeblock .cm-comment,
.solarized.theme-dark .HyperMD-codeblock .cm-meta,
.solarized.theme-dark .HyperMD-codeblock .cm-type,
.solarized.theme-dark .HyperMD-codeblock .token.comment,
.solarized.theme-dark .HyperMD-codeblock .token.prolog,
.solarized.theme-dark .HyperMD-codeblock .token.doctype,
.solarized.theme-dark .HyperMD-codeblock .token.cdata {
  color: #586e75;
}
.solarized.theme-dark p > code .cm-operator,
.solarized.theme-dark p > code .cm-punctuation,
.solarized.theme-dark p > code .cm-bracket.cm-tag,
.solarized.theme-dark p > code .token.punctuation,
.solarized.theme-dark pre:not(.frontmatter) .cm-operator,
.solarized.theme-dark pre:not(.frontmatter) .cm-punctuation,
.solarized.theme-dark pre:not(.frontmatter) .cm-bracket.cm-tag,
.solarized.theme-dark pre:not(.frontmatter) .token.punctuation,
.solarized.theme-dark .HyperMD-codeblock .cm-operator,
.solarized.theme-dark .HyperMD-codeblock .cm-punctuation,
.solarized.theme-dark .HyperMD-codeblock .cm-bracket.cm-tag,
.solarized.theme-dark .HyperMD-codeblock .token.punctuation {
  color: #93a1a1;
}
.solarized.theme-dark p > code .namespace,
.solarized.theme-dark pre:not(.frontmatter) .namespace,
.solarized.theme-dark .HyperMD-codeblock .namespace {
  opacity: 0.7;
}
.solarized.theme-dark p > code .cm-keyword,
.solarized.theme-dark p > code .cm-tag,
.solarized.theme-dark p > code .cm-property,
.solarized.theme-dark p > code .token.property,
.solarized.theme-dark p > code .token.keyword,
.solarized.theme-dark p > code .token.tag,
.solarized.theme-dark pre:not(.frontmatter) .cm-keyword,
.solarized.theme-dark pre:not(.frontmatter) .cm-tag,
.solarized.theme-dark pre:not(.frontmatter) .cm-property,
.solarized.theme-dark pre:not(.frontmatter) .token.property,
.solarized.theme-dark pre:not(.frontmatter) .token.keyword,
.solarized.theme-dark pre:not(.frontmatter) .token.tag,
.solarized.theme-dark .HyperMD-codeblock .cm-keyword,
.solarized.theme-dark .HyperMD-codeblock .cm-tag,
.solarized.theme-dark .HyperMD-codeblock .cm-property,
.solarized.theme-dark .HyperMD-codeblock .token.property,
.solarized.theme-dark .HyperMD-codeblock .token.keyword,
.solarized.theme-dark .HyperMD-codeblock .token.tag {
  color: #268bd2;
}
.solarized.theme-dark p > code .token.class-name,
.solarized.theme-dark pre:not(.frontmatter) .token.class-name,
.solarized.theme-dark .HyperMD-codeblock .token.class-name {
  color: #FFFFB6;
  text-decoration: underline;
}
.solarized.theme-dark p > code .token.boolean,
.solarized.theme-dark p > code .token.constant,
.solarized.theme-dark pre:not(.frontmatter) .token.boolean,
.solarized.theme-dark pre:not(.frontmatter) .token.constant,
.solarized.theme-dark .HyperMD-codeblock .token.boolean,
.solarized.theme-dark .HyperMD-codeblock .token.constant {
  color: #b58900;
}
.solarized.theme-dark p > code .token.symbol,
.solarized.theme-dark p > code .token.deleted,
.solarized.theme-dark pre:not(.frontmatter) .token.symbol,
.solarized.theme-dark pre:not(.frontmatter) .token.deleted,
.solarized.theme-dark .HyperMD-codeblock .token.symbol,
.solarized.theme-dark .HyperMD-codeblock .token.deleted {
  color: #dc322f;
}
.solarized.theme-dark p > code .cm-number,
.solarized.theme-dark p > code .token.number,
.solarized.theme-dark pre:not(.frontmatter) .cm-number,
.solarized.theme-dark pre:not(.frontmatter) .token.number,
.solarized.theme-dark .HyperMD-codeblock .cm-number,
.solarized.theme-dark .HyperMD-codeblock .token.number {
  color: #859900;
}
.solarized.theme-dark p > code .cm-string-2,
.solarized.theme-dark p > code .cm-link,
.solarized.theme-dark p > code .cm-builtin,
.solarized.theme-dark p > code .cm-attribute,
.solarized.theme-dark p > code .token.selector,
.solarized.theme-dark p > code .token.attr-name,
.solarized.theme-dark p > code .token.string,
.solarized.theme-dark p > code .token.char,
.solarized.theme-dark p > code .token.builtin,
.solarized.theme-dark p > code .token.inserted,
.solarized.theme-dark pre:not(.frontmatter) .cm-string-2,
.solarized.theme-dark pre:not(.frontmatter) .cm-link,
.solarized.theme-dark pre:not(.frontmatter) .cm-builtin,
.solarized.theme-dark pre:not(.frontmatter) .cm-attribute,
.solarized.theme-dark pre:not(.frontmatter) .token.selector,
.solarized.theme-dark pre:not(.frontmatter) .token.attr-name,
.solarized.theme-dark pre:not(.frontmatter) .token.string,
.solarized.theme-dark pre:not(.frontmatter) .token.char,
.solarized.theme-dark pre:not(.frontmatter) .token.builtin,
.solarized.theme-dark pre:not(.frontmatter) .token.inserted,
.solarized.theme-dark .HyperMD-codeblock .cm-string-2,
.solarized.theme-dark .HyperMD-codeblock .cm-link,
.solarized.theme-dark .HyperMD-codeblock .cm-builtin,
.solarized.theme-dark .HyperMD-codeblock .cm-attribute,
.solarized.theme-dark .HyperMD-codeblock .token.selector,
.solarized.theme-dark .HyperMD-codeblock .token.attr-name,
.solarized.theme-dark .HyperMD-codeblock .token.string,
.solarized.theme-dark .HyperMD-codeblock .token.char,
.solarized.theme-dark .HyperMD-codeblock .token.builtin,
.solarized.theme-dark .HyperMD-codeblock .token.inserted {
  color: #859900;
}
.solarized.theme-dark p > code .cm-variable,
.solarized.theme-dark p > code .cm-variable-2,
.solarized.theme-dark p > code .cm-variable-3,
.solarized.theme-dark p > code .token.variable,
.solarized.theme-dark pre:not(.frontmatter) .cm-variable,
.solarized.theme-dark pre:not(.frontmatter) .cm-variable-2,
.solarized.theme-dark pre:not(.frontmatter) .cm-variable-3,
.solarized.theme-dark pre:not(.frontmatter) .token.variable,
.solarized.theme-dark .HyperMD-codeblock .cm-variable,
.solarized.theme-dark .HyperMD-codeblock .cm-variable-2,
.solarized.theme-dark .HyperMD-codeblock .cm-variable-3,
.solarized.theme-dark .HyperMD-codeblock .token.variable {
  color: #268bd2;
}
.solarized.theme-dark p > code .cm-operator,
.solarized.theme-dark p > code .token.operator,
.solarized.theme-dark pre:not(.frontmatter) .cm-operator,
.solarized.theme-dark pre:not(.frontmatter) .token.operator,
.solarized.theme-dark .HyperMD-codeblock .cm-operator,
.solarized.theme-dark .HyperMD-codeblock .token.operator {
  color: #EDEDED;
}
.solarized.theme-dark p > code .cm-qualifier,
.solarized.theme-dark p > code .token.function,
.solarized.theme-dark pre:not(.frontmatter) .cm-qualifier,
.solarized.theme-dark pre:not(.frontmatter) .token.function,
.solarized.theme-dark .HyperMD-codeblock .cm-qualifier,
.solarized.theme-dark .HyperMD-codeblock .token.function {
  color: #268bd2;
}
.solarized.theme-dark p > code .token.regex,
.solarized.theme-dark pre:not(.frontmatter) .token.regex,
.solarized.theme-dark .HyperMD-codeblock .token.regex {
  color: #E9C062;
}
.solarized.theme-dark p > code .token.important,
.solarized.theme-dark pre:not(.frontmatter) .token.important,
.solarized.theme-dark .HyperMD-codeblock .token.important {
  color: #fd971f;
}
.solarized.theme-dark p > code .token.entity,
.solarized.theme-dark pre:not(.frontmatter) .token.entity,
.solarized.theme-dark .HyperMD-codeblock .token.entity {
  color: #FFFFB6;
  cursor: help;
}
.solarized.theme-dark p > code .token.url,
.solarized.theme-dark pre:not(.frontmatter) .token.url,
.solarized.theme-dark .HyperMD-codeblock .token.url {
  color: #96CBFE;
}
.solarized.theme-dark p > code .language-css .token.string,
.solarized.theme-dark p > code .style .token.string,
.solarized.theme-dark pre:not(.frontmatter) .language-css .token.string,
.solarized.theme-dark pre:not(.frontmatter) .style .token.string,
.solarized.theme-dark .HyperMD-codeblock .language-css .token.string,
.solarized.theme-dark .HyperMD-codeblock .style .token.string {
  color: #87C38A;
}
.solarized.theme-dark p > code .token.important,
.solarized.theme-dark p > code .token.bold,
.solarized.theme-dark pre:not(.frontmatter) .token.important,
.solarized.theme-dark pre:not(.frontmatter) .token.bold,
.solarized.theme-dark .HyperMD-codeblock .token.important,
.solarized.theme-dark .HyperMD-codeblock .token.bold {
  font-weight: bold;
}
.solarized.theme-dark p > code .token.italic,
.solarized.theme-dark pre:not(.frontmatter) .token.italic,
.solarized.theme-dark .HyperMD-codeblock .token.italic {
  font-style: italic;
}
.solarized.theme-dark p > code .cm-string,
.solarized.theme-dark p > code .token.atrule,
.solarized.theme-dark p > code .token.attr-value,
.solarized.theme-dark pre:not(.frontmatter) .cm-string,
.solarized.theme-dark pre:not(.frontmatter) .token.atrule,
.solarized.theme-dark pre:not(.frontmatter) .token.attr-value,
.solarized.theme-dark .HyperMD-codeblock .cm-string,
.solarized.theme-dark .HyperMD-codeblock .token.atrule,
.solarized.theme-dark .HyperMD-codeblock .token.attr-value {
  color: #F9EE98;
}
.gruvbox.theme-light p > code,
.gruvbox.theme-light pre:not(.frontmatter),
.gruvbox.theme-light .HyperMD-codeblock {
  --code-background: #fbf1c7;
  --code-normal: #3c3836;
  --text-muted: #7c6f64;
  --text-faint: #7c6f64;
}
.gruvbox.theme-light p > code .cm-hr,
.gruvbox.theme-light p > code .cm-comment,
.gruvbox.theme-light p > code .cm-meta,
.gruvbox.theme-light p > code .token.comment,
.gruvbox.theme-light p > code .token.prolog,
.gruvbox.theme-light p > code .token.cdata,
.gruvbox.theme-light pre:not(.frontmatter) .cm-hr,
.gruvbox.theme-light pre:not(.frontmatter) .cm-comment,
.gruvbox.theme-light pre:not(.frontmatter) .cm-meta,
.gruvbox.theme-light pre:not(.frontmatter) .token.comment,
.gruvbox.theme-light pre:not(.frontmatter) .token.prolog,
.gruvbox.theme-light pre:not(.frontmatter) .token.cdata,
.gruvbox.theme-light .HyperMD-codeblock .cm-hr,
.gruvbox.theme-light .HyperMD-codeblock .cm-comment,
.gruvbox.theme-light .HyperMD-codeblock .cm-meta,
.gruvbox.theme-light .HyperMD-codeblock .token.comment,
.gruvbox.theme-light .HyperMD-codeblock .token.prolog,
.gruvbox.theme-light .HyperMD-codeblock .token.cdata {
  color: #7c6f64;
}
.gruvbox.theme-light p > code .cm-keyword,
.gruvbox.theme-light p > code .token.delimiter,
.gruvbox.theme-light p > code .token.boolean,
.gruvbox.theme-light p > code .token.keyword,
.gruvbox.theme-light p > code .token.selector,
.gruvbox.theme-light p > code .token.important,
.gruvbox.theme-light p > code .token.atrule,
.gruvbox.theme-light pre:not(.frontmatter) .cm-keyword,
.gruvbox.theme-light pre:not(.frontmatter) .token.delimiter,
.gruvbox.theme-light pre:not(.frontmatter) .token.boolean,
.gruvbox.theme-light pre:not(.frontmatter) .token.keyword,
.gruvbox.theme-light pre:not(.frontmatter) .token.selector,
.gruvbox.theme-light pre:not(.frontmatter) .token.important,
.gruvbox.theme-light pre:not(.frontmatter) .token.atrule,
.gruvbox.theme-light .HyperMD-codeblock .cm-keyword,
.gruvbox.theme-light .HyperMD-codeblock .token.delimiter,
.gruvbox.theme-light .HyperMD-codeblock .token.boolean,
.gruvbox.theme-light .HyperMD-codeblock .token.keyword,
.gruvbox.theme-light .HyperMD-codeblock .token.selector,
.gruvbox.theme-light .HyperMD-codeblock .token.important,
.gruvbox.theme-light .HyperMD-codeblock .token.atrule {
  color: #9d0006;
}
.gruvbox.theme-light p > code .cm-attribute,
.gruvbox.theme-light p > code .cm-operator,
.gruvbox.theme-light p > code .cm-punctuation,
.gruvbox.theme-light p > code .cm-bracket.cm-tag,
.gruvbox.theme-light p > code .token.operator,
.gruvbox.theme-light p > code .token.punctuation,
.gruvbox.theme-light p > code .token.attr-name,
.gruvbox.theme-light pre:not(.frontmatter) .cm-attribute,
.gruvbox.theme-light pre:not(.frontmatter) .cm-operator,
.gruvbox.theme-light pre:not(.frontmatter) .cm-punctuation,
.gruvbox.theme-light pre:not(.frontmatter) .cm-bracket.cm-tag,
.gruvbox.theme-light pre:not(.frontmatter) .token.operator,
.gruvbox.theme-light pre:not(.frontmatter) .token.punctuation,
.gruvbox.theme-light pre:not(.frontmatter) .token.attr-name,
.gruvbox.theme-light .HyperMD-codeblock .cm-attribute,
.gruvbox.theme-light .HyperMD-codeblock .cm-operator,
.gruvbox.theme-light .HyperMD-codeblock .cm-punctuation,
.gruvbox.theme-light .HyperMD-codeblock .cm-bracket.cm-tag,
.gruvbox.theme-light .HyperMD-codeblock .token.operator,
.gruvbox.theme-light .HyperMD-codeblock .token.punctuation,
.gruvbox.theme-light .HyperMD-codeblock .token.attr-name {
  color: #7c6f64;
}
.gruvbox.theme-light p > code .cm-builtin,
.gruvbox.theme-light p > code .cm-type,
.gruvbox.theme-light p > code .cm-tag,
.gruvbox.theme-light p > code .token.tag,
.gruvbox.theme-light p > code .token.tag .punctuation,
.gruvbox.theme-light p > code .token.doctype,
.gruvbox.theme-light p > code .token.builtin,
.gruvbox.theme-light pre:not(.frontmatter) .cm-builtin,
.gruvbox.theme-light pre:not(.frontmatter) .cm-type,
.gruvbox.theme-light pre:not(.frontmatter) .cm-tag,
.gruvbox.theme-light pre:not(.frontmatter) .token.tag,
.gruvbox.theme-light pre:not(.frontmatter) .token.tag .punctuation,
.gruvbox.theme-light pre:not(.frontmatter) .token.doctype,
.gruvbox.theme-light pre:not(.frontmatter) .token.builtin,
.gruvbox.theme-light .HyperMD-codeblock .cm-builtin,
.gruvbox.theme-light .HyperMD-codeblock .cm-type,
.gruvbox.theme-light .HyperMD-codeblock .cm-tag,
.gruvbox.theme-light .HyperMD-codeblock .token.tag,
.gruvbox.theme-light .HyperMD-codeblock .token.tag .punctuation,
.gruvbox.theme-light .HyperMD-codeblock .token.doctype,
.gruvbox.theme-light .HyperMD-codeblock .token.builtin {
  color: #b57614;
}
.gruvbox.theme-light p > code .cm-number,
.gruvbox.theme-light p > code .token.entity,
.gruvbox.theme-light p > code .token.number,
.gruvbox.theme-light p > code .token.symbol,
.gruvbox.theme-light pre:not(.frontmatter) .cm-number,
.gruvbox.theme-light pre:not(.frontmatter) .token.entity,
.gruvbox.theme-light pre:not(.frontmatter) .token.number,
.gruvbox.theme-light pre:not(.frontmatter) .token.symbol,
.gruvbox.theme-light .HyperMD-codeblock .cm-number,
.gruvbox.theme-light .HyperMD-codeblock .token.entity,
.gruvbox.theme-light .HyperMD-codeblock .token.number,
.gruvbox.theme-light .HyperMD-codeblock .token.symbol {
  color: #8f3f71;
}
.gruvbox.theme-light p > code .cm-variable,
.gruvbox.theme-light p > code .cm-variable-2,
.gruvbox.theme-light p > code .cm-variable-3,
.gruvbox.theme-light p > code .cm-property,
.gruvbox.theme-light p > code .token.property,
.gruvbox.theme-light p > code .token.constant,
.gruvbox.theme-light p > code .token.variable,
.gruvbox.theme-light pre:not(.frontmatter) .cm-variable,
.gruvbox.theme-light pre:not(.frontmatter) .cm-variable-2,
.gruvbox.theme-light pre:not(.frontmatter) .cm-variable-3,
.gruvbox.theme-light pre:not(.frontmatter) .cm-property,
.gruvbox.theme-light pre:not(.frontmatter) .token.property,
.gruvbox.theme-light pre:not(.frontmatter) .token.constant,
.gruvbox.theme-light pre:not(.frontmatter) .token.variable,
.gruvbox.theme-light .HyperMD-codeblock .cm-variable,
.gruvbox.theme-light .HyperMD-codeblock .cm-variable-2,
.gruvbox.theme-light .HyperMD-codeblock .cm-variable-3,
.gruvbox.theme-light .HyperMD-codeblock .cm-property,
.gruvbox.theme-light .HyperMD-codeblock .token.property,
.gruvbox.theme-light .HyperMD-codeblock .token.constant,
.gruvbox.theme-light .HyperMD-codeblock .token.variable {
  color: #9d0006;
}
.gruvbox.theme-light p > code .cm-string-2,
.gruvbox.theme-light p > code .token.string,
.gruvbox.theme-light p > code .token.char,
.gruvbox.theme-light pre:not(.frontmatter) .cm-string-2,
.gruvbox.theme-light pre:not(.frontmatter) .token.string,
.gruvbox.theme-light pre:not(.frontmatter) .token.char,
.gruvbox.theme-light .HyperMD-codeblock .cm-string-2,
.gruvbox.theme-light .HyperMD-codeblock .token.string,
.gruvbox.theme-light .HyperMD-codeblock .token.char {
  color: #797403;
}
.gruvbox.theme-light p > code .cm-string,
.gruvbox.theme-light p > code .token.attr-value,
.gruvbox.theme-light p > code .token.attr-value .punctuation,
.gruvbox.theme-light pre:not(.frontmatter) .cm-string,
.gruvbox.theme-light pre:not(.frontmatter) .token.attr-value,
.gruvbox.theme-light pre:not(.frontmatter) .token.attr-value .punctuation,
.gruvbox.theme-light .HyperMD-codeblock .cm-string,
.gruvbox.theme-light .HyperMD-codeblock .token.attr-value,
.gruvbox.theme-light .HyperMD-codeblock .token.attr-value .punctuation {
  color: #7c6f64;
}
.gruvbox.theme-light p > code .cm-link,
.gruvbox.theme-light p > code .token.url,
.gruvbox.theme-light pre:not(.frontmatter) .cm-link,
.gruvbox.theme-light pre:not(.frontmatter) .token.url,
.gruvbox.theme-light .HyperMD-codeblock .cm-link,
.gruvbox.theme-light .HyperMD-codeblock .token.url {
  color: #797403;
  text-decoration: underline;
}
.gruvbox.theme-light p > code .cm-qualifier,
.gruvbox.theme-light p > code .token.function,
.gruvbox.theme-light pre:not(.frontmatter) .cm-qualifier,
.gruvbox.theme-light pre:not(.frontmatter) .token.function,
.gruvbox.theme-light .HyperMD-codeblock .cm-qualifier,
.gruvbox.theme-light .HyperMD-codeblock .token.function {
  color: #b57614;
}
.gruvbox.theme-light p > code .token.regex,
.gruvbox.theme-light pre:not(.frontmatter) .token.regex,
.gruvbox.theme-light .HyperMD-codeblock .token.regex {
  background: #797403;
}
.gruvbox.theme-light p > code .token.bold,
.gruvbox.theme-light pre:not(.frontmatter) .token.bold,
.gruvbox.theme-light .HyperMD-codeblock .token.bold {
  font-weight: bold;
}
.gruvbox.theme-light p > code .token.italic,
.gruvbox.theme-light pre:not(.frontmatter) .token.italic,
.gruvbox.theme-light .HyperMD-codeblock .token.italic {
  font-style: italic;
}
.gruvbox.theme-light p > code .token.inserted,
.gruvbox.theme-light pre:not(.frontmatter) .token.inserted,
.gruvbox.theme-light .HyperMD-codeblock .token.inserted {
  background: #7c6f64;
}
.gruvbox.theme-light p > code .token.deleted,
.gruvbox.theme-light pre:not(.frontmatter) .token.deleted,
.gruvbox.theme-light .HyperMD-codeblock .token.deleted {
  background: #9d0006;
}
.gruvbox.theme-dark p > code,
.gruvbox.theme-dark pre:not(.frontmatter),
.gruvbox.theme-dark .HyperMD-codeblock {
  --code-background: #282828;
  --code-normal: #fbf1c7;
  --text-muted: #a89984;
  --text-faint: #a89984;
}
.gruvbox.theme-dark p > code .cm-hr,
.gruvbox.theme-dark p > code .cm-comment,
.gruvbox.theme-dark p > code .cm-meta,
.gruvbox.theme-dark p > code .token.comment,
.gruvbox.theme-dark p > code .token.prolog,
.gruvbox.theme-dark p > code .token.cdata,
.gruvbox.theme-dark pre:not(.frontmatter) .cm-hr,
.gruvbox.theme-dark pre:not(.frontmatter) .cm-comment,
.gruvbox.theme-dark pre:not(.frontmatter) .cm-meta,
.gruvbox.theme-dark pre:not(.frontmatter) .token.comment,
.gruvbox.theme-dark pre:not(.frontmatter) .token.prolog,
.gruvbox.theme-dark pre:not(.frontmatter) .token.cdata,
.gruvbox.theme-dark .HyperMD-codeblock .cm-hr,
.gruvbox.theme-dark .HyperMD-codeblock .cm-comment,
.gruvbox.theme-dark .HyperMD-codeblock .cm-meta,
.gruvbox.theme-dark .HyperMD-codeblock .token.comment,
.gruvbox.theme-dark .HyperMD-codeblock .token.prolog,
.gruvbox.theme-dark .HyperMD-codeblock .token.cdata {
  color: #a89984;
}
.gruvbox.theme-dark p > code .cm-keyword,
.gruvbox.theme-dark p > code .token.delimiter,
.gruvbox.theme-dark p > code .token.boolean,
.gruvbox.theme-dark p > code .token.keyword,
.gruvbox.theme-dark p > code .token.selector,
.gruvbox.theme-dark p > code .token.important,
.gruvbox.theme-dark p > code .token.atrule,
.gruvbox.theme-dark pre:not(.frontmatter) .cm-keyword,
.gruvbox.theme-dark pre:not(.frontmatter) .token.delimiter,
.gruvbox.theme-dark pre:not(.frontmatter) .token.boolean,
.gruvbox.theme-dark pre:not(.frontmatter) .token.keyword,
.gruvbox.theme-dark pre:not(.frontmatter) .token.selector,
.gruvbox.theme-dark pre:not(.frontmatter) .token.important,
.gruvbox.theme-dark pre:not(.frontmatter) .token.atrule,
.gruvbox.theme-dark .HyperMD-codeblock .cm-keyword,
.gruvbox.theme-dark .HyperMD-codeblock .token.delimiter,
.gruvbox.theme-dark .HyperMD-codeblock .token.boolean,
.gruvbox.theme-dark .HyperMD-codeblock .token.keyword,
.gruvbox.theme-dark .HyperMD-codeblock .token.selector,
.gruvbox.theme-dark .HyperMD-codeblock .token.important,
.gruvbox.theme-dark .HyperMD-codeblock .token.atrule {
  color: #fb4934;
}
.gruvbox.theme-dark p > code .cm-attribute,
.gruvbox.theme-dark p > code .cm-operator,
.gruvbox.theme-dark p > code .cm-punctuation,
.gruvbox.theme-dark p > code .cm-bracket.cm-tag,
.gruvbox.theme-dark p > code .token.operator,
.gruvbox.theme-dark p > code .token.punctuation,
.gruvbox.theme-dark p > code .token.attr-name,
.gruvbox.theme-dark pre:not(.frontmatter) .cm-attribute,
.gruvbox.theme-dark pre:not(.frontmatter) .cm-operator,
.gruvbox.theme-dark pre:not(.frontmatter) .cm-punctuation,
.gruvbox.theme-dark pre:not(.frontmatter) .cm-bracket.cm-tag,
.gruvbox.theme-dark pre:not(.frontmatter) .token.operator,
.gruvbox.theme-dark pre:not(.frontmatter) .token.punctuation,
.gruvbox.theme-dark pre:not(.frontmatter) .token.attr-name,
.gruvbox.theme-dark .HyperMD-codeblock .cm-attribute,
.gruvbox.theme-dark .HyperMD-codeblock .cm-operator,
.gruvbox.theme-dark .HyperMD-codeblock .cm-punctuation,
.gruvbox.theme-dark .HyperMD-codeblock .cm-bracket.cm-tag,
.gruvbox.theme-dark .HyperMD-codeblock .token.operator,
.gruvbox.theme-dark .HyperMD-codeblock .token.punctuation,
.gruvbox.theme-dark .HyperMD-codeblock .token.attr-name {
  color: #a89984;
}
.gruvbox.theme-dark p > code .cm-builtin,
.gruvbox.theme-dark p > code .cm-type,
.gruvbox.theme-dark p > code .cm-tag,
.gruvbox.theme-dark p > code .token.tag,
.gruvbox.theme-dark p > code .token.tag .punctuation,
.gruvbox.theme-dark p > code .token.doctype,
.gruvbox.theme-dark p > code .token.builtin,
.gruvbox.theme-dark pre:not(.frontmatter) .cm-builtin,
.gruvbox.theme-dark pre:not(.frontmatter) .cm-type,
.gruvbox.theme-dark pre:not(.frontmatter) .cm-tag,
.gruvbox.theme-dark pre:not(.frontmatter) .token.tag,
.gruvbox.theme-dark pre:not(.frontmatter) .token.tag .punctuation,
.gruvbox.theme-dark pre:not(.frontmatter) .token.doctype,
.gruvbox.theme-dark pre:not(.frontmatter) .token.builtin,
.gruvbox.theme-dark .HyperMD-codeblock .cm-builtin,
.gruvbox.theme-dark .HyperMD-codeblock .cm-type,
.gruvbox.theme-dark .HyperMD-codeblock .cm-tag,
.gruvbox.theme-dark .HyperMD-codeblock .token.tag,
.gruvbox.theme-dark .HyperMD-codeblock .token.tag .punctuation,
.gruvbox.theme-dark .HyperMD-codeblock .token.doctype,
.gruvbox.theme-dark .HyperMD-codeblock .token.builtin {
  color: #fabd2f;
}
.gruvbox.theme-dark p > code .cm-number,
.gruvbox.theme-dark p > code .token.entity,
.gruvbox.theme-dark p > code .token.number,
.gruvbox.theme-dark p > code .token.symbol,
.gruvbox.theme-dark pre:not(.frontmatter) .cm-number,
.gruvbox.theme-dark pre:not(.frontmatter) .token.entity,
.gruvbox.theme-dark pre:not(.frontmatter) .token.number,
.gruvbox.theme-dark pre:not(.frontmatter) .token.symbol,
.gruvbox.theme-dark .HyperMD-codeblock .cm-number,
.gruvbox.theme-dark .HyperMD-codeblock .token.entity,
.gruvbox.theme-dark .HyperMD-codeblock .token.number,
.gruvbox.theme-dark .HyperMD-codeblock .token.symbol {
  color: #d3869b;
}
.gruvbox.theme-dark p > code .cm-variable,
.gruvbox.theme-dark p > code .cm-variable-2,
.gruvbox.theme-dark p > code .cm-variable-3,
.gruvbox.theme-dark p > code .cm-property,
.gruvbox.theme-dark p > code .token.property,
.gruvbox.theme-dark p > code .token.constant,
.gruvbox.theme-dark p > code .token.variable,
.gruvbox.theme-dark pre:not(.frontmatter) .cm-variable,
.gruvbox.theme-dark pre:not(.frontmatter) .cm-variable-2,
.gruvbox.theme-dark pre:not(.frontmatter) .cm-variable-3,
.gruvbox.theme-dark pre:not(.frontmatter) .cm-property,
.gruvbox.theme-dark pre:not(.frontmatter) .token.property,
.gruvbox.theme-dark pre:not(.frontmatter) .token.constant,
.gruvbox.theme-dark pre:not(.frontmatter) .token.variable,
.gruvbox.theme-dark .HyperMD-codeblock .cm-variable,
.gruvbox.theme-dark .HyperMD-codeblock .cm-variable-2,
.gruvbox.theme-dark .HyperMD-codeblock .cm-variable-3,
.gruvbox.theme-dark .HyperMD-codeblock .cm-property,
.gruvbox.theme-dark .HyperMD-codeblock .token.property,
.gruvbox.theme-dark .HyperMD-codeblock .token.constant,
.gruvbox.theme-dark .HyperMD-codeblock .token.variable {
  color: #fb4934;
}
.gruvbox.theme-dark p > code .cm-string-2,
.gruvbox.theme-dark p > code .token.string,
.gruvbox.theme-dark p > code .token.char,
.gruvbox.theme-dark pre:not(.frontmatter) .cm-string-2,
.gruvbox.theme-dark pre:not(.frontmatter) .token.string,
.gruvbox.theme-dark pre:not(.frontmatter) .token.char,
.gruvbox.theme-dark .HyperMD-codeblock .cm-string-2,
.gruvbox.theme-dark .HyperMD-codeblock .token.string,
.gruvbox.theme-dark .HyperMD-codeblock .token.char {
  color: #b8bb26;
}
.gruvbox.theme-dark p > code .cm-string,
.gruvbox.theme-dark p > code .token.attr-value,
.gruvbox.theme-dark p > code .token.attr-value .punctuation,
.gruvbox.theme-dark pre:not(.frontmatter) .cm-string,
.gruvbox.theme-dark pre:not(.frontmatter) .token.attr-value,
.gruvbox.theme-dark pre:not(.frontmatter) .token.attr-value .punctuation,
.gruvbox.theme-dark .HyperMD-codeblock .cm-string,
.gruvbox.theme-dark .HyperMD-codeblock .token.attr-value,
.gruvbox.theme-dark .HyperMD-codeblock .token.attr-value .punctuation {
  color: #a89984;
}
.gruvbox.theme-dark p > code .cm-link,
.gruvbox.theme-dark p > code .token.url,
.gruvbox.theme-dark pre:not(.frontmatter) .cm-link,
.gruvbox.theme-dark pre:not(.frontmatter) .token.url,
.gruvbox.theme-dark .HyperMD-codeblock .cm-link,
.gruvbox.theme-dark .HyperMD-codeblock .token.url {
  color: #b8bb26;
  text-decoration: underline;
}
.gruvbox.theme-dark p > code .cm-qualifier,
.gruvbox.theme-dark p > code .token.function,
.gruvbox.theme-dark pre:not(.frontmatter) .cm-qualifier,
.gruvbox.theme-dark pre:not(.frontmatter) .token.function,
.gruvbox.theme-dark .HyperMD-codeblock .cm-qualifier,
.gruvbox.theme-dark .HyperMD-codeblock .token.function {
  color: #fabd2f;
}
.gruvbox.theme-dark p > code .token.regex,
.gruvbox.theme-dark pre:not(.frontmatter) .token.regex,
.gruvbox.theme-dark .HyperMD-codeblock .token.regex {
  background: #b8bb26;
}
.gruvbox.theme-dark p > code .token.bold,
.gruvbox.theme-dark pre:not(.frontmatter) .token.bold,
.gruvbox.theme-dark .HyperMD-codeblock .token.bold {
  font-weight: bold;
}
.gruvbox.theme-dark p > code .token.italic,
.gruvbox.theme-dark pre:not(.frontmatter) .token.italic,
.gruvbox.theme-dark .HyperMD-codeblock .token.italic {
  font-style: italic;
}
.gruvbox.theme-dark p > code .token.inserted,
.gruvbox.theme-dark pre:not(.frontmatter) .token.inserted,
.gruvbox.theme-dark .HyperMD-codeblock .token.inserted {
  background: #a89984;
}
.gruvbox.theme-dark p > code .token.deleted,
.gruvbox.theme-dark pre:not(.frontmatter) .token.deleted,
.gruvbox.theme-dark .HyperMD-codeblock .token.deleted {
  background: #fb4934;
}
.material.theme-dark p > code,
.material.theme-dark pre:not(.frontmatter),
.material.theme-dark .HyperMD-codeblock {
  --code-background: #2f2f2f;
  --code-normal: #eee;
  --text-muted:#616161;
  --text-faint:#616161;
}
.material.theme-dark p > code .language-css > code,
.material.theme-dark p > code .language-sass > code,
.material.theme-dark p > code .language-scss > code,
.material.theme-dark pre:not(.frontmatter) .language-css > code,
.material.theme-dark pre:not(.frontmatter) .language-sass > code,
.material.theme-dark pre:not(.frontmatter) .language-scss > code,
.material.theme-dark .HyperMD-codeblock .language-css > code,
.material.theme-dark .HyperMD-codeblock .language-sass > code,
.material.theme-dark .HyperMD-codeblock .language-scss > code {
  color: #fd9170;
}
.material.theme-dark p > code [class*=language-] .namespace,
.material.theme-dark pre:not(.frontmatter) [class*=language-] .namespace,
.material.theme-dark .HyperMD-codeblock [class*=language-] .namespace {
  opacity: 0.7;
}
.material.theme-dark p > code .token.atrule,
.material.theme-dark pre:not(.frontmatter) .token.atrule,
.material.theme-dark .HyperMD-codeblock .token.atrule {
  color: #c792ea;
}
.material.theme-dark p > code .token.attr-name,
.material.theme-dark pre:not(.frontmatter) .token.attr-name,
.material.theme-dark .HyperMD-codeblock .token.attr-name {
  color: #ffcb6b;
}
.material.theme-dark p > code .cm-string,
.material.theme-dark p > code .token.attr-value,
.material.theme-dark pre:not(.frontmatter) .cm-string,
.material.theme-dark pre:not(.frontmatter) .token.attr-value,
.material.theme-dark .HyperMD-codeblock .cm-string,
.material.theme-dark .HyperMD-codeblock .token.attr-value {
  color: #a5e844;
}
.material.theme-dark p > code .cm-attribute,
.material.theme-dark p > code .token.attribute,
.material.theme-dark pre:not(.frontmatter) .cm-attribute,
.material.theme-dark pre:not(.frontmatter) .token.attribute,
.material.theme-dark .HyperMD-codeblock .cm-attribute,
.material.theme-dark .HyperMD-codeblock .token.attribute {
  color: #a5e844;
}
.material.theme-dark p > code .token.boolean,
.material.theme-dark pre:not(.frontmatter) .token.boolean,
.material.theme-dark .HyperMD-codeblock .token.boolean {
  color: #c792ea;
}
.material.theme-dark p > code .cm-builtin,
.material.theme-dark p > code .token.builtin,
.material.theme-dark pre:not(.frontmatter) .cm-builtin,
.material.theme-dark pre:not(.frontmatter) .token.builtin,
.material.theme-dark .HyperMD-codeblock .cm-builtin,
.material.theme-dark .HyperMD-codeblock .token.builtin {
  color: #ffcb6b;
}
.material.theme-dark p > code .token.cdata,
.material.theme-dark pre:not(.frontmatter) .token.cdata,
.material.theme-dark .HyperMD-codeblock .token.cdata {
  color: #80cbc4;
}
.material.theme-dark p > code .token.char,
.material.theme-dark pre:not(.frontmatter) .token.char,
.material.theme-dark .HyperMD-codeblock .token.char {
  color: #80cbc4;
}
.material.theme-dark p > code .token.class,
.material.theme-dark pre:not(.frontmatter) .token.class,
.material.theme-dark .HyperMD-codeblock .token.class {
  color: #ffcb6b;
}
.material.theme-dark p > code .token.class-name,
.material.theme-dark pre:not(.frontmatter) .token.class-name,
.material.theme-dark .HyperMD-codeblock .token.class-name {
  color: #f2ff00;
}
.material.theme-dark p > code .cm-comment,
.material.theme-dark p > code .cm-meta,
.material.theme-dark p > code .cm-hr,
.material.theme-dark p > code .token.comment,
.material.theme-dark pre:not(.frontmatter) .cm-comment,
.material.theme-dark pre:not(.frontmatter) .cm-meta,
.material.theme-dark pre:not(.frontmatter) .cm-hr,
.material.theme-dark pre:not(.frontmatter) .token.comment,
.material.theme-dark .HyperMD-codeblock .cm-comment,
.material.theme-dark .HyperMD-codeblock .cm-meta,
.material.theme-dark .HyperMD-codeblock .cm-hr,
.material.theme-dark .HyperMD-codeblock .token.comment {
  color: #616161;
}
.material.theme-dark p > code .token.constant,
.material.theme-dark pre:not(.frontmatter) .token.constant,
.material.theme-dark .HyperMD-codeblock .token.constant {
  color: #c792ea;
}
.material.theme-dark p > code .token.deleted,
.material.theme-dark pre:not(.frontmatter) .token.deleted,
.material.theme-dark .HyperMD-codeblock .token.deleted {
  color: #ff6666;
}
.material.theme-dark p > code .cm-type,
.material.theme-dark p > code .token.doctype,
.material.theme-dark pre:not(.frontmatter) .cm-type,
.material.theme-dark pre:not(.frontmatter) .token.doctype,
.material.theme-dark .HyperMD-codeblock .cm-type,
.material.theme-dark .HyperMD-codeblock .token.doctype {
  color: #616161;
}
.material.theme-dark p > code .token.entity,
.material.theme-dark pre:not(.frontmatter) .token.entity,
.material.theme-dark .HyperMD-codeblock .token.entity {
  color: #ff6666;
}
.material.theme-dark p > code .cm-qualifier,
.material.theme-dark p > code .token.function,
.material.theme-dark pre:not(.frontmatter) .cm-qualifier,
.material.theme-dark pre:not(.frontmatter) .token.function,
.material.theme-dark .HyperMD-codeblock .cm-qualifier,
.material.theme-dark .HyperMD-codeblock .token.function {
  color: #c792ea;
}
.material.theme-dark p > code .token.hexcode,
.material.theme-dark pre:not(.frontmatter) .token.hexcode,
.material.theme-dark .HyperMD-codeblock .token.hexcode {
  color: #f2ff00;
}
.material.theme-dark p > code .token.id,
.material.theme-dark pre:not(.frontmatter) .token.id,
.material.theme-dark .HyperMD-codeblock .token.id {
  color: #c792ea;
  font-weight: bold;
}
.material.theme-dark p > code .token.important,
.material.theme-dark pre:not(.frontmatter) .token.important,
.material.theme-dark .HyperMD-codeblock .token.important {
  color: #c792ea;
  font-weight: bold;
}
.material.theme-dark p > code .token.inserted,
.material.theme-dark pre:not(.frontmatter) .token.inserted,
.material.theme-dark .HyperMD-codeblock .token.inserted {
  color: #80cbc4;
}
.material.theme-dark p > code .cm-keyword,
.material.theme-dark p > code .token.keyword,
.material.theme-dark pre:not(.frontmatter) .cm-keyword,
.material.theme-dark pre:not(.frontmatter) .token.keyword,
.material.theme-dark .HyperMD-codeblock .cm-keyword,
.material.theme-dark .HyperMD-codeblock .token.keyword {
  color: #c792ea;
}
.material.theme-dark p > code .cm-number,
.material.theme-dark p > code .token.number,
.material.theme-dark pre:not(.frontmatter) .cm-number,
.material.theme-dark pre:not(.frontmatter) .token.number,
.material.theme-dark .HyperMD-codeblock .cm-number,
.material.theme-dark .HyperMD-codeblock .token.number {
  color: #fd9170;
}
.material.theme-dark p > code .cm-operator,
.material.theme-dark p > code .token.operator,
.material.theme-dark pre:not(.frontmatter) .cm-operator,
.material.theme-dark pre:not(.frontmatter) .token.operator,
.material.theme-dark .HyperMD-codeblock .cm-operator,
.material.theme-dark .HyperMD-codeblock .token.operator {
  color: #89ddff;
}
.material.theme-dark p > code .token.prolog,
.material.theme-dark pre:not(.frontmatter) .token.prolog,
.material.theme-dark .HyperMD-codeblock .token.prolog {
  color: #616161;
}
.material.theme-dark p > code .cm-property,
.material.theme-dark p > code .token.property,
.material.theme-dark pre:not(.frontmatter) .cm-property,
.material.theme-dark pre:not(.frontmatter) .token.property,
.material.theme-dark .HyperMD-codeblock .cm-property,
.material.theme-dark .HyperMD-codeblock .token.property {
  color: #80cbc4;
}
.material.theme-dark p > code .token.pseudo-class,
.material.theme-dark pre:not(.frontmatter) .token.pseudo-class,
.material.theme-dark .HyperMD-codeblock .token.pseudo-class {
  color: #a5e844;
}
.material.theme-dark p > code .token.pseudo-element,
.material.theme-dark pre:not(.frontmatter) .token.pseudo-element,
.material.theme-dark .HyperMD-codeblock .token.pseudo-element {
  color: #a5e844;
}
.material.theme-dark p > code .cm-punctuation,
.material.theme-dark p > code .cm-bracket.cm-tag,
.material.theme-dark p > code .token.punctuation,
.material.theme-dark pre:not(.frontmatter) .cm-punctuation,
.material.theme-dark pre:not(.frontmatter) .cm-bracket.cm-tag,
.material.theme-dark pre:not(.frontmatter) .token.punctuation,
.material.theme-dark .HyperMD-codeblock .cm-punctuation,
.material.theme-dark .HyperMD-codeblock .cm-bracket.cm-tag,
.material.theme-dark .HyperMD-codeblock .token.punctuation {
  color: #89ddff;
}
.material.theme-dark p > code .token.regex,
.material.theme-dark pre:not(.frontmatter) .token.regex,
.material.theme-dark .HyperMD-codeblock .token.regex {
  color: #f2ff00;
}
.material.theme-dark p > code .token.selector,
.material.theme-dark pre:not(.frontmatter) .token.selector,
.material.theme-dark .HyperMD-codeblock .token.selector {
  color: #ff6666;
}
.material.theme-dark p > code .cm-string-2,
.material.theme-dark p > code .token.string,
.material.theme-dark pre:not(.frontmatter) .cm-string-2,
.material.theme-dark pre:not(.frontmatter) .token.string,
.material.theme-dark .HyperMD-codeblock .cm-string-2,
.material.theme-dark .HyperMD-codeblock .token.string {
  color: #a5e844;
}
.material.theme-dark p > code .token.symbol,
.material.theme-dark pre:not(.frontmatter) .token.symbol,
.material.theme-dark .HyperMD-codeblock .token.symbol {
  color: #c792ea;
}
.material.theme-dark p > code .cm-tag,
.material.theme-dark p > code .token.tag,
.material.theme-dark pre:not(.frontmatter) .cm-tag,
.material.theme-dark pre:not(.frontmatter) .token.tag,
.material.theme-dark .HyperMD-codeblock .cm-tag,
.material.theme-dark .HyperMD-codeblock .token.tag {
  color: #ff6666;
}
.material.theme-dark p > code .token.unit,
.material.theme-dark pre:not(.frontmatter) .token.unit,
.material.theme-dark .HyperMD-codeblock .token.unit {
  color: #fd9170;
}
.material.theme-dark p > code .cm-link,
.material.theme-dark p > code .token.url,
.material.theme-dark pre:not(.frontmatter) .cm-link,
.material.theme-dark pre:not(.frontmatter) .token.url,
.material.theme-dark .HyperMD-codeblock .cm-link,
.material.theme-dark .HyperMD-codeblock .token.url {
  color: #ff6666;
}
.material.theme-dark p > code .cm-variable,
.material.theme-dark p > code .cm-variable-2,
.material.theme-dark p > code .cm-variable-3,
.material.theme-dark p > code .token.variable,
.material.theme-dark pre:not(.frontmatter) .cm-variable,
.material.theme-dark pre:not(.frontmatter) .cm-variable-2,
.material.theme-dark pre:not(.frontmatter) .cm-variable-3,
.material.theme-dark pre:not(.frontmatter) .token.variable,
.material.theme-dark .HyperMD-codeblock .cm-variable,
.material.theme-dark .HyperMD-codeblock .cm-variable-2,
.material.theme-dark .HyperMD-codeblock .cm-variable-3,
.material.theme-dark .HyperMD-codeblock .token.variable {
  color: #ff6666;
}
.material.theme-light p > code,
.material.theme-light pre:not(.frontmatter),
.material.theme-light .HyperMD-codeblock {
  --code-background: #fafafa;
  --code-normal: #90a4ae;
  --text-muted: #aabfc9;
  --text-faint: #aabfc9;
}
.material.theme-light p > code .language-css > code,
.material.theme-light p > code .language-sass > code,
.material.theme-light p > code .language-scss > code,
.material.theme-light pre:not(.frontmatter) .language-css > code,
.material.theme-light pre:not(.frontmatter) .language-sass > code,
.material.theme-light pre:not(.frontmatter) .language-scss > code,
.material.theme-light .HyperMD-codeblock .language-css > code,
.material.theme-light .HyperMD-codeblock .language-sass > code,
.material.theme-light .HyperMD-codeblock .language-scss > code {
  color: #f76d47;
}
.material.theme-light p > code [class*=language-] .namespace,
.material.theme-light pre:not(.frontmatter) [class*=language-] .namespace,
.material.theme-light .HyperMD-codeblock [class*=language-] .namespace {
  opacity: 0.7;
}
.material.theme-light p > code .token.atrule,
.material.theme-light pre:not(.frontmatter) .token.atrule,
.material.theme-light .HyperMD-codeblock .token.atrule {
  color: #7c4dff;
}
.material.theme-light p > code .token.attr-name,
.material.theme-light pre:not(.frontmatter) .token.attr-name,
.material.theme-light .HyperMD-codeblock .token.attr-name {
  color: #39adb5;
}
.material.theme-light p > code .cm-string,
.material.theme-light p > code .token.attr-value,
.material.theme-light pre:not(.frontmatter) .cm-string,
.material.theme-light pre:not(.frontmatter) .token.attr-value,
.material.theme-light .HyperMD-codeblock .cm-string,
.material.theme-light .HyperMD-codeblock .token.attr-value {
  color: #f6a434;
}
.material.theme-light p > code .cm-attribute,
.material.theme-light p > code .token.attribute,
.material.theme-light pre:not(.frontmatter) .cm-attribute,
.material.theme-light pre:not(.frontmatter) .token.attribute,
.material.theme-light .HyperMD-codeblock .cm-attribute,
.material.theme-light .HyperMD-codeblock .token.attribute {
  color: #f6a434;
}
.material.theme-light p > code .token.boolean,
.material.theme-light pre:not(.frontmatter) .token.boolean,
.material.theme-light .HyperMD-codeblock .token.boolean {
  color: #7c4dff;
}
.material.theme-light p > code .cm-builtin,
.material.theme-light p > code .token.builtin,
.material.theme-light pre:not(.frontmatter) .cm-builtin,
.material.theme-light pre:not(.frontmatter) .token.builtin,
.material.theme-light .HyperMD-codeblock .cm-builtin,
.material.theme-light .HyperMD-codeblock .token.builtin {
  color: #39adb5;
}
.material.theme-light p > code .token.cdata,
.material.theme-light pre:not(.frontmatter) .token.cdata,
.material.theme-light .HyperMD-codeblock .token.cdata {
  color: #39adb5;
}
.material.theme-light p > code .token.char,
.material.theme-light pre:not(.frontmatter) .token.char,
.material.theme-light .HyperMD-codeblock .token.char {
  color: #39adb5;
}
.material.theme-light p > code .token.class,
.material.theme-light pre:not(.frontmatter) .token.class,
.material.theme-light .HyperMD-codeblock .token.class {
  color: #39adb5;
}
.material.theme-light p > code .token.class-name,
.material.theme-light pre:not(.frontmatter) .token.class-name,
.material.theme-light .HyperMD-codeblock .token.class-name {
  color: #6182b8;
}
.material.theme-light p > code .cm-comment,
.material.theme-light p > code .cm-meta,
.material.theme-light p > code .cm-hr,
.material.theme-light p > code .token.comment,
.material.theme-light pre:not(.frontmatter) .cm-comment,
.material.theme-light pre:not(.frontmatter) .cm-meta,
.material.theme-light pre:not(.frontmatter) .cm-hr,
.material.theme-light pre:not(.frontmatter) .token.comment,
.material.theme-light .HyperMD-codeblock .cm-comment,
.material.theme-light .HyperMD-codeblock .cm-meta,
.material.theme-light .HyperMD-codeblock .cm-hr,
.material.theme-light .HyperMD-codeblock .token.comment {
  color: #aabfc9;
}
.material.theme-light p > code .token.constant,
.material.theme-light pre:not(.frontmatter) .token.constant,
.material.theme-light .HyperMD-codeblock .token.constant {
  color: #7c4dff;
}
.material.theme-light p > code .token.deleted,
.material.theme-light pre:not(.frontmatter) .token.deleted,
.material.theme-light .HyperMD-codeblock .token.deleted {
  color: #e53935;
}
.material.theme-light p > code .cm-type,
.material.theme-light p > code .token.doctype,
.material.theme-light pre:not(.frontmatter) .cm-type,
.material.theme-light pre:not(.frontmatter) .token.doctype,
.material.theme-light .HyperMD-codeblock .cm-type,
.material.theme-light .HyperMD-codeblock .token.doctype {
  color: #aabfc9;
}
.material.theme-light p > code .token.entity,
.material.theme-light pre:not(.frontmatter) .token.entity,
.material.theme-light .HyperMD-codeblock .token.entity {
  color: #e53935;
}
.material.theme-light p > code .cm-qualifier,
.material.theme-light p > code .token.function,
.material.theme-light pre:not(.frontmatter) .cm-qualifier,
.material.theme-light pre:not(.frontmatter) .token.function,
.material.theme-light .HyperMD-codeblock .cm-qualifier,
.material.theme-light .HyperMD-codeblock .token.function {
  color: #7c4dff;
}
.material.theme-light p > code .token.hexcode,
.material.theme-light pre:not(.frontmatter) .token.hexcode,
.material.theme-light .HyperMD-codeblock .token.hexcode {
  color: #f76d47;
}
.material.theme-light p > code .token.id,
.material.theme-light pre:not(.frontmatter) .token.id,
.material.theme-light .HyperMD-codeblock .token.id {
  color: #7c4dff;
  font-weight: bold;
}
.material.theme-light p > code .token.important,
.material.theme-light pre:not(.frontmatter) .token.important,
.material.theme-light .HyperMD-codeblock .token.important {
  color: #7c4dff;
  font-weight: bold;
}
.material.theme-light p > code .token.inserted,
.material.theme-light pre:not(.frontmatter) .token.inserted,
.material.theme-light .HyperMD-codeblock .token.inserted {
  color: #39adb5;
}
.material.theme-light p > code .cm-keyword,
.material.theme-light p > code .token.keyword,
.material.theme-light pre:not(.frontmatter) .cm-keyword,
.material.theme-light pre:not(.frontmatter) .token.keyword,
.material.theme-light .HyperMD-codeblock .cm-keyword,
.material.theme-light .HyperMD-codeblock .token.keyword {
  color: #7c4dff;
}
.material.theme-light p > code .cm-number,
.material.theme-light p > code .token.number,
.material.theme-light pre:not(.frontmatter) .cm-number,
.material.theme-light pre:not(.frontmatter) .token.number,
.material.theme-light .HyperMD-codeblock .cm-number,
.material.theme-light .HyperMD-codeblock .token.number {
  color: #f76d47;
}
.material.theme-light p > code .cm-operator,
.material.theme-light p > code .token.operator,
.material.theme-light pre:not(.frontmatter) .cm-operator,
.material.theme-light pre:not(.frontmatter) .token.operator,
.material.theme-light .HyperMD-codeblock .cm-operator,
.material.theme-light .HyperMD-codeblock .token.operator {
  color: #39adb5;
}
.material.theme-light p > code .token.prolog,
.material.theme-light pre:not(.frontmatter) .token.prolog,
.material.theme-light .HyperMD-codeblock .token.prolog {
  color: #aabfc9;
}
.material.theme-light p > code .cm-property,
.material.theme-light p > code .token.property,
.material.theme-light pre:not(.frontmatter) .cm-property,
.material.theme-light pre:not(.frontmatter) .token.property,
.material.theme-light .HyperMD-codeblock .cm-property,
.material.theme-light .HyperMD-codeblock .token.property {
  color: #39adb5;
}
.material.theme-light p > code .token.pseudo-class,
.material.theme-light pre:not(.frontmatter) .token.pseudo-class,
.material.theme-light .HyperMD-codeblock .token.pseudo-class {
  color: #f6a434;
}
.material.theme-light p > code .token.pseudo-element,
.material.theme-light pre:not(.frontmatter) .token.pseudo-element,
.material.theme-light .HyperMD-codeblock .token.pseudo-element {
  color: #f6a434;
}
.material.theme-light p > code .cm-punctuation,
.material.theme-light p > code .cm-bracket.cm-tag,
.material.theme-light p > code .token.punctuation,
.material.theme-light pre:not(.frontmatter) .cm-punctuation,
.material.theme-light pre:not(.frontmatter) .cm-bracket.cm-tag,
.material.theme-light pre:not(.frontmatter) .token.punctuation,
.material.theme-light .HyperMD-codeblock .cm-punctuation,
.material.theme-light .HyperMD-codeblock .cm-bracket.cm-tag,
.material.theme-light .HyperMD-codeblock .token.punctuation {
  color: #39adb5;
}
.material.theme-light p > code .token.regex,
.material.theme-light pre:not(.frontmatter) .token.regex,
.material.theme-light .HyperMD-codeblock .token.regex {
  color: #6182b8;
}
.material.theme-light p > code .token.selector,
.material.theme-light pre:not(.frontmatter) .token.selector,
.material.theme-light .HyperMD-codeblock .token.selector {
  color: #e53935;
}
.material.theme-light p > code .cm-string-2,
.material.theme-light p > code .token.string,
.material.theme-light pre:not(.frontmatter) .cm-string-2,
.material.theme-light pre:not(.frontmatter) .token.string,
.material.theme-light .HyperMD-codeblock .cm-string-2,
.material.theme-light .HyperMD-codeblock .token.string {
  color: #f6a434;
}
.material.theme-light p > code .token.symbol,
.material.theme-light pre:not(.frontmatter) .token.symbol,
.material.theme-light .HyperMD-codeblock .token.symbol {
  color: #7c4dff;
}
.material.theme-light p > code .cm-tag,
.material.theme-light p > code .token.tag,
.material.theme-light pre:not(.frontmatter) .cm-tag,
.material.theme-light pre:not(.frontmatter) .token.tag,
.material.theme-light .HyperMD-codeblock .cm-tag,
.material.theme-light .HyperMD-codeblock .token.tag {
  color: #e53935;
}
.material.theme-light p > code .token.unit,
.material.theme-light pre:not(.frontmatter) .token.unit,
.material.theme-light .HyperMD-codeblock .token.unit {
  color: #f76d47;
}
.material.theme-light p > code .cm-link,
.material.theme-light p > code .token.url,
.material.theme-light pre:not(.frontmatter) .cm-link,
.material.theme-light pre:not(.frontmatter) .token.url,
.material.theme-light .HyperMD-codeblock .cm-link,
.material.theme-light .HyperMD-codeblock .token.url {
  color: #e53935;
}
.material.theme-light p > code .cm-variable,
.material.theme-light p > code .cm-variable-2,
.material.theme-light p > code .cm-variable-3,
.material.theme-light p > code .token.variable,
.material.theme-light pre:not(.frontmatter) .cm-variable,
.material.theme-light pre:not(.frontmatter) .cm-variable-2,
.material.theme-light pre:not(.frontmatter) .cm-variable-3,
.material.theme-light pre:not(.frontmatter) .token.variable,
.material.theme-light .HyperMD-codeblock .cm-variable,
.material.theme-light .HyperMD-codeblock .cm-variable-2,
.material.theme-light .HyperMD-codeblock .cm-variable-3,
.material.theme-light .HyperMD-codeblock .token.variable {
  color: #e53935;
}
.is-mobile {
  font-optical-sizing: auto;
}
body {
  --graph-controls-width: 240px;
  --graph-text: var(--text-normal);
  --graph-line: var(--border-subtle-1, var(--background-modifier-border-focus));
  --graph-node: var(--text-muted);
  --graph-node-unresolved: var(--text-faint);
  --graph-node-focused: rgb(var(--color-accent-rgb));
  --graph-node-tag: var(--red);
  --graph-node-attachment: var(--yellow);
}
body {
  --prompt-width: 700px;
  --prompt-max-width: 80vw;
  --prompt-max-height: 70vh;
  --prompt-border-width: var(--border-width);
  --prompt-border-color: var(--background-modifier-border);
}
input.prompt-input[type=text] {
  padding: 24px;
  border-radius: 0;
  border-bottom: 1px solid var(--border-strong-1);
}
input.prompt-input[type=text]:focus:placeholder-shown {
  box-shadow: none;
  border-bottom: 1px solid var(--background-modifier-border-focus);
}
.prompt-results {
  padding-right: 1px;
  overflow: auto;
}
.suggestion-item.mod-complex .suggestion-prefix {
  color: var(--interactive);
}
kbd,
.suggestion-item.mod-complex .suggestion-hotkey {
  background-color: var(--layer-1);
  outline: 1px solid var(--background-modifier-border);
}
.prompt-instruction-command {
  background-color: var(--layer-1);
  font-weight: var(--semibold-weight);
  padding: 2px 4px;
  border-radius: var(--radius-s);
  outline: 1px solid var(--background-modifier-border);
}
.theme-light .suggestion-container {
  --shadow-color: 60deg 5% 59%;
}
.theme-dark .suggestion-container {
  --shadow-color: 0deg 0% 1%;
}
.suggestion-container {
  box-shadow:
    0px 0.5px 0.5px hsl(var(--shadow-color)/0.43),
    0px 1.5px 1.6px -1px hsl(var(--shadow-color)/0.4),
    0px 4px 4.2px -2px hsl(var(--shadow-color)/0.36),
    0px 10.1px 10.6px -3px hsl(var(--shadow-color)/0.32);
}
body {
  --calendar-dot: var(--orange);
  --calendar-dot-hover: var(--red);
}
#calendar-container {
  padding: 0;
  font-feature-settings: normal;
}
#calendar-container .nav {
  margin: 0 0 1.5em 0;
  padding: 0;
}
#calendar-container .nav .title {
  padding-left: 8px;
  font-size: 1rem;
  flex-grow: 4;
  line-height: 1;
}
#calendar-container .nav .title .month,
#calendar-container .nav .title .year {
  color: var(--text-normal);
  font-weight: normal;
}
#calendar-container .right-nav {
  align-items: flex-start;
  align-self: flex-end;
  margin: 0;
}
#calendar-container .right-nav .reset-button {
  margin: 0;
  padding: 0;
  font-size: 0.75em;
  line-height: 1.5;
  color: var(--text-muted);
}
#calendar-container .right-nav .reset-button:hover {
  color: var(--text-normal);
}
#calendar-container .right-nav .arrow svg {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' xml:space='preserve'%3E%3Cpath d='M10 16 20 6l1.4 1.4-8.6 8.6 8.6 8.6L20 26z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
  opacity: 0.25;
}
#calendar-container .right-nav .arrow svg:hover {
  opacity: 1;
}
#calendar-container .right-nav .arrow svg > path {
  display: none;
}
#calendar-container table {
  table-layout: fixed;
  width: 100%;
  border-collapse: collapse;
}
#calendar-container table td {
  width: 32px;
}
#calendar-container table .day,
#calendar-container table .week-num {
  height: 32px;
  width: 100%;
  font-size: 0.75rem;
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
#calendar-container table th {
  padding-bottom: 2em;
  border-bottom: 1px solid var(--background-modifier-border);
}
#calendar-container .dot-container {
  padding-top: 2px;
  flex-wrap: nowrap;
  justify-content: center;
  align-items: center;
  width: 100%;
}
#calendar-container .dot-container svg {
  position: absolute;
  --color-dot: var(--calendar-dot);
}
#calendar-container .dot-container svg:first-child {
  height: 4px;
  width: 4px;
}
#calendar-container .dot-container svg:nth-child(2) {
  height: 6px;
  width: 6px;
}
#calendar-container .dot-container svg:nth-child(3) {
  height: 8px;
  width: 8px;
}
#calendar-container .dot-container svg:nth-child(4) {
  height: 10px;
  width: 10px;
}
#calendar-container .dot-container svg:nth-child(5) {
  height: 12px;
  width: 12px;
}
#calendar-container .day:hover .dot-container svg {
  --color-dot: var(--calendar-dot-hover);
}
.calendar-flip #calendar-container .nav {
  flex-direction: row-reverse;
  justify-content: space-between;
}
.calendar-flip #calendar-container .title {
  flex-grow: unset;
  padding-right: 8px;
}
.stendig.calendar-flip #calendar-container .title {
  justify-content: flex-end;
}
.stendig.calendar-flip #calendar-container .right-nav {
  justify-content: flex-end;
}
.stendig #calendar-container {
  padding: 0;
  font-feature-settings: normal;
}
.stendig #calendar-container .nav {
  flex-direction: column;
  margin-bottom: 2em;
}
.stendig #calendar-container .title {
  font-size: 1rem;
  padding-right: 8px;
  display: flex;
  flex-direction: row-reverse;
  width: 100%;
  justify-content: flex-start;
  gap: 2px;
  border-bottom: 1px solid var(--background-modifier-border);
}
.stendig #calendar-container .right-nav {
  flex-direction: row;
  justify-content: flex-start;
  margin: 0;
  width: 100%;
  gap: 0px;
  align-items: flex-end;
  padding-top: 4px;
}
.stendig #calendar-container .right-nav .reset-button {
  margin: 0;
  padding: 0;
  font-size: 0.75em;
  opacity: 0.25;
}
.stendig #calendar-container .right-nav .reset-button:hover {
  opacity: 1;
}
.stendig #calendar-container .right-nav .arrow svg {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' xml:space='preserve'%3E%3Cpath d='M10 16 20 6l1.4 1.4-8.6 8.6 8.6 8.6L20 26z'/%3E%3Cpath fill='none' d='M0 0h32v32H0z'/%3E%3C/svg%3E");
  opacity: 0.25;
}
.stendig #calendar-container .right-nav .arrow svg:hover {
  opacity: 1;
}
.stendig #calendar-container .right-nav .arrow svg > path {
  display: none;
}
.stendig #calendar-container table.calendar th {
  padding: 0;
  font-size: 1rem;
  font-weight: normal;
  color: var(--text-muted);
  text-align: left;
  color: transparent;
  border: none;
}
.stendig #calendar-container table.calendar th:not(:first-of-type) {
  border-left: 1px solid var(--background-modifier-border);
  border-radius: 0;
}
.stendig #calendar-container table.calendar th::first-letter {
  color: var(--text-normal);
}
.stendig #calendar-container table.calendar td:not(:first-of-type) {
  border-left: 1px solid var(--background-modifier-border);
  border-radius: 0;
}
.stendig #calendar-container table.calendar td .day,
.stendig #calendar-container table.calendar td .week-num {
  text-align: left;
  font-size: 1rem;
  padding: 0;
  display: flex;
  flex-direction: row;
}
.stendig #calendar-container table.calendar td .day:hover,
.stendig #calendar-container table.calendar td .week-num:hover {
  background-color: var(--background-hover);
}
.stendig #calendar-container table.calendar td .week-num {
  opacity: 0.25;
}
.stendig #calendar-container .dot-container {
  padding-top: 0;
}
.stendig #calendar-container .day:hover .dot-container svg {
  --color-dot: var(--calendar-dot-hover);
}
.stendig #calendar-container th,
.stendig #calendar-container td {
  padding-left: 2px;
}
.kanban-plugin {
  background-color: var(--background-primary);
  --lane-width: 272px;
}
.kanban-plugin__lane-wrapper {
  margin-bottom: 16px;
  margin-right: 16px;
}
.kanban-plugin__lane {
  border-radius: var(--radius-m);
  background-color: transparent;
  border: none;
}
.kanban-plugin__lane-header-wrapper {
  padding: 0 8px;
  border-bottom: none;
}
.kanban-plugin__lane-header-wrapper .kanban-plugin__lane-grip {
  margin-right: 4px;
  margin-left: -4px;
  padding: 4px;
  border-radius: var(--radius-s);
}
.kanban-plugin__lane-header-wrapper .kanban-plugin__lane-grip:hover {
  background-color: var(--background-modifier-hover);
  color: var(--text-muted);
}
.kanban-plugin__lane-header-wrapper .kanban-plugin__lane-title {
  font-size: var(--font-ui-medium);
  font-weight: var(--font-medium);
}
.kanban-plugin__lane-header-wrapper div.kanban-plugin__lane-title-count {
  border-radius: var(--radius-s);
  font-size: var(--font-ui-smaller);
}
.kanban-plugin__lane-header-wrapper button.kanban-plugin__lane-settings-button {
  box-shadow: none;
  color: var(--text-faint);
}
.kanban-plugin__lane-header-wrapper button.kanban-plugin__lane-settings-button:hover {
  background-color: var(--background-modifier-hover);
  color: var(--text-muted);
}
.kanban-plugin__scroll-container.kanban-plugin__vertical {
  border-radius: var(--radius-m) var(--radius-m) 0 0;
  border: 1px solid var(--background-modifier-border);
  border-bottom: none;
}
.kanban-plugin__lane-items {
  background-color: var(--background-secondary);
  margin: 0;
  padding: 4px 8px;
}
.kanban-plugin__item-button-wrapper {
  background-color: var(--background-secondary);
  border-radius: 0 0 var(--radius-m) var(--radius-m);
  border: 1px solid var(--background-modifier-border);
}
.kanban-plugin__item {
  border-radius: var(--radius-m);
}
.kanban-plugin__item:hover .kanban-plugin__item-title-wrapper {
  background-color: var(--layer-hover-1);
}
.kanban-plugin__item a.tag:not(.kanban-plugin__item-tag) {
  display: none;
}
.kanban-plugin__item .kanban-plugin__item-tag {
  padding: 2px 4px;
  border: 1px solid var(--interactive);
}
.kanban-plugin__item .kanban-plugin__item-tag:hover {
  padding: 2px 4px;
  border: 1px solid var(--interactive);
}
.kanban-plugin__item-content-wrapper {
  background: transparent;
}
.kanban-plugin__item-title-wrapper {
  padding: 8px;
  background: transparent;
}
.kanban-plugin__item button.kanban-plugin__item-postfix-button {
  opacity: 1;
  color: var(--text-faint);
  visibility: visible;
  box-shadow: none;
}
.kanban-plugin__item button.kanban-plugin__item-postfix-button:hover {
  background-color: var(--background-modifier-hover);
  border-radius: var(--radius-s);
  color: var(--text-muted);
}
button.kanban-plugin__new-item-button {
  padding: 8px 16px;
  background-color: transparent;
}
button.kanban-plugin__new-item-button:hover {
  background-color: var(--layer-hover-1);
  color: var(--text-muted);
}
.wrap-kanban-lanes .kanban-plugin__board > div {
  flex-wrap: wrap;
  overflow-y: scroll;
}
body:is(.is-focused, :not(.is-focused)) .workspace-leaf-content[data-type=kanban] .view-header {
  height: unset;
  background-color: var(--background-primary);
}
body:is(.is-focused, :not(.is-focused)) .workspace-leaf-content[data-type=kanban] .view-header .view-header-title {
  visibility: hidden;
}
body:is(.is-focused, :not(.is-focused)) .workspace-leaf-content[data-type=kanban] .view-header .view-header-title-container::after {
  background: none;
}
body:is(.is-focused, :not(.is-focused)) .workspace-leaf-content[data-type=kanban] .view-header .view-actions {
  position: unset;
  margin-top: unset;
}
.workspace-tab-header.is-active[data-type=kanban] {
  --tab-background-active: var(--background-primary);
}
/*!
/* @settings

name: Sanctum
id: sanctum
settings:
	-
		id: light-mode-contrast-mode
		title: Light mode contrast mode
		type: class-select
		allowEmpty: false
		default: 'sanctum-default-light'
		options:
			-
				label: Default
				value: 'sanctum-default-light'
			-
				label: Contrast
				value: 'sanctum-contrast-light'
			-
				label: White
				value: 'sanctum-white'
	-
		id: dark-mode-contrast-mode
		title: Dark mode contrast mode
		type: class-select
		allowEmpty: false
		default: 'sanctum-default-dark'
		options:
			-
				label: Default
				value: 'sanctum-default-dark'
			-
				label: Contrast
				value: 'sanctum-contrast-dark'
			-
				label: True Black
				value: 'sanctum-black'
	-			
		id: color-accent-rgb-l
		title: Accent color light mode
		type: variable-select
		default: '246, 141, 69'
		options:
			-
				label: 'Orange'
				value: '246, 141, 69'
			-
				label: 'Red'
				value: '243, 90, 55'
			-
				label: 'Pink'
				value: '236, 90, 118'
			-
				label: 'Lavender'
				value: '165, 119, 218'
			-
				label: 'Blue'
				value: '83, 112, 159'
			-
				label: 'Cyan'
				value: '99, 149, 156'
			-
				label: 'Viridian'
				value: '92, 153, 124'
			-
				label: 'Green'
				value: '102, 153, 97'
			-
				label: 'Pistachio'
				value: '141, 147, 25'
			-
				label: 'Yellow'
				value: '243, 189, 79'
	-			
		id: color-accent-rgb-d
		title: Accent color dark mode
		type: variable-select
		default: '102, 153, 97'
		options:
			-
				label: 'Orange'
				value: '246, 141, 69'
			-
				label: 'Red'
				value: '243, 90, 55'
			-
				label: 'Pink'
				value: '236, 90, 118'
			-
				label: 'Lavender'
				value: '165, 119, 218'
			-
				label: 'Blue'
				value: '83, 112, 159'
			-
				label: 'Cyan'
				value: '99, 149, 156'
			-
				label: 'Viridian'
				value: '92, 153, 124'
			-
				label: 'Green'
				value: '102, 153, 97'
			-
				label: 'Pistachio'
				value: '141, 147, 25'
			-
				label: 'Yellow'
				value: '243, 189, 79'
	-
		id: font-editor-theme
		title: Editor font
		type: variable-text
		default: '??'
    - 
        id: file-line-width
        title: Readable line width
        description: The maximum line width in rem (unit relative to Appearance -> Font Size)
        type: variable-number-slider
        default: 40
        min: 30
        max: 70
        step: 1
		format: 'rem'
	-
		id: active-line-highlighting
		title: Active line highlighting
		type: heading
		level: 1
		collapsed: true
	-
			id: active-line-accent
			title: Active Line Highlighting
			type: variable-select
			default: 'transparent'
			options:
					-
						label: 'No highlighting'
						value: 'transparent'
					-
						label: 'Subtle highlight'
						value: 'var(--background-hover)'
					-
						label: 'Accent color highlight'
						value: 'rgba(var(--color-accent-rgb), .3)'
	-
		id: asides
		title: Asides
		type: heading
		level: 1
		collapsed: true
	-
				id: aside-counter
				title: Toggle aside counter [Beta]
				type: class-toggle
	-
				id: aside-border
				title: Toggle aside borders
				type: class-toggle
	-
				id: aside-border-color
				title: Aside border color
				type: variable-themed-color
				format: rgb
				opacity: true
				default-light: '#'
				default-dark: '#'
	-
				id: aside-text-color
				title: Aside text color
				type: variable-themed-color
				format: rgb
				opacity: true
				default-light: '#'
				default-dark: '#'
	-
				id: aside-background
				title: Aside background color
				type: variable-themed-color
				format: rgb
				opacity: true
				default-light: '#'
				default-dark: '#'
	-
				id: aside-background-hover
				title: Aside background hover color
				type: variable-themed-color
				format: rgb
				opacity: true
				default-light: '#'
				default-dark: '#'
	-
		id: blockquotes
		title: Blockquotes
		type: heading
		level: 1
		collapsed: true
	-
				id: blockquote-border-color
				title: Blockquote border color
				type: variable-themed-color
				format: hex
				default-light: '#'
				default-dark: '#'
	-
				id: blockquote-border-thickness
				title: Blockquote border thickness (px)
				type: variable-number
				default: 0
				format: 'px'
	-
				id: blockquote-marker
				title: Toggle blockquote short divider
				description: Adds a visual element to the top of blockquotes. Its is affected by the border options above.
				type: class-toggle
	-
				id: blockquote-border
				title: Toggle blockquote horizontal dividers
				description: Adds 2 horizontal rules to the top and bottom of blockquotes. This option replaces blockquote short dividers.
				type: class-toggle
	-
				id: blockquote-color
				title: Blockquote text color
				type: variable-themed-color
				format: hex
				default-light: '#'
				default-dark: '#'
	-
				id: blockquote-size
				title: Blockquote text size (rem)
				type: variable-number
				default: .9
				format: 'rem'
	-
				id: blockquote-font-style
				title: Blockquote font style
				type: variable-select
				default: 'normal'
				options:
					- 'normal'
					- 'italic'
					- 'oblique'
	-
		id: callouts
		title: Callouts
		type: heading
		level: 1
		collapsed: true
	-
				id: callout-border-width
				title: Callout border width (px)
				type: variable-number
				default: 0
				format: 'px'
	-
				id: callout-border-opacity
				title: Callout border color opacity (%)
				type: variable-number
				default: 30
				format: '%'
	-
		id: code-blocks
		title: Code
		type: heading
		level: 1
		collapsed: true
	-
				id: code-lines
				title: Toggle code block line numbers
				description: Adds line numbers to code blocks
				type: class-toggle
	-
				id: code-label
				title: Toggle code block labels
				description: Adds programming language labels to code blocks
				type: class-toggle
	-
				id: code-border
				title: Code borders
				description: Adds a subtle border around code blocks
				type: class-toggle
	-
				id: code-background
				title: Code background color
				type: variable-themed-color
				format: rgb
				opacity: true
				default-light: '#'
				default-dark: '#'
	-
				id: code-normal-inline
				title: Inline Code text color
				type: variable-themed-color
				format: rgb
				opacity: true
				default-light: '#'
				default-dark: '#'
	-
				id: syntax-highlighting
				title: Syntax Highlighting color scheme
				description: This will only affect code block text colors.
				type: class-select
				allowEmpty: false
				default: '#'
				options:
					-
						label: 'Default'
						value: '#'
					-
						label: 'Nord'
						value: 'nord'
					-
						label: 'Dracula'
						value: 'dracula'
					-
						label: 'One Dark/Light'
						value: 'one-dark'
					-
						label: 'Solarized'
						value: 'solarized'
					-
						label: 'Gruvbox'
						value: 'gruvbox'
					-
						label: 'Material'
						value: 'material'
	-
			id: custom-syntax-colors
			title: Custom syntax highlighting
			description: Note that because Obsidian uses 2 different processes for code syntax in Reading and Editing view, syntax highlighting is bound to not be a 100% match between views.
			type: heading
			level: 2
			collapsed: true
	-
				id: code-normal
				title: Code normal text color
				type: variable-themed-color
				format: rgb
				opacity: true
				default-light: '#'
				default-dark: '#'
	-
				id: code-comment
				title: Code comment text color
				type: variable-themed-color
				format: rgb
				opacity: true
				default-light: '#'
				default-dark: '#'
	-
				id: code-punctuation
				title: Code punctuation text color
				type: variable-themed-color
				format: rgb
				opacity: true
				default-light: '#'
				default-dark: '#'
	-
				id: code-tag
				title: Code tag text color
				type: variable-themed-color
				format: rgb
				opacity: true
				default-light: '#'
				default-dark: '#'
	-
				id: code-value
				title: Code value text color
				type: variable-themed-color
				format: rgb
				opacity: true
				default-light: '#'
				default-dark: '#'
	-
				id: code-string
				title: Code string text color
				type: variable-themed-color
				format: rgb
				opacity: true
				default-light: '#'
				default-dark: '#'
	-
				id: code-property
				title: Code text color
				type: variable-themed-color
				format: rgb
				opacity: true
				default-light: '#'
				default-dark: '#'
	-
				id: code-function
				title: Code function text color
				type: variable-themed-color
				format: rgb
				opacity: true
				default-light: '#'
				default-dark: '#'
	-
				id: code-keyword
				title: Code keyword text color
				type: variable-themed-color
				format: rgb
				opacity: true
				default-light: '#'
				default-dark: '#'
	-
				id: code-important
				title: Code important text color
				type: variable-themed-color
				format: rgb
				opacity: true
				default-light: '#'
				default-dark: '#'
	-
		id: embeds
		title: Embeds / Transclusions
		type: heading
		level: 1
		collapsed: true
	-
				id: clean-embeds
				title: Clean embeds
				description: It is recommended to have every setting below set to default while clean embeds is toggled on.
				type: class-toggle
	-
				id: embed-background
				title: Embed background color
				type: variable-themed-color
				format: rgb
				opacity: true
				default-light: '#'
				default-dark: '#'
	-
				id: embed-border-color
				title: Embed border color
				type: variable-themed-color
				format: rgb
				opacity: true
				default-light: '#'
				default-dark: '#'
	-
				id: embed-border-width
				title: Embed left border width (px)
				type: variable-number
				default: 1
				format: 'px'
	-
		id: emphasis
		title: Emphasis
		type: heading
		level: 1
		collapsed: true
	-
				id: highlight
				title: Highlight background color
				type: variable-themed-color
				format: rgb
				opacity: true
				default-light: '#'
				default-dark: '#'
	-
				id: bold-color
				title: Bold text color
				type: variable-themed-color
				format: rgb
				opacity: true
				default-light: '#'
				default-dark: '#'
	-
				id: italic-color
				title: Italic text color
				type: variable-themed-color
				format: rgb
				opacity: true
				default-light: '#'
				default-dark: '#'
	-
		id: file-explorer
		title: File Explorer
		type: heading
		level: 1
		collapsed: true
	-
				id: hide-vault-title
				title: Hide vault title
				type: class-toggle
	-
				id: wrap-nav-titles
				title: Wrap titles
				type: class-toggle
	-
		id: headings
		title: Headings
		type: heading
		level: 1
		collapsed: true
	-
				id: heading-outline
				title: Heading outline
				description: Select the heading outline method you prefer. This will only affect Reading View. This feature is reliant on the Contextual Typography plugin.
				type: class-select
				allowEmpty: false
				default: '#'
				options:
					-
						label: 'Default'
						value: '#'
					-
						label: 'H1 is Lvl 0'
						value: 'heading-counter-0'
					-
						label: 'H1 is lvl 1'
						value: 'heading-counter-1'
	-
				id: headings-1
				title: Level 1 headings
				type: heading
				level: 2
				collapsed: true
	-	
						id: h1-font
						title: H1 font
						type: variable-text
						default: 'Inter, sans-serif'
	-	
						id: h1-color
						title: H1 color
						description: For reverting back to default text color, click "Cancel" inside the color picker, and then click the "Restore default" button next to it.
						type: variable-color
						opacity: false
						format: hsl
						default: 'hsl(24.4, 90.8%, 61.8%)'
	-
						id: h1-line
						title: H1 divider line
						type: class-toggle
	-
						id: h1-size
						title: H1 size (em)
						type: variable-number
						default: 2
						format: 'em'
	-
						id: h1-weight
						title: H1 weight
						description: Available weights depend on the font itself.
						type: variable-select
						default: '600'
						options:
							- '100'
							- '200'
							- '300'
							- '400'
							- '500'
							- '600'
							- '700'
							- '800'
							- '900'
	-
						id: h1-variant
						title: H1 font variant
						description: Available font variants depend on the font itself.
						type: variable-select
						default: 'normal'
						options:
							- 'normal'
							- 'small-caps'
							- 'all-small-caps'
							- 'petite-caps'
							- 'all-petite-caps'
							- 'titling-caps'
	-
						id: h1-style
						title: H1 font style
						description: Available font styles depend on the font itself.
						type: variable-select
						default: 'normal'
						options:
							- 'normal'
							- 'italic'
							- 'oblique'
	-
				id: headings-2
				title: Level 2 headings
				type: heading
				level: 2
				collapsed: true
	-	
						id: h2-font
						title: H2 font
						type: variable-text
						default: 'Inter, sans-serif'
	-	
						id: h2-color
						title: H2 color
						description: For reverting back to default text color, click "Cancel" inside the color picker, and then click the "Restore default" button next to it.
						type: variable-color
						opacity: false
						format: hsl
						default: 'hsl(40.2, 87.2%, 63.1%)'
	-
						id: h2-line
						title: H2 divider line
						type: class-toggle
	-
						id: h2-size
						title: H2 size (em)
						type: variable-number
						default: 1.75
						format: 'em'
	-
						id: h2-weight
						title: H2 weight
						description: Available weights depend on the font itself.
						type: variable-select
						default: '600'
						options:
							- '100'
							- '200'
							- '300'
							- '400'
							- '500'
							- '600'
							- '700'
							- '800'
							- '900'
	-
						id: h2-variant
						title: H2 font variant
						description: Available font variants depend on the font itself.
						type: variable-select
						default: 'normal'
						options:
							- 'normal'
							- 'small-caps'
							- 'all-small-caps'
							- 'petite-caps'
							- 'all-petite-caps'
							- 'titling-caps'
	-
						id: h2-style
						title: H2 font style
						description: Available font styles depend on the font itself.
						type: variable-select
						default: 'normal'
						options:
							- 'normal'
							- 'italic'
							- 'oblique'
	-
				id: headings-3
				title: Level 3 headings
				type: heading
				level: 2
				collapsed: true
	-	
						id: h3-font
						title: H3 font
						type: variable-text
						default: 'Inter, sans-serif'
	-	
						id: h3-color
						title: H3 color
						description: For reverting back to default text color, click "Cancel" inside the color picker, and then click the "Restore default" button next to it.
						type: variable-color
						opacity: false
						format: hsl
						default: 'hsl(63, 70.9%, 33.7%)'
	-
						id: h3-line
						title: H3 divider line
						type: class-toggle
	-
						id: h3-size
						title: H3 size (em)
						type: variable-number
						default: 1.5
						format: 'em'
	-
						id: h3-weight
						title: H3 weight
						description: Available weights depend on the font itself.
						type: variable-select
						default: '600'
						options:
							- '100'
							- '200'
							- '300'
							- '400'
							- '500'
							- '600'
							- '700'
							- '800'
							- '900'
	-
						id: h3-variant
						title: H3 font variant
						description: Available font variants depend on the font itself.
						type: variable-select
						default: 'normal'
						options:
							- 'normal'
							- 'small-caps'
							- 'all-small-caps'
							- 'petite-caps'
							- 'all-petite-caps'
							- 'titling-caps'
	-
						id: h3-style
						title: H3 font style
						description: Available font styles depend on the font itself.
						type: variable-select
						default: 'normal'
						options:
							- 'normal'
							- 'italic'
							- 'oblique'
	-
				id: headings-4
				title: Level 4 headings
				type: heading
				level: 2
				collapsed: true
	-	
						id: h4-font
						title: H4 font
						type: variable-text
						default: 'Inter, sans-serif'
	-	
						id: h4-color
						title: H4 color
						description: For reverting back to default text color, click "Cancel" inside the color picker, and then click the "Restore default" button next to it.
						type: variable-color
						opacity: false
						format: hsl
						default: 'hsl(151.5, 24.9%, 48%)'
	-
						id: h4-line
						title: H4 divider line
						type: class-toggle
	-
						id: h4-size
						title: H4 size (em)
						type: variable-number
						default: 1.25
						format: 'em'
	-
						id: h4-weight
						title: H4 weight
						description: Available weights depend on the font itself.
						type: variable-select
						default: '600'
						options:
							- '100'
							- '200'
							- '300'
							- '400'
							- '500'
							- '600'
							- '700'
							- '800'
							- '900'
	-
						id: h4-variant
						title: H4 font variant
						description: Available font variants depend on the font itself.
						type: variable-select
						default: 'normal'
						options:
							- 'normal'
							- 'small-caps'
							- 'all-small-caps'
							- 'petite-caps'
							- 'all-petite-caps'
							- 'titling-caps'
	-
						id: h4-style
						title: H4 font style
						description: Available font styles depend on the font itself.
						type: variable-select
						default: 'normal'
						options:
							- 'normal'
							- 'italic'
							- 'oblique'
	-
				id: headings-5
				title: Level 5 headings
				type: heading
				level: 2
				collapsed: true
	-	
						id: h5-font
						title: H5 font
						type: variable-text
						default: 'Inter, sans-serif'
	-	
						id: h5-color
						title: H5 color
						description: For reverting back to default text color, click "Cancel" inside the color picker, and then click the "Restore default" button next to it.
						type: variable-color
						opacity: false
						format: hsl
						default: 'hsl(217.1, 31.4%, 47.5%)'
	-
						id: h5-line
						title: H5 divider line
						type: class-toggle
	-
						id: h5-size
						title: H5 size (em)
						type: variable-number
						default: 1.125
						format: 'em'
	-
						id: h5-weight
						title: H5 weight
						description: Available weights depend on the font itself.
						type: variable-select
						default: '600'
						options:
							- '100'
							- '200'
							- '300'
							- '400'
							- '500'
							- '600'
							- '700'
							- '800'
							- '900'
	-
						id: h5-variant
						title: H5 font variant
						description: Available font variants depend on the font itself.
						type: variable-select
						default: 'normal'
						options:
							- 'normal'
							- 'small-caps'
							- 'all-small-caps'
							- 'petite-caps'
							- 'all-petite-caps'
							- 'titling-caps'
	-
						id: h5-style
						title: H5 font style
						description: Available font styles depend on the font itself.
						type: variable-select
						default: 'normal'
						options:
							- 'normal'
							- 'italic'
							- 'oblique'
	-
				id: headings-6
				title: Level 6 headings
				type: heading
				level: 2
				collapsed: true
	-	
						id: h6-font
						title: H6 font
						type: variable-text
						default: 'Inter, sans-serif'
	-	
						id: h6-color
						title: H6 color
						description: For reverting back to default text color, click "Cancel" inside the color picker, and then click the "Restore default" button next to it.
						type: variable-color
						opacity: false
						format: hsl
						default: 'hsl(268deg, 57%, 66%)'
	-
						id: h6-line
						title: H6 divider line
						type: class-toggle
	-
						id: h6-size
						title: H6 size (em)
						type: variable-number
						default: 1
						format: 'em'
	-
						id: h6-weight
						title: H6 weight
						description: Available weights depend on the font itself.
						type: variable-select
						default: '600'
						options:
							- '100'
							- '200'
							- '300'
							- '400'
							- '500'
							- '600'
							- '700'
							- '800'
							- '900'
	-
						id: h6-variant
						title: H6 font variant
						description: Available font variants depend on the font itself.
						type: variable-select
						default: 'normal'
						options:
							- 'normal'
							- 'small-caps'
							- 'all-small-caps'
							- 'petite-caps'
							- 'all-petite-caps'
							- 'titling-caps'
	-
						id: h6-style
						title: H6 font style
						description: Available font styles depend on the font itself.
						type: variable-select
						default: 'normal'
						options:
							- 'normal'
							- 'italic'
							- 'oblique'
	-
		id: indentation-guides
		title: Indentation guides
		type: heading
		level: 1
		collapsed: true
	-
				id: indentation-guide-active
				title: Indentation guide active color
				type: variable-select
				default: 'rgba(var(--mono-rgb-100), 0.3)'
				options:
					-
						label: Default
						value: 'rgba(var(--mono-rgb-100), 0.3)'
					-
						label: Accent color
						value: 'var(--interactive)'
	-
		id: links
		title: Links
		type: heading
		level: 1
		collapsed: true
	-
			id: internal-links
			title: Internal links
			type: heading
			level: 2
			collapsed: false
	-
					id: link-color
					title: Internal link color
					type: variable-themed-color
					format: rgb
					opacity: false
					default-light: 'rgb(var(--interactive-accent-rgb))'
					default-dark: 'rgb(var(--interactive-accent-rgb))'
	-
					id: link-color-hover
					title: Internal link color (hover)
					type: variable-themed-color
					format: rgb
					opacity: false
					default-light: 'rgb(var(--interactive-accent-rgb))'
					default-dark: 'rgb(var(--interactive-accent-rgb))'
	-
					id: link-decoration
					title: Internal link decoration
					type: variable-select
					default: 'underline'
					options:
						-
							label: Underline
							value: 'underline'
						-
							label: None
							value: 'none'
	-
					id: link-decoration-hover
					title: Internal link decoration (hover)
					type: variable-select
					default: 'underline'
					options:
						-
							label: Underline
							value: 'underline'
						-
							label: None
							value: 'none'
	-
				id: link-style
				title: Internal link style
				type: variable-select
				default: 'normal'
				options:
					- 'normal'
					- 'italic'
	-
				id: link-text-transform
				title: Internal link text transform
				type: variable-select
				default: 'none'
				options:
					- 'none'
					- 'capitalize'
					- 'uppercase'
					- 'lowercase'
	-
			id: external-links
			title: External links
			type: heading
			level: 2
			collapsed: false
	-
					id: link-external-color
					title: External link color
					type: variable-themed-color
					format: rgb
					opacity: false
					default-light: 'rgb(var(--interactive-accent-rgb))'
					default-dark: 'rgb(var(--interactive-accent-rgb))'
	-
					id: link-external-color-hover
					title: External link color (hover)
					type: variable-themed-color
					format: rgb
					opacity: false
					default-light: 'rgb(var(--interactive-accent-rgb))'
					default-dark: 'rgb(var(--interactive-accent-rgb))'
	-
					id: link-external-decoration
					title: External link decoration
					type: variable-select
					default: 'underline'
					options:
						-
							label: Underline
							value: 'underline'
						-
							label: None
							value: 'none'
	-
					id: link-external-decoration-hover
					title: External link decoration (hover)
					type: variable-select
					default: 'underline'
					options:
						-
							label: Underline
							value: 'underline'
						-
							label: None
							value: 'none'
	-
				id: link-external-style
				title: External link style
				type: variable-select
				default: 'normal'
				options:
					- 'normal'
					- 'italic'
	-
		id: lists
		title: Lists & Tasks
		type: heading
		level: 1
		collapsed: true
	-			
			id: checkbox-color-rgb
			title: Checkbox color
			type: variable-select
			default: '102, 153, 97'
			options:
				-
					label: 'Orange'
					value: '246, 141, 69'
				-
					label: 'Red'
					value: '197, 65, 40'
				-
					label: 'Pink'
					value: '236, 90, 118'
				-
					label: 'Lavender'
					value: '165, 119, 218'
				-
					label: 'Blue'
					value: '83, 112, 159'
				-
					label: 'Cyan'
					value: '99, 149, 156'
				-
					label: 'Viridian'
					value: '92, 153, 124'
				-
					label: 'Green'
					value: '102, 153, 97'
				-
					label: 'Pistachio'
					value: '141, 147, 25'
				-
					label: 'Yellow'
					value: '243, 189, 79'
	-
				id: checkbox-radius
				title: Checkbox shape
				type: variable-select
				allowEmpty: false
				default: '16px'
				options:
					-
						label: circle
						value: '16px'
					-
						label: square
						value: '2px'
	-
				id: checklist-done-decoration
				title: Strike completed tasks
				type: variable-select
				default: 'line-through'
				options:
					-
						label: Strike
						value: 'line-through'
					-
						label: No strike
						value: 'none'
	-
				id: checklist-done-color
				title: Completed task text color
				type: variable-select
				default: 'var(--text-faint)'
				options:
					-
						label: Default
						value: 'var(--text-faint)'
					-
						label: Normal text
						value: 'var(--text-normal)'
	-
				id: checklist-done-strike-color
				title: Completed task text strike color
				type: variable-select
				default: 'var(--text-faint)'
				options:
					-
						label: Default
						value: 'var(--text-faint)'
					-
						label: Normal text
						value: 'var(--text-normal)'
					-
						label: Red
						value: 'var(--red)'
	-
				id: list-indent
				title: Nested list indentation (em)
				type: variable-number
				default: 1.5
				format: 'em'
	-
				id: list-spacing
				title: List item spacing (em)
				type: variable-number
				default: 0.075
				format: 'em'
	-
				id: odd-marker
				title: Odd numbered list marker style
				type: class-select
				allowEmpty: false
				default: 'default-marker-odd'
				options:
					-
						label: em-dash
						value: 'default-marker-odd'
					-
						label: hyphen
						value: 'alternate-marker-odd-1'
					-
						label: bullet operator
						value: 'alternate-marker-odd-2'
					-
						label: square bullet
						value: 'alternate-marker-odd-3'
					-
						label: bullet
						value: 'alternate-marker-odd-4'
					-
						label: ring
						value: 'alternate-marker-odd-5'
					-
						label: triangule
						value: 'alternate-marker-odd-6'
	-
				id: even-marker
				title: Odd numbered list marker style
				type: class-select
				allowEmpty: false
				default: 'default-marker-even'
				options:
					-
						label: em-dash
						value: 'alternate-marker-even-1'
					-
						label: hyphen
						value: 'alternate-marker-even-2'
					-
						label: bullet operator
						value: 'alternate-marker-even-3'
					-
						label: square bullet
						value: 'default-marker-even'
					-
						label: bullet
						value: 'alternate-marker-even-4'
					-
						label: ring
						value: 'alternate-marker-even-5'
					-
						label: triangule
						value: 'alternate-marker-even-6'
	-
				id: step-list
				title: Ordered list marker style
				description: This feature will only affect Reading View.
				type: class-select
				allowEmpty: false
				default: '#'
				options:
					-
						label: 'Default'
						value: '#'
					-
						label: 'Step list'
						value: 'step-list-0'
					-
						label: 'Outlined List'
						value: 'step-list-1'
	-
		id: tables
		title: Tables
		type: heading
		level: 1
		collapsed: true
	-
				id: table-cell-border
				title: Cell borders
				type: class-toggle
	-
				id: table-row-border
				title: Row lines
				type: class-toggle
	-
				id: table-column-border
				title: Column lines
				type: class-toggle
	-
				id: table-alternate-row
				title: Striped rows
				type: class-toggle
	-
				id: table-alternate-column
				title: Striped columns
				type: class-toggle
	-
				id: table-hover-row
				title: Disable active row highlighting
				type: class-toggle
	-
				id: table-hover-highlight-color
				title: Active row highlight color
				type: variable-themed-color
				format: rgb
				opacity: true
				default-dark: 'rgba(var(--interactive-accent-rgb), 0.15)'
				default-light: 'rgba(var(--interactive-accent-rgb), 0.15)'
	-
				id: table-single-rows
				title: Disable row text wrap
				type: class-toggle
	-
				id: table-sticky-headers
				title: Sticky headers
				type: class-toggle
	-
				id: table-sticky-height
				title: Sticky header table max height (px)
				type: variable-number
				default: 600
				format: 'px'
	-
				id: table-tabular-figures
				title: Tabular figures
				type: class-toggle
	-
				id: table-nums
				title: Row numbers
				type: class-toggle
	-
		id: tags
		title: Tags
		type: heading
		level: 1
		collapsed: true
	-
				id: tag-radius
				title: Tag Shape
				type: variable-select
				default: '2em'
				allowEmpty: false
				options:
					-
						label: 'Pill'
						value: '2em'
					-
						label: 'Rounded'
						value: '4px'
					-
						label: 'Square'
						value: '0em'
	-
				id: tag-border-width
				title: Tag border
				type: variable-select
				default: '0px'
				allowEmpty: false
				options:
					-
						label: 'None'
						value: '0px'
					-
						label: 'Thin'
						value: '1px'
					-
						label: 'Thick'
						value: '2px'
	-
				id: tag-color
				title: Tag text color
				type: variable-themed-color
				format: rgb
				opacity: false
				default-light: 'rgb(var(--interactive-accent-rgb))'
				default-dark: 'rgb(var(--interactive-accent-rgb))'
	-
				id: tag-color-hover
				title: Tag text color (hover)
				type: variable-themed-color
				format: rgb
				opacity: false
				default-light: 'rgb(var(--interactive-accent-rgb))'
				default-dark: 'rgb(var(--interactive-accent-rgb))'
	-
				id: tag-background
				title: Tag background color
				type: variable-themed-color
				format: rgb
				opacity: true
				default-light: 'rgba(var(--interactive-accent-rgb), 0.15)'
				default-dark: 'rgba(var(--interactive-accent-rgb), 0.15)'
	-
				id: tag-background-hover
				title: Tag background color (hover)
				type: variable-themed-color
				format: rgb
				opacity: true
				default-light: 'rgba(var(--interactive-accent-rgb), 0.25)'
				default-dark: 'rgba(var(--interactive-accent-rgb), 0.25)'
	-
				id: tag-decoration
				title: Tag text decoration
				type: variable-select
				default: 'none'
				allowEmpty: false
				options:
					-
						label: 'None'
						value: 'none'
					-
						label: 'Thin underline'
						value: 'underline 1px'
					-
						label: 'Thick underline'
						value: 'underline 2px'
	-
				id: tag-decoration-hover
				title: Tag text decoration (hover)
				type: variable-select
				default: 'none'
				allowEmpty: false
				options:
					-
						label: 'None'
						value: 'none'
					-
						label: 'Thin underline'
						value: 'underline 1px'
					-
						label: 'Thick underline'
						value: 'underline 2px'
	-
		id: tab-title-bar
		title: Tab title bar
		type: heading
		level: 1
		collapsed: true
	-
				id: sticky-view-actions
				title: Disable floating tab title bar buttons
				description: Reveals the entirety of the tab title bar, like native obsidian.
				type: class-toggle
	-
		id: tab-nav-items
		title: Navigation Items
		type: heading
		level: 1
		collapsed: true
	-
				id: nav-folder-indicators
				title: Replace folder icons with regular chevron/arrows
				type: class-toggle
	-
				id: colorful-active-nav
				title: Disable colorful active nav items
				type: class-toggle
	-
		id: plugins
		title: Plugins
		type: heading
		level: 1
		collapsed: true
	-
			id: calendar
			title: Calendar
			type: heading
			level: 2
			collapsed: true
	-
				id: stendig
				title: Stendig-like layout [Beta]
				description: Calendar layout inspired by Massimo Vignelli's famous Stendig calendars
				type: class-toggle
	-
				id: calendar-flip
				title: Flip calendar Month + Year title
				type: class-toggle
	-
				id: calendar-dot
				title: Calendar dot color
				type: variable-themed-color
				format: hex
				default-light: '#'
				default-dark: '#'
	-
				id: calendar-dot-hover
				title: Calendar dot color on hover
				type: variable-themed-color
				format: hex
				default-light: '#'
				default-dark: '#'
	-
			id: kanban
			title: Kanban
			type: heading
			level: 2
			collapsed: true
	-
				id: wrap-kanban-lanes
				title: Wrap kanban lanes
				type: class-toggle
	-
		id: advanced
		title: Advanced Settings
		type: heading
		level: 1
		collapsed: false
	-
			id: no-sanctum-icons
			title: Disable custom icons
			type: class-toggle
*/
/* @plugins
core:
- audio-recorder
- backlink
- command-palette
- daily-notes
- editor-status
- file-explorer
- file-recovery
- global-search
- graph
- outgoing-link
- outline
- page-preview
- starred
- switcher
- sync
- tag-pane

community:
- calendar
- dataview
- obsidian-style-settings
- obsidian-kanban

*/


