.plugin-text-format H3,
.plugin-text-format H4 {
  margin-bottom: 0px;
  padding-bottom: 0px;
}
.plugin-text-format H4{
  opacity: 0.7;
}
.plugin-text-format .heading-description {
  padding-top: 0px;
  color: var(--text-faint);
}

.plugin-text-format .header-div:has(H3) + .tf-collapsible-content {
  margin-top: 10px;
}
.plugin-text-format .header-div:has(H4) + .tf-collapsible-content {
  margin-top: 5px;
}

.plugin-text-format .setting-item > .setting-item-control > input {
  min-width: 145px;
}

.plugin-text-format .setting-item.custom-replace input,
.plugin-text-format .setting-item.wrapper input {
  width: 33%;
}
.plugin-text-format .setting-item.api-request input:first-child {
  width: 30%;
}
.plugin-text-format .setting-item.api-request input {
  width: 70%;
}

.tf-collapsible-content.is-active {
  display: block;
}

.tf-collapsible-content {
  display: none;
}

.plugin-text-format .header-div:hover {
  background-color: var(--interactive-hover, #363636);
  border-radius: var(--button-radius, 5px);
}

.tf-collapsible-header {
  padding-bottom: 8px;
  padding-top: 4px;
}

.tf-collapsible-icon {
  position: relative;
  top: 4px;
  left: 16px;
}
