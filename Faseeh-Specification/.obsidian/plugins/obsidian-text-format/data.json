{"manifest": {"version": "3.1.0"}, "MergeParagraph_Newlines": true, "MergeParagraph_Spaces": true, "LowercaseFirst": true, "RemoveBlanksWhenChinese": false, "ZoteroNoteRegExp": "“(?<text>.*)” \\((?<item>.*?)\\) \\(\\[pdf\\]\\((?<pdf_url>.*?)\\)\\)", "ZoteroNoteTemplate": "{text} [🔖]({pdf_url})", "BulletPoints": "•–§", "WrapperList": [{"name": "underline", "prefix": "<u>", "suffix": "</u>", "id": "underline"}], "RequestList": [], "customReplaceList": [{"id": "remove-trailing-spaces", "name": "Remove trailing spaces", "data": [{"search": "(\\s*)(?=\\n)|(\\s*)$", "replace": ""}]}, {"id": "remove-blank-line", "name": "Remove blank line(s)", "data": [{"search": "\\n\\s*\\n", "replace": "\\n"}]}, {"id": "add-line-break", "name": "Add extra line break between paragraphs", "data": [{"search": "\\n", "replace": "\\n\\n"}]}, {"id": "split-lines-by-blank", "name": "Split line(s) by blanks", "data": [{"search": " ", "replace": "\\n"}]}], "customReplaceBuiltInLog": {"remove-trailing-spaces": {"id": "remove-trailing-spaces", "modified": false, "data": [{"search": "(\\s*)(?=\\n)|(\\s*)$", "replace": ""}]}, "remove-blank-line": {"id": "remove-blank-line", "modified": false, "data": [{"search": "\\n\\s*\\n", "replace": "\\n"}]}, "add-line-break": {"id": "add-line-break", "modified": false, "data": [{"search": "\\n", "replace": "\\n\\n"}]}, "split-lines-by-blank": {"id": "split-lines-by-blank", "modified": false, "data": [{"search": " ", "replace": "\\n"}]}}, "ToggleSequence": "titleCase\nlowerCase\nupperCase", "RemoveWikiURL2": false, "WikiLinkFormat": {"headingOnly": "{title} (> {heading})", "aliasOnly": "{alias} ({title})", "both": "{alias} ({title} > {heading})"}, "UrlLinkFormat": "{text}", "ProperNoun": "", "OrderedListOtherSeparator": "", "Wikilink2mdRelativePath": "relative-obsidian", "calloutType": "NOTE", "debugMode": false, "headingLevelMin": 0, "calloutTypeDecider": "previous-content", "formatOnSaveSettings": {"enabled": false, "commandsString": ""}}