{"anuppuccin-theme-settings@@anp-active-line": "anp-current-line", "anuppuccin-theme-settings@@anp-custom-checkboxes": true, "anuppuccin-theme-settings@@anp-list-toggle": true, "anuppuccin-theme-settings@@anp-table-toggle": false, "anuppuccin-theme-settings@@anp-toggle-preview": true, "anuppuccin-theme-settings@@anp-alt-rainbow-style": "anp-simple-rainbow-color-toggle", "anuppuccin-theme-settings@@anp-layout-select": "anp-border-layout", "blue-topaz-theme@@transparent-setting-panel": false, "blue-topaz-theme@@color-scheme-options": "color-scheme-options-default", "blue-topaz-theme@@background-settings-workplace-background-image": false, "blue-topaz-theme@@background-image-settings-switch": false, "blue-topaz-theme@@layout-style-options": "layout-style-options-default", "blue-topaz-theme@@left-ribbon-style": "default-left-ribbon-style", "shiba-theme-settings@@shiba-colors-theme-dark": "Lilac-dark", "shimmering-focus@@bg-hue-light": 230, "shimmering-focus@@bg-hue-dark": 230, "shimmering-focus@@highlight-hue-1": 360, "catppuccin-theme-settings@@catppuccin-theme-dark": "ctp-mocha", "catppuccin-theme-settings@@catppuccin-theme-accents": "ctp-full-palette", "catppuccin-interface-styles@@ctp-bold-folder-title": true, "anuppuccin-theme-settings@@anp-rainbow-file-toggle": false, "anuppuccin-theme-settings@@anp-full-rainbow-text-color-toggle-light": false, "anuppuccin-theme-settings@@anp-full-rainbow-text-color-toggle-dark": false, "anuppuccin-theme-settings@@anp-color-transition-toggle": false, "anuppuccin-theme-settings@@anp-button-metadata-toggle": false, "anuppuccin-theme-settings@@anp-print": false, "anuppuccin-theme-settings@@anuppuccin-theme-light": "ctp-latte", "anuppuccin-theme-settings@@anuppuccin-light-theme-accents": "none", "anuppuccin-theme-settings@@anuppuccin-theme-accents": "ctp-accent-flamingo", "anuppuccin-theme-settings@@anuppuccin-accent-toggle": true, "anuppuccin-theme-settings@@anp-simple-rainbow-title-toggle": true, "anuppuccin-theme-settings@@anp-simple-rainbow-icon-folder-toggle": false, "anuppuccin-theme-settings@@anp-simple-rainbow-indentation-toggle": true, "anuppuccin-theme-settings@@anp-simple-rainbow-collapse-toggle": false, "anuppuccin-theme-settings@@anp-simple-rainbow-icon-toggle": false, "anuppuccin-theme-settings@@anp-rainbow-subfolder-color-toggle": false, "anuppuccin-theme-settings@@anp-status-bar-select": "none", "anuppuccin-theme-settings@@anp-collapse-folders": true, "anuppuccin-theme-settings@@anp-cursor": "pointer", "anuppuccin-theme-settings@@anp-autohide-titlebar": false, "anuppuccin-theme-settings@@anp-toggle-metadata": false, "anuppuccin-theme-settings@@anp-toggle-scrollbars": true, "anuppuccin-theme-settings@@anp-hide-status-bar": false, "anuppuccin-theme-settings@@anp-tooltip-toggle": false, "anuppuccin-theme-settings@@anp-file-icons": true, "anuppuccin-theme-settings@@anp-floating-header": false, "anuppuccin-theme-settings@@anp-custom-vault-toggle": true, "anuppuccin-theme-settings@@anp-file-label-align": "0", "anuppuccin-theme-settings@@anp-background-image-toggle": false, "anuppuccin-theme-settings@@anp-alt-tab-style": "anp-default-tab", "anuppuccin-theme-settings@@anp-disable-newtab-align": true, "anuppuccin-theme-settings@@anp-bg-fix": false, "anuppuccin-theme-settings@@anp-hide-borders": false, "anuppuccin-theme-settings@@anp-colorful-frame": false, "anuppuccin-theme-settings@@anp-colorful-frame-icon-toggle-light": false, "anuppuccin-theme-settings@@anp-colorful-frame-icon-toggle-dark": false, "anuppuccin-theme-settings@@anp-canvas-dark-bg": true, "vauxhall@@main-color": "vauxhall-blue", "vauxhall@@color-intensity": "intensity-standard", "vauxhall@@header-color-type": "headers-gradient-cyan-to-purple", "vauxhall@@h1-accent": false}