/* In the name of <PERSON> */

.cm-line
/* Fixes most elements of the editor */
,
.inline-title
/* Fixes the big note title inside the view */
,
.view-header-title

/* File title above editor or reading view for Obsidian v0.15.*/
    {
    unicode-bidi: plaintext !important;
}

/* Fixes th elements */
th {
    text-align: -webkit-auto !important;
}

/* Fixes the bullet points margin with text in reading mode */
.rtl-bullet-point::after {
    margin-left: 20px !important;
}

/* Fixes RTL bullet point direction for ITS theme */
.rtlListItem::before {
    float: right !important;
    margin-left: unset !important;
}

/* Fixes the RTL checklists bad view */
input.task-list-item-checkbox {
    margin-left: 6px !important;
}

/* Moves copy button for RTL code blocks to the left in reading view */
.rtlPre>button {
    right: unset !important;
    left: 0 !important;
}

/* Fixes the code blocks indentation for RTL lines in editing view */
.markdown-source-view.mod-cm6 .cm-line.HyperMD-codeblock {
    padding-right: var(--size-4-4) !important;
}

/* Fixes the margin between bullet point & text in RTL bullet point (editing view) */
body:not(.is-mobile) .markdown-source-view.mod-cm6 .list-bullet:after {
    left: unset !important;
}

/* Fixes the indent border direction for RTL lists (Reading view) */
.rtlList::before {
    left: unset !important;
    right: 0px !important;
}

/* Fixes list indentation border margin with RTL text */
.markdown-source-view.mod-cm6 .cm-indent::before {
    transform: translateX(10px) !important;
}

/* Fixes the line break problem with Tasks plugin */
.tasks-list-text>div {
    display: inline !important;
}

.tasks-list-text>div>div {
    display: inline !important;
}

/* Fixes the Tasks plugin RTL problem */
/* .plugin-tasks-list-item {
    direction: rtl !important;
    text-align: right !important;
} */ 
/* UNCOMMENT FOR RTL Tasks Plugin */