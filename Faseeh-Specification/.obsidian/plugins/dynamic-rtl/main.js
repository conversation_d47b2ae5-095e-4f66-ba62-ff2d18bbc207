/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// main.ts
var main_exports = {};
__export(main_exports, {
  default: () => DynamicRTL
});
module.exports = __toCommonJS(main_exports);
var import_obsidian = require("obsidian");
var DynamicRTL = class extends import_obsidian.Plugin {
  async onload() {
    const RTLRegEx = /[\u0591-\u07FF\u200F\u202B\u202E\uFB1D-\uFDFD\uFE70-\uFEFC]/;
    this.registerMarkdownPostProcessor((container, context) => {
      container.querySelectorAll("p,div.cm-line,h1,h2,h3,h4,h5,h6,div.callout-title-inner").forEach((element) => {
        element.setAttribute("dir", "auto");
      });
      container.querySelectorAll("table,ol,ul,pre").forEach((element) => {
        var _a;
        (_a = element.parentElement) == null ? void 0 : _a.setAttribute("dir", "auto");
      });
      container.querySelectorAll(".callout-title").forEach((element) => {
        if (RTLRegEx.test(element.innerText.charAt(0))) {
          element.style.direction = "rtl";
        }
      });
      container.querySelectorAll("blockquote").forEach((element) => {
        if (RTLRegEx.test(element.innerText.charAt(1))) {
          element.style.borderLeft = "0";
          element.style.borderRight = "var(--blockquote-border-thickness) solid var(--blockquote-border-color)";
          element.style.marginRight = "23px";
          const innerContent = element.querySelector("p");
          if (innerContent) {
            innerContent.style.marginRight = "23px";
          }
        }
      });
      container.querySelectorAll("li").forEach((element) => {
        if (RTLRegEx.test(element.innerText.charAt(0))) {
          element.querySelectorAll(".list-bullet").forEach((bullet) => {
            bullet.style.float = "right";
            bullet.classList.add("rtl-bullet-point");
          });
          element.style.textAlign = "right";
          element.style.direction = "rtl";
          element.classList.add("rtlListItem");
        } else {
          element.style.textAlign = "left";
          element.style.direction = "ltr";
        }
      });
      container.querySelectorAll("h1,h2,h3,h4,h5,h6").forEach((element) => {
        if (RTLRegEx.test(element.innerText.charAt(0))) {
          const icon = element.querySelector("div");
          if (icon) {
            icon.style.marginRight = "-22px";
            icon.style.float = "right";
          }
        }
      });
      container.querySelectorAll("ol,ul").forEach((element) => {
        if (RTLRegEx.test(element.innerText.charAt(1))) {
          element.classList.add("rtlList");
        }
      });
      container.querySelectorAll("p").forEach((element) => {
        let biDiParagraph = "";
        element.innerHTML.split("<br>").forEach((line) => {
          biDiParagraph += `<div dir="auto">${line}</div>`;
        });
        element.innerHTML = biDiParagraph;
      });
      container.querySelectorAll("code").forEach((element) => {
        if (element.classList.length == 0) {
          let biDiCode = "";
          const lineList = element.innerHTML.split("\n");
          if (lineList.length > 1) {
            lineList.forEach((line, index, array) => {
              if (index != array.length - 1) {
                biDiCode += `<div dir="auto">${line}</div>`;
              }
            });
            element.innerHTML = biDiCode;
          }
        }
      });
      container.querySelectorAll("pre").forEach((element) => {
        if (RTLRegEx.test(element.innerText.charAt(0))) {
          element.classList.add("rtlPre");
        }
      });
      container.querySelectorAll("a.external-link").forEach((element) => {
        if (RTLRegEx.test(element.innerText.charAt(0))) {
          element.style.backgroundPosition = "center left";
          element.style.paddingRight = "unset";
          element.style.paddingLeft = "16px";
        }
      });
    });
  }
};
//# sourceMappingURL=data:application/json;base64,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
