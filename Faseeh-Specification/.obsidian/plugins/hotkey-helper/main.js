var M=Object.defineProperty;var Z=Object.getOwnPropertyDescriptor;var Q=Object.getOwnPropertyNames;var X=Object.prototype.hasOwnProperty;var Y=(i,s)=>{for(var t in s)M(i,t,{get:s[t],enumerable:!0})},R=(i,s,t,e)=>{if(s&&typeof s=="object"||typeof s=="function")for(let n of Q(s))!X.call(i,n)&&n!==t&&M(i,n,{get:()=>s[n],enumerable:!(e=Z(s,n))||e.enumerable});return i},K=(i,s,t)=>(R(i,s,"default"),t&&R(t,s,"default"));var tt=i=>R(M({},"__esModule",{value:!0}),i);var ct={};Y(ct,{default:()=>I});module.exports=tt(ct);var w=require("obsidian");function E(i,s){let t=Object.keys(s).map(e=>et(i,e,s[e]));return t.length===1?t[0]:function(){t.forEach(e=>e())}}function et(i,s,t){let e=i[s],n=i.hasOwnProperty(s),o=t(e);return e&&Object.setPrototypeOf(o,e),Object.setPrototypeOf(r,o),i[s]=r,c;function r(...l){return o===e&&i[s]===r&&c(),o.apply(this,l)}function c(){i[s]===r&&(n?i[s]=e:delete i[s]),o!==e&&(o=e,Object.setPrototypeOf(r,e||Function))}}function F(i,s){return i.then(s,s)}function N(i){let s=Promise.resolve();function t(...e){return s=new Promise((n,o)=>{F(s,()=>{i.apply(this,e).then(n,o)})})}return t.after=function(){return s=new Promise((e,n)=>{F(s,e)})},t}var d={};K(d,require("obsidian"));var B=Symbol.for("v1.to-use.peak-dev.org"),H=Symbol.for("v1.factory.to-use.peak-dev.org"),S,T,b=function(){return Object.defineProperties(i(),{this:{get(){if(S)return S;throw new TypeError("No current context")}},me:{value:B},factory:{value:H}});function i(n){let o=new Map;o.prev=n;let r=Object.assign(n?l=>{let a=o.get(l);if(!a){for(let f=o.prev;f;f=f.prev)if(a=f.get(l)){a=Object.assign(Object.assign({},a),{s:a.s||1});break}a=a||{s:2,v:t},o.set(l,a)}let p,g,y;for(;;)switch(a.s){case 0:return S===r&&T&&T.push(l),a.v;case 1:if(p=a.d,!p||c(()=>p.k.every(f=>r(f)===p.c(f)))){a.s=0;break}a.v=p.f;case 2:a.s=4;try{s(o,l,0,c(g=a.v,l,y=[])),y.length&&(a.d={c:r,f:g,k:y});break}catch(f){a.s=3,a.v=f,a.d=null}case 3:throw a.v;case 4:throw new Error(`Factory ${String(a.v)} didn't resolve ${String(l)}`)}}:l=>b.this(l),{def(l,a){return s(o,l,2,a),r},set(l,a){return s(o,l,1,a),r},fork(l){let a=i(o);return l!=null?a(l):a}});return n?r.use=r:r;function c(l,a,p){let g=S,y=T;try{return S=r,T=p,l(a)}finally{S=g,T=y}}}function s(n,o,r,c){if(n.has(o)){let l=n.get(o);if(!l.s)throw new Error(`Already read: ${String(o)}`);l.s=r,l.v=c,l.d=null}else n.set(o,{s:r,v:c})}function t(n){if(typeof n[B]=="function")return n[B](n);if(e(n))return typeof n.prototype[H]=="function"?n.prototype[H]():new n;throw new ReferenceError(`No config for ${String(n)}`)}function e(n){return typeof n=="function"&&n.prototype!==void 0&&(Object.getPrototypeOf(n.prototype)!==Object.prototype||Object.getOwnPropertyNames(n.prototype).length>1||n.toString().startsWith("class"))}}();var $,C=($=window.queueMicrotask)!=null?$:(i=>s=>i.then(s))(Promise.resolve());b.def(d.Plugin,()=>{throw new Error("Plugin not created yet")});var P=class extends d.Component{constructor(){super(...arguments);this.use=b.service(this)}};b.service=function(i){return b(L).addChild(i),b.this};b.plugin=function(i){let s=b.fork().set(d.Plugin,i).set(i.constructor,i);return i.addChild(s.use(L)),s};var L=class extends d.Component{constructor(){super(...arguments);this.children=new Set([this])}onload(){this.loaded=!0}onunload(){this.loaded=!1,this.children.clear()}addChild(t){return this.children.has(t)||(this.children.add(t),this.loaded?C(()=>super.addChild(t)):super.addChild(t)),t}};function W(i,s){i._loaded&&i.removeChild(s)}function z(i,s){let t=new d.Component;t.onload=()=>{W(i,t),s()},i.addChild(t)}function q(i){let s=app.workspace;switch(i==null?void 0:i.getRoot()){case s.rootSplit:case s.floatingSplit:case s.leftSplit:case s.rightSplit:return!0;default:return!1}}var A=2,Dt=Symbol.for(`v${A}.layout-storage-events.ophidian.peak-dev.org`);var jt=`ophidian-layout-storage:v${A}:item-load`,Kt=`ophidian-layout-storage:v${A}:item-save`;var _=class extends d.Component{constructor(t,e){super();this.use=t;this.container=e;this.win=this.container.win}[b.factory](){return new D(this.constructor)}static onload(t){}static onunload(t){}},D=class extends P{constructor(t){super();this.factory=t;this.instances=new Map;this.watching=!1;this.layoutReadyCallbacks=[]}onload(){var t,e;this.registerEvent(app.workspace.on("layout-change",()=>{app.workspace.layoutReady&&this.layoutReadyCallbacks.length&&(this.layoutReadyCallbacks.forEach(C),this.layoutReadyCallbacks=[])})),(e=(t=this.factory).onload)==null||e.call(t,this.use)}onLeafChange(t,e){return this.onLayoutReady(()=>t.call(e,app.workspace.activeLeaf)),app.workspace.on("active-leaf-change",n=>{app.workspace.layoutReady&&t.call(e,n)})}onLayoutReady(t){app.workspace.layoutReady?C(t):this.layoutReadyCallbacks.push(t)}onunload(){var t,e;(e=(t=this.factory).onunload)==null||e.call(t,this.use)}watch(){if(!this._loaded)z(this,()=>this.watch());else if(!this.watching){let{workspace:t}=app,e=this;this.watching=!0,this.registerEvent(t.on("window-open",n=>{this.onLayoutReady(()=>this.forContainer(n))})),this.register(E(t,{clearLayout(n){return async function(){try{return await n.call(this)}finally{e.onLayoutReady(()=>e.forAll())}}}})),this.onLayoutReady(()=>this.forAll())}return this}forWindow(t=(n=>(n=window.activeWindow)!=null?n:window)(),e=!0){let o=it(t);if(o)return this.forContainer(o,e)}forContainer(t,e=!0){t=t.getContainer();let n=this.instances.get(t);return!n&&e&&(n=new this.factory(this.use,t),n&&(this.instances.set(t,n),this.addChild(n),t.component.addChild(n),n.register(()=>{W(this,n),W(t.component,n),this.instances.delete(t)}))),n}forDom(t,e=!0){return this.forWindow(ot(t),e)}forLeaf(t=app.workspace.activeLeaf,e=!0){if(q(t))return this.forContainer(t.getContainer(),e)}forView(t,e=!0){return this.forLeaf(t.leaf,e)}forAll(t=!0){return nt().map(e=>this.forContainer(e,t)).filter(e=>e)}};function nt(){return[app.workspace.rootSplit].concat(app.workspace.floatingSplit.children)}function ot(i){return i.win||(i.ownerDocument||i).defaultView||window}function it(i){if(i===window)return app.workspace.rootSplit;let{floatingSplit:s}=app.workspace;if(s){for(let t of s.children)if(i===t.win)return t}}function G(i,s,t,e,n){return i.on(s,t,e,n),()=>i.off(s,t,e,n)}function O(){let i,s,t=new Promise((e,n)=>{i=e,s=n});return{resolve:i,reject:s,promise:t}}function j(i,s,t,e){let{resolve:n,promise:o}=O(),r=new class extends d.FuzzySuggestModal{getItemText(l){var a;return(a=s==null?void 0:s(l))!=null?a:""+l}getItems(){return i}onChooseItem(l,a){n({item:l,event:a})}onClose(){super.onClose(),C(()=>n({item:null,event:null}))}}(app);return t&&r.setPlaceholder(t),e==null||e(r),r.open(),o}function rt(i){return w.Keymap.compileModifiers(i.modifiers)+","+i.key.toLowerCase()}function at(i){return i==="plugins"||i==="community-plugins"}function U(){var i;return J()&&at((i=app.setting.activeTab)==null?void 0:i.id)}function J(){return app.setting.containerEl.parentElement!==null}function lt(i){return i instanceof w.Modal&&i.hasOwnProperty("autoload")&&typeof i.showPlugin=="function"&&typeof i.updateSearch=="function"&&typeof i.searchEl=="object"}function ut(i){var s;return i instanceof w.Modal&&typeof i.setAutoOpen=="function"&&typeof((s=i.search)==null?void 0:s.inputEl)=="object"}var I=class extends w.Plugin{constructor(){super(...arguments);this.lastSearch={};this.hotkeyButtons={};this.globalsAdded=!1;this.searchInput=null;this.commandsByPlugin={};this.assignedKeyCount={}}onload(){let t=this.app.workspace,e=this,n=t;this.registerEvent(n.on("plugin-settings:before-display",(c,l)=>{this.hotkeyButtons={},this.globalsAdded=!1,this.searchInput=null;let a=E(w.Setting.prototype,{addSearch(p){return function(g){return a(),p.call(this,y=>{e.searchInput=y,g==null||g(y)})}}});C(a)})),this.registerEvent(n.on("plugin-settings:after-display",()=>this.refreshButtons(!0))),this.registerEvent(n.on("plugin-settings:plugin-control",(c,l,a,p)=>{this.globalsAdded||this.addGlobals(p,c.settingEl)}));let o=(0,w.debounce)(this.refreshButtons.bind(this),50,!0);function r(c){return function(...l){return o(),c.apply(this,l)}}this.register(E(app.commands,{addCommand:r,removeCommand:r})),this.register(E(app.setting,{addSettingTab:r,removeSettingTab:r})),t.onLayoutReady(this.whenReady.bind(this)),this.registerObsidianProtocolHandler("goto-plugin",({id:c,show:l})=>{t.onLayoutReady(()=>{this.gotoPlugin(c,l)})})}whenReady(){var g,y;let t=this.app,e=this,n=(y=(g=t.internalPlugins.plugins["command-palette"])==null?void 0:g.instance)==null?void 0:y.modal;if(n){this.register(E(n,{onChooseItem(u){return function(m,k){return w.Keymap.isModEvent(k)?(C(()=>e.showHotkeysFor(m.name)),!1):u.call(this,m,k)}}}));let f=n.modalEl.find(".prompt-instructions .prompt-instruction");f&&createDiv("prompt-instruction",u=>{u.createSpan({cls:"prompt-instruction-command",text:w.Keymap.compileModifiers(["Mod"])+"+\u21B5"}),u.appendText(" "),u.createSpan({text:"to configure hotkey(s)"}),this.register(()=>u.detach())}).insertAfter(f)}let o=this.getSettingsTab("plugins"),r=this.getSettingsTab("community-plugins");o&&this.register(E(o,{display:this.addPluginSettingEvents.bind(this,o.id)})),r&&this.register(E(r,{display:this.addPluginSettingEvents.bind(this,r.id)}));let c=()=>this.enhanceViewer();r&&this.register(G(r.containerEl,"click",".mod-cta, .installed-plugins-container .setting-item-info",c,!0)),this.register(E(t.workspace.protocolHandlers,{get(f){return function(h){return h==="show-plugin"&&c(),f.call(this,h)}}}));function l(){U()&&t.setting.openTabById(t.setting.activeTab.id)}l(),this.register(()=>C(l));let a=this.getSettingsTab("hotkeys");a&&this.register(E(a,{display(f){return function(){var u,h;f.call(this),(h=(u=this.searchInputEl)!=null?u:this.searchComponent.inputEl)==null||h.focus()}},updateHotkeyVisibility(f){return function(){var k;let u=(k=this.searchInputEl)!=null?k:this.searchComponent.inputEl;if(!u)return f.call(this);let h=u.value,m=t.commands.commands;try{if(h.endsWith(":")&&!h.contains(" ")){let v=m,x=Object.fromEntries(Object.entries(t.commands.commands).filter(([V,pt])=>(V+":").startsWith(h)));u.value="",t.commands.commands=new Proxy(m,{ownKeys(){try{return Object.keys(v)}finally{v=x}}})}return f.call(this)}finally{u.value=h,t.commands.commands=m}}}})),this.addCommand({id:"open-plugins",name:"Open the Community Plugins settings",callback:()=>this.showSettings("community-plugins")||!0}),this.addCommand({id:"browse-plugins",name:"Browse or search the Community Plugins catalog",callback:()=>this.gotoPlugin()});let p=new Intl.Collator(void 0,{usage:"sort",sensitivity:"base",numeric:!0}).compare;this.addCommand({id:"open-settings",name:"Open settings for plugin...",callback:async()=>{let{item:f}=await j(t.setting.pluginTabs.concat(t.setting.settingTabs).sort((u,h)=>p(u.name,h.name)),u=>u.name,"Select a plugin to open its settings...");f&&(t.setting.open(),t.setting.openTabById(f.id))}}),this.addCommand({id:"open-hotkeys",name:"Open hotkeys for plugin...",callback:async()=>{var m,k;let f=this.refreshCommands(),u=Object.values(t.plugins.plugins).map(v=>v.manifest).concat(Object.entries(t.internalPlugins.plugins).map(([v,{instance:{name:x},_loaded:V}])=>({id:v,name:x,enabled:V})).filter(v=>v.enabled)).concat([{id:"app",name:"App"},{id:"editor",name:((m=this.getSettingsTab("editor"))==null?void 0:m.name)||"Editor"},{id:"workspace",name:((k=this.getSettingsTab("file"))==null?void 0:k.name)||"Files & Links"}]).filter(v=>{var x;return(x=f[v.id])==null?void 0:x.length}),{item:h}=await j(u.sort((v,x)=>p(v.name,x.name)),v=>v.name,"Select a plugin to open its hotkeys...");h&&this.showHotkeysFor(h.id+":")}})}createExtraButtons(t,e,n){e.id!=="app"&&t.addExtraButton(o=>{o.setIcon("gear"),o.onClick(()=>this.showConfigFor(e.id.replace(/^workspace$/,"file"))),o.setTooltip("Options"),o.extraSettingsEl.toggle(n)}),t.addExtraButton(o=>{o.setIcon("any-key"),o.onClick(()=>this.showHotkeysFor(e.id+":")),o.extraSettingsEl.toggle(n),this.hotkeyButtons[e.id]=o})}addGlobals(t,e){var l,a,p;this.globalsAdded=!0;let n=e.parentElement,o;if(t!=="plugins"||this.searchInput)(l=o=this.searchInput)==null||l.onChange(c);else{let g=new w.Setting(n).addSearch(y=>{o=y,y.setPlaceholder("Filter plugins...").onChange(c)});o.containerEl.style.margin="0",n.createDiv("hotkey-search-container").append(o.containerEl),g.settingEl.detach()}t==="community-plugins"&&o.inputEl.addEventListener("keydown",g=>{if(g.keyCode===13&&!w.Keymap.getModifiers(g))return this.gotoPlugin(),!1});let r=this;function c(g){let y=(r.lastSearch[t]=g).toLowerCase();function f(u){if(!u)return!1;let h=u.textContent=u.textContent,m=h.toLowerCase().indexOf(y);return~m?(u.textContent=h.substr(0,m),u.createSpan("suggestion-highlight").textContent=h.substr(m,y.length),u.insertAdjacentText("beforeend",h.substr(m+y.length)),!0):!1}n.findAll(".setting-item").forEach(u=>{var v;let h=f(u.find(".setting-item-name")),m=f((v=u.find(".setting-item-description > div:last-child"))!=null?v:u.find(".setting-item-description")),k=f(u.find(".setting-item-description > div:nth-child(2)"));u.toggle(h||m||k)})}if(C(()=>{!o||(o&&typeof r.lastSearch[t]=="string"&&(o.setValue(r.lastSearch[t]),o.onChanged()),w.Platform.isMobile||o.inputEl.select())}),n.append(e),t==="plugins"){let g=((a=this.getSettingsTab("editor"))==null?void 0:a.name)||"Editor",y=((p=this.getSettingsTab("file"))==null?void 0:p.name)||"Files & Links";this.createExtraButtons(new w.Setting(e.parentElement).setName("App").setDesc("Miscellaneous application commands (always enabled)"),{id:"app",name:"App"},!0),this.createExtraButtons(new w.Setting(e.parentElement).setName(g).setDesc("Core editing commands (always enabled)"),{id:"editor",name:g},!0),this.createExtraButtons(new w.Setting(e.parentElement).setName(y).setDesc("Core file and pane management commands (always enabled)"),{id:"workspace",name:y},!0),e.parentElement.append(e)}}enhanceViewer(){let t=this;setTimeout(E(w.Modal.prototype,{open(e){return function(...n){return ut(this)&&(C(()=>{t.lastSearch["community-plugins"]&&(this.search.inputEl.value=t.lastSearch["community-plugins"],this.search.inputEl.dispatchEvent(new Event("input")))}),t.currentViewer=this,E(this,{close(o){return function(...r){return t.currentViewer=null,o.apply(this,r)}},showItem(o){return async function(r){let c=await o.call(this,r);if(t.app.plugins.plugins[r.id]){let l=i18next.t("setting.hotkeys.name"),a=this.detailsEl.find("button").parentElement;for(let p of a.findAll("button"))p.textContent===l&&(t.hotkeyButtons[r.id]={setTooltip(g){return p.title=g,this},extraSettingsEl:p});t.refreshButtons(!0)}return c}}})),lt(this)&&(C(()=>{if(t.lastSearch["community-plugins"]){let o=this.searchResultEl.cloneNode();this.searchContainerEl.replaceChild(o,this.searchResultEl),this.searchResultEl=o,this.searchEl.value=t.lastSearch["community-plugins"],this.searchEl.dispatchEvent(new Event("input"))}this.searchEl.select()}),t.currentViewer=this,E(this,{updateSearch:N,close(o){return function(...r){return t.currentViewer=null,o.apply(this,r)}},showPlugin(o){return async function(r){let c=await o.call(this,r);if(t.app.plugins.plugins[r.id]){let l=i18next.t("setting.hotkeys.name"),a=this.pluginContentEl.find("button").parentElement;for(let p of a.findAll("button"))p.textContent===l&&(t.hotkeyButtons[r.id]={setTooltip(g){return p.title=g,this},extraSettingsEl:p});t.refreshButtons(!0)}return c}}})),e.apply(this,n)}}}),0)}getSettingsTab(t){return app.setting.settingTabs.filter(e=>e.id===t).shift()}addPluginSettingEvents(t,e){let n=this.app,o=this,r=!1;function c(l,...a){r=!0;try{n.workspace.trigger(l,...a)}catch(p){console.error(p)}r=!1}return function(...a){if(r)return;c("plugin-settings:before-display",this,t);let p;t==="plugins"?p=Object.entries(n.internalPlugins.plugins).map(([u,{instance:{name:h,hiddenFromList:m},_loaded:k}])=>!m&&{id:u,name:h,enabled:k}).filter(u=>u):p=Object.values(n.plugins.manifests),p.sort((u,h)=>u.name.localeCompare(h.name));let g=0,y="",f=E(w.Setting.prototype,{addExtraButton(u){return function(h){if(!r&&(t==="plugins"||this.descEl.childElementCount)&&(p[g]||{}).name===this.nameEl.textContent){let m=p[g++];y=m.id,c("plugin-settings:plugin-control",this,m,m.enabled,t)}return u.call(this,function(m){h(m),!r&&m.extraSettingsEl.find("svg.any-key, svg.lucide-plus-circle")&&y&&(o.hotkeyButtons[y]=m,m.onClick(o.showHotkeysFor.bind(o,y+":")))})}}});try{return e.apply(this,a)}finally{f(),c("plugin-settings:after-display",this)}}}gotoPlugin(t,e="info"){if(t&&e==="hotkeys")return this.showHotkeysFor(t+":");if(t&&e==="config"){this.showConfigFor(t)||this.app.setting.close();return}this.showSettings("community-plugins");let n=E(w.Modal.prototype,{open(o){return function(...r){var c;return n(),t&&(this.autoload=t,(c=this.setAutoOpen)==null||c.call(this,t)),o.apply(this,r)}}});this.app.setting.activeTab.containerEl.find(".mod-cta").click()}showSettings(t){var e,n,o;if((e=this.currentViewer)==null||e.close(),J()||app.setting.open(),t)return((n=app.setting.activeTab)==null?void 0:n.id)!==t&&app.setting.openTabById(t),((o=app.setting.activeTab)==null?void 0:o.id)===t?app.setting.activeTab:!1}showHotkeysFor(t){var n,o;let e=this.showSettings("hotkeys");e&&((n=e.searchInputEl)!=null?n:e.searchComponent.inputEl)&&e.updateHotkeyVisibility&&(((o=e.searchInputEl)!=null?o:e.searchComponent.inputEl).value=t,e.updateHotkeyVisibility())}showConfigFor(t){return this.showSettings(t)?!0:(new w.Notice(`No settings tab for "${t}": it may not be installed or might not have settings.`),!1)}pluginEnabled(t){var e;return((e=app.internalPlugins.plugins[t])==null?void 0:e._loaded)||app.plugins.plugins[t]}refreshCommands(){let t=app.hotkeyManager;return this.assignedKeyCount={},this.commandsByPlugin=Object.values(app.commands.commands).reduce((e,n)=>{let o=n.id.split(":",2).shift(),r=(t.getHotkeys(n.id)||t.getDefaultHotkeys(n.id)||[]).map(rt);return r.forEach(c=>this.assignedKeyCount[c]=1+(this.assignedKeyCount[c]||0)),(e[o]||(e[o]=[])).push({hotkeys:r,cmd:n}),e},{})}refreshButtons(t=!1){var n;if(!U()&&!t)return;this.refreshCommands();let e=Object.values(app.setting.pluginTabs).reduce((o,r)=>(o[r.id]=r,o),{});e.workspace=e.editor=!0;for(let o of Object.keys(this.hotkeyButtons||{})){let r=this.hotkeyButtons[o];if(!this.commandsByPlugin[o]||((n=app.internalPlugins.plugins[o])==null?void 0:n.enabled)===!1){r.extraSettingsEl.hide();continue}let c=this.commandsByPlugin[o].filter(a=>a.hotkeys.length),l=c.filter(a=>a.hotkeys.filter(p=>this.assignedKeyCount[p]>1).length).length;r.setTooltip(`Configure hotkeys
(${c.length}/${this.commandsByPlugin[o].length} assigned${l?"; "+l+" conflicting":""})`),r.extraSettingsEl.toggleClass("mod-error",!!l),r.extraSettingsEl.show()}}};
