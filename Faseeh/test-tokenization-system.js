#!/usr/bin/env node

/**
 * Standalone test for the Faseeh text tokenization system
 * This tests the core tokenization functionality without any UI dependencies
 */

// Mock the Token interface for Node.js testing
class Token {
  constructor(text, startIndex, endIndex, isWord) {
    this.text = text;
    this.startIndex = startIndex;
    this.endIndex = endIndex;
    this.isWord = isWord;
  }
}

// Simple tokenizer implementation compatible with all Node.js versions
function unicodeTokenizer(text) {
  const tokens = [];

  if (!text || text.trim().length === 0) {
    return tokens;
  }

  // Simple regex that works with all Node.js versions
  // Matches word characters, numbers, and common punctuation
  const tokenRegex = /(\w+(?:[-']\w+)*|[^\w\s]|\s+)/g;

  let match;

  while ((match = tokenRegex.exec(text)) !== null) {
    const tokenText = match[0];
    const startIndex = match.index;
    const endIndex = startIndex + tokenText.length - 1;

    // Skip whitespace-only tokens
    if (/^\s+$/.test(tokenText)) {
      continue;
    }

    // Determine if this is likely a word
    const isWord = /^\w+(?:[-']\w+)*$/.test(tokenText);

    tokens.push(new Token(tokenText, startIndex, endIndex, isWord));
  }

  return tokens;
}

// Punctuation tokenizer implementation
function punctuationTokenizer(text) {
  const tokens = [];
  
  if (!text || text.trim().length === 0) {
    return tokens;
  }

  const tokenRegex = /(\w+(?:[-']\w+)*|\d+(?:\.\d+)?|[^\w\s]|\s+)/g;
  
  let match;
  
  while ((match = tokenRegex.exec(text)) !== null) {
    const tokenText = match[0];
    const startIndex = match.index;
    const endIndex = startIndex + tokenText.length - 1;
    
    // Skip whitespace-only tokens
    if (/^\s+$/.test(tokenText)) {
      continue;
    }
    
    // Determine if this is likely a word
    const isWord = /^[\w][\w-']*[\w]$|^\w$/.test(tokenText) || /^\d+(?:\.\d+)?$/.test(tokenText);
    
    tokens.push(new Token(tokenText, startIndex, endIndex, isWord));
  }
  
  return tokens;
}

// Mock TokenizerRegistry implementation
class TokenizerRegistry {
  constructor() {
    this.tokenizers = new Map();
    this.defaultLanguage = '*';
  }

  register(registration) {
    if (this.tokenizers.has(registration.id)) {
      throw new Error(`Tokenizer with id '${registration.id}' is already registered`);
    }

    if (registration.priority === undefined) {
      registration.priority = 0;
    }

    this.tokenizers.set(registration.id, registration);
  }

  unregister(id) {
    this.tokenizers.delete(id);
  }

  async tokenizeText(text, languageCode = this.defaultLanguage) {
    const tokenizer = this.findBestTokenizer(languageCode);

    if (!tokenizer) {
      throw new Error(`No tokenizer found for language: ${languageCode}`);
    }

    const result = tokenizer.tokenize(text);
    return result instanceof Promise ? result : Promise.resolve(result);
  }

  listRegisteredTokenizers() {
    return Array.from(this.tokenizers.values()).map(({ tokenize, ...info }) => info);
  }

  getTokenizerById(id) {
    return this.tokenizers.get(id) || null;
  }

  findBestTokenizer(languageCode) {
    let bestMatch = null;
    let bestScore = -1;

    for (const tokenizer of this.tokenizers.values()) {
      const langSupported = tokenizer.languageCodes.some(
        lang => lang === languageCode || lang === '*'
      );

      if (langSupported) {
        const score = this.calculateScore(tokenizer, languageCode);
        if (score > bestScore) {
          bestScore = score;
          bestMatch = tokenizer;
        }
      }
    }

    return bestMatch;
  }

  calculateScore(tokenizer, languageCode) {
    let score = tokenizer.priority || 0;

    if (tokenizer.languageCodes.includes(languageCode)) {
      score += 1000;
    } else if (tokenizer.languageCodes.includes('*')) {
      score += 100;
    }

    return score;
  }
}

// Initialize default tokenizers
function initializeDefaultTokenizers(registry) {
  // Register the Unicode tokenizer as default
  registry.register({
    id: 'faseeh-core-unicode',
    name: 'Unicode-Aware Tokenizer',
    description: 'Unicode-aware tokenizer that handles international characters and scripts',
    languageCodes: ['*'],
    priority: 0,
    tokenize: unicodeTokenizer
  });

  // Register English-specific tokenizer
  registry.register({
    id: 'faseeh-core-english',
    name: 'English Tokenizer',
    description: 'Optimized tokenizer for English text',
    languageCodes: ['en', 'en-US', 'en-GB', 'en-CA', 'en-AU'],
    priority: 50,
    tokenize: punctuationTokenizer
  });

  // Register Romance languages tokenizer
  registry.register({
    id: 'faseeh-core-romance',
    name: 'Romance Languages Tokenizer',
    description: 'Tokenizer optimized for Romance languages with accent support',
    languageCodes: ['es', 'fr', 'it', 'pt', 'ro', 'ca'],
    priority: 50,
    tokenize: unicodeTokenizer
  });
}

// Main test function
async function runTokenizationTests() {
  console.log('🚀 Testing Faseeh Text Tokenization System\n');

  try {
    // Create registry and initialize default tokenizers
    const registry = new TokenizerRegistry();
    initializeDefaultTokenizers(registry);

    console.log('✅ Tokenizer registry initialized');
    console.log(`📋 Registered ${registry.listRegisteredTokenizers().length} default tokenizers\n`);

    // Test texts in different languages
    const testCases = [
      { text: 'Hello, world! This is a test.', lang: 'en', description: 'English' },
      { text: 'Bonjour le monde! Ceci est un test.', lang: 'fr', description: 'French' },
      { text: 'Hola mundo! Esta es una prueba.', lang: 'es', description: 'Spanish' },
      { text: 'Привет мир! Это тест.', lang: 'ru', description: 'Russian (Cyrillic)' },
      { text: 'مرحبا بالعالم! هذا اختبار.', lang: 'ar', description: 'Arabic' },
      { text: 'Hello-world, it\'s working!', lang: 'en', description: 'English with hyphens/contractions' }
    ];

    for (const testCase of testCases) {
      console.log(`🔤 Testing ${testCase.description}: "${testCase.text}"`);
      
      const tokens = await registry.tokenizeText(testCase.text, testCase.lang);
      const words = tokens.filter(t => t.isWord);
      const nonWords = tokens.filter(t => !t.isWord);
      
      console.log(`   📊 Total tokens: ${tokens.length} (${words.length} words, ${nonWords.length} punctuation)`);
      console.log(`   🔍 Words: ${words.map(t => t.text).join(', ')}`);
      console.log(`   ⚡ Punctuation: ${nonWords.map(t => t.text).join(', ')}`);
      console.log('');
    }

    // Test tokenizer selection
    console.log('🎯 Testing tokenizer selection:');
    const englishTokenizer = registry.findBestTokenizer('en');
    const frenchTokenizer = registry.findBestTokenizer('fr');
    const unknownTokenizer = registry.findBestTokenizer('unknown');

    console.log(`   English (en): ${englishTokenizer.name} (priority: ${englishTokenizer.priority})`);
    console.log(`   French (fr): ${frenchTokenizer.name} (priority: ${frenchTokenizer.priority})`);
    console.log(`   Unknown: ${unknownTokenizer.name} (priority: ${unknownTokenizer.priority})`);
    console.log('');

    // List all registered tokenizers
    console.log('📝 Registered tokenizers:');
    const tokenizers = registry.listRegisteredTokenizers();
    tokenizers.forEach(t => {
      console.log(`   • ${t.name} (${t.id})`);
      console.log(`     Languages: ${t.languageCodes.join(', ')}`);
      console.log(`     Priority: ${t.priority}`);
      console.log('');
    });

    console.log('🎉 All tokenization tests completed successfully!');
    return true;

  } catch (error) {
    console.error('❌ Tokenization test failed:', error.message);
    console.error(error.stack);
    return false;
  }
}

// Run the tests
if (require.main === module) {
  runTokenizationTests().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = { runTokenizationTests, TokenizerRegistry, unicodeTokenizer, punctuationTokenizer };
