# Faseeh: Learn Languages Through What You Love

Faseeh lets you turn your favorite content—books, podcasts, videos, social media—into personalized learning material. No rigid courses, no boring drills. Just real-life content, smart tools, and full control over how you learn.

**Why Faseeh?**  
✨ **Learn Anywhere, with Anything**: Use any digital material—learn from what already interests you.  
✨ **Expand with Plugins**: Build or install plugins for specific languages, skills, or styles.  
✨ **Collaborate & Share**: Join a global community to co-create resources, strategies, and plugins.  
✨ **All-in-One Toolkit**: Access Subtitles generation, dictionaries, pronunciation tools, and more in one place—no app-hopping.

> **Vision:** Faseeh operates on the principle that language acquisition thrives when aligned with personal interests and real-world contexts. Bridges the gap between education and daily life by transforming passive scrolling or reading into active learning, making mastery a natural result of exploring what fascinates you.

## ✅ Recommended IDE Setup

- [VSCode](https://code.visualstudio.com/) + [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint) + [Prettier](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar)

## 🚀 Getting Started

1. Clone the repo and install dependencies:

```bash
git clone https://github.com/FaseehApp/Faseeh.git
cd Faseeh
npm install
```

2. Start the app in development mode (hot‑reload enabled):

```bash
$ npm run dev
```

## 🛠️ Build

```bash
# For windows
$ npm run build:win

# For macOS
$ npm run build:mac

# For Linux
$ npm run build:linux
```

## 📜 License

This project is licensed under the MIT License. See LICENSE for details.
