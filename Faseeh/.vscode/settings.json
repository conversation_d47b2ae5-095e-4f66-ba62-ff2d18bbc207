{"[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/.classpath": true, "**/.project": true, "**/.settings": true, "**/.factorypath": true, "node_modules": true, ".prettierignore": true, ".editorconfig": true, "dev-app-update.yml": true, "package-lock.json": true, "electron-builder.yml": true, "build": true, "out": true, "eslint.config.mjs": true, "desktop.ini": true, ".prettierrc.yaml": true, "LICENSE": true, ".vscode": true}, "hide-files.files": ["node_modules", ".prettieri<PERSON>re", ".editorconfig", "dev-app-update.yml", "package-lock.json", "electron-builder.yml", "build", "out", "eslint.config.mjs", "desktop.ini", ".prettierrc.yaml", "LICENSE", ".vscode"], "files.associations": {"*.css": "tailwindcss"}, "editor.quickSuggestions": {"strings": "on"}}