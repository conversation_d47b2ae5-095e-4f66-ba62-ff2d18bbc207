{"name": "fase<PERSON>", "version": "1.0.0", "description": "An Electron application with Vue and TypeScript", "main": "./out/main/index.js", "author": "example.com", "homepage": "https://electron-vite.org", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "vue-tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "npm run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^4.0.0", "@iconify-prerendered/vue-eva": "^0.28.1722793329", "@iconify-prerendered/vue-fluent": "^0.28.1745645887", "@iconify-prerendered/vue-solar": "^0.28.1722795734", "@vueuse/components": "^13.1.0", "@vueuse/core": "^13.1.0", "better-sqlite3": "^11.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "electron-updater": "^6.3.9", "kysely": "^0.28.2", "lucide-vue-next": "^0.508.0", "pinia": "^3.0.2", "reka-ui": "^2.2.1", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.9", "vue-router": "^4.5.1"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "3.0.0", "@electron-toolkit/eslint-config-ts": "^3.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@iconify/json": "^2.2.336", "@iconify/tailwind4": "^1.0.6", "@tailwindcss/vite": "^4.1.5", "@types/better-sqlite3": "^7.6.13", "@types/node": "^22.14.1", "@vitejs/plugin-vue": "^5.2.3", "electron": "^35.1.5", "electron-builder": "^25.1.8", "electron-rebuild": "^3.2.9", "electron-vite": "^3.1.0", "eslint": "^9.24.0", "eslint-plugin-vue": "^10.0.0", "glob": "^11.0.2", "prettier": "^3.5.3", "tailwindcss": "^4.1.5", "typescript": "^5.8.3", "vite": "^6.2.6", "vite-plugin-static-copy": "^3.0.0", "vue": "^3.5.13", "vue-eslint-parser": "^10.1.3", "vue-tsc": "^2.2.8"}}