<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tokenizer Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .token {
            display: inline-block;
            margin: 2px;
            padding: 2px 6px;
            background: #e0e0e0;
            border-radius: 3px;
            font-family: monospace;
        }
        .token.word {
            background: #c8e6c9;
        }
        .token.non-word {
            background: #ffcdd2;
        }
    </style>
</head>
<body>
    <h1>Faseeh Tokenizer Test</h1>
    <div id="results"></div>

    <script type="module">
        // Mock Token class
        class Token {
            constructor(text, startIndex, endIndex, isWord) {
                this.text = text;
                this.startIndex = startIndex;
                this.endIndex = endIndex;
                this.isWord = isWord;
            }
        }

        // Unicode-aware tokenizer implementation
        function unicodeTokenizer(text) {
            const tokens = [];
            
            if (!text || text.trim().length === 0) {
                return tokens;
            }

            // Unicode-aware regex that handles international characters
            const tokenRegex = /([\p{L}\p{N}\p{M}]+(?:[-'][\p{L}\p{N}\p{M}]+)*|[\p{P}\p{S}]|\s+)/gu;
            
            let match;
            
            while ((match = tokenRegex.exec(text)) !== null) {
                const tokenText = match[0];
                const startIndex = match.index;
                const endIndex = startIndex + tokenText.length - 1;
                
                // Skip whitespace-only tokens
                if (/^\s+$/.test(tokenText)) {
                    continue;
                }
                
                // Determine if this is likely a word (letters, numbers, marks)
                const isWord = /^[\p{L}\p{N}\p{M}][\p{L}\p{N}\p{M}-']*[\p{L}\p{N}\p{M}]$|^[\p{L}\p{N}\p{M}]$/u.test(tokenText);
                
                tokens.push(new Token(tokenText, startIndex, endIndex, isWord));
            }
            
            return tokens;
        }

        // Mock TokenizerRegistry
        class TokenizerRegistry {
            constructor() {
                this.tokenizers = new Map();
                this.defaultLanguage = '*';
            }

            register(registration) {
                if (this.tokenizers.has(registration.id)) {
                    throw new Error(`Tokenizer with id '${registration.id}' is already registered`);
                }

                if (registration.priority === undefined) {
                    registration.priority = 0;
                }

                this.tokenizers.set(registration.id, registration);
            }

            async tokenizeText(text, languageCode = this.defaultLanguage) {
                const tokenizer = this.findBestTokenizer(languageCode);

                if (!tokenizer) {
                    throw new Error(`No tokenizer found for language: ${languageCode}`);
                }

                const result = tokenizer.tokenize(text);
                return result instanceof Promise ? result : Promise.resolve(result);
            }

            listRegisteredTokenizers() {
                return Array.from(this.tokenizers.values()).map(({ tokenize, ...info }) => info);
            }

            findBestTokenizer(languageCode) {
                let bestMatch = null;
                let bestScore = -1;

                for (const tokenizer of this.tokenizers.values()) {
                    const langSupported = tokenizer.languageCodes.some(
                        lang => lang === languageCode || lang === '*'
                    );

                    if (langSupported) {
                        const score = this.calculateScore(tokenizer, languageCode);
                        if (score > bestScore) {
                            bestScore = score;
                            bestMatch = tokenizer;
                        }
                    }
                }

                return bestMatch;
            }

            calculateScore(tokenizer, languageCode) {
                let score = tokenizer.priority || 0;

                if (tokenizer.languageCodes.includes(languageCode)) {
                    score += 1000;
                } else if (tokenizer.languageCodes.includes('*')) {
                    score += 100;
                }

                return score;
            }
        }

        // Initialize and test
        async function runTests() {
            const resultsDiv = document.getElementById('results');
            
            try {
                // Create registry and register tokenizers
                const registry = new TokenizerRegistry();
                
                // Register Unicode tokenizer
                registry.register({
                    id: 'faseeh-core-unicode',
                    name: 'Unicode-Aware Tokenizer',
                    description: 'Unicode-aware tokenizer that handles international characters and scripts',
                    languageCodes: ['*'],
                    priority: 0,
                    tokenize: unicodeTokenizer
                });

                // Register English-specific tokenizer
                registry.register({
                    id: 'faseeh-core-english',
                    name: 'English Tokenizer',
                    description: 'Optimized tokenizer for English text',
                    languageCodes: ['en', 'en-US', 'en-GB'],
                    priority: 50,
                    tokenize: unicodeTokenizer
                });

                resultsDiv.innerHTML += '<div class="test-result"><h3>✅ Tokenizer Registry Initialized</h3></div>';

                // Test texts
                const testTexts = [
                    { text: 'Hello, world! This is a test.', lang: 'en' },
                    { text: 'Bonjour le monde! Ceci est un test.', lang: 'fr' },
                    { text: 'Hola mundo! Esta es una prueba.', lang: 'es' },
                    { text: 'Привет мир! Это тест.', lang: 'ru' },
                    { text: 'مرحبا بالعالم! هذا اختبار.', lang: 'ar' }
                ];

                for (const { text, lang } of testTexts) {
                    const tokens = await registry.tokenizeText(text, lang);
                    
                    let html = `<div class="test-result">`;
                    html += `<h4>Text: "${text}" (${lang})</h4>`;
                    html += `<p>Tokens (${tokens.length}):</p>`;
                    html += '<div>';
                    
                    for (const token of tokens) {
                        const className = token.isWord ? 'word' : 'non-word';
                        html += `<span class="token ${className}" title="Start: ${token.startIndex}, End: ${token.endIndex}">${token.text}</span>`;
                    }
                    
                    html += '</div></div>';
                    resultsDiv.innerHTML += html;
                }

                // List registered tokenizers
                const tokenizers = registry.listRegisteredTokenizers();
                let html = '<div class="test-result"><h3>Registered Tokenizers</h3><ul>';
                for (const tokenizer of tokenizers) {
                    html += `<li><strong>${tokenizer.name}</strong> (${tokenizer.id})<br>`;
                    html += `Languages: ${tokenizer.languageCodes.join(', ')}<br>`;
                    html += `Priority: ${tokenizer.priority}</li>`;
                }
                html += '</ul></div>';
                resultsDiv.innerHTML += html;

                resultsDiv.innerHTML += '<div class="test-result"><h3>✅ All Tests Completed Successfully!</h3></div>';

            } catch (error) {
                resultsDiv.innerHTML += `<div class="test-result"><h3>❌ Test Failed</h3><p>${error.message}</p></div>`;
                console.error('Test failed:', error);
            }
        }

        // Run tests when page loads
        runTests();
    </script>
</body>
</html>
