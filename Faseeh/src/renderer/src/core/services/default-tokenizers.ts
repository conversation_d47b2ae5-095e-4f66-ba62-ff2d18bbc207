import { TokenizerRegistry } from './text-tokenizer-registry'
import {
  whitespaceTokenizer,
  punctuationTokenizer,
  unicodeTokenizer
} from '../tokenizers/whitespace-tokenizer'

/**
 * Initializes and registers the default/core tokenizers that come with Faseeh
 * These provide essential tokenization functionality for core languages
 *
 * Priority Levels:
 * - 1000+: Specialized plugin tokenizers (e.g., Jieba for Chinese, MeCab for Japanese)
 * - 100-300: Language-specific tokenizers for core languages
 * - 0 to -100: Universal fallback tokenizers
 */
export function initializeDefaultTokenizers(registry: TokenizerRegistry): void {
  // ========================================
  // UNIVERSAL FALLBACK TOKENIZERS
  // ========================================

  // Register the basic whitespace tokenizer as the ultimate fallback
  registry.register({
    id: 'faseeh-core-whitespace',
    name: 'Basic Whitespace Tokenizer',
    description: 'Simple whitespace and punctuation-based tokenizer that works for any language',
    languageCodes: ['*'], // Universal fallback
    priority: -100, // Very low priority - only used when nothing else is available
    tokenize: whitespaceTokenizer
  })

  // Register the punctuation-aware tokenizer for better handling of structured text
  registry.register({
    id: 'faseeh-core-punctuation',
    name: 'Punctuation-Aware Tokenizer',
    description: 'Improved tokenizer that handles punctuation and contractions better',
    languageCodes: ['*'], // Universal fallback
    priority: -50, // Low priority but better than basic whitespace
    tokenize: punctuationTokenizer
  })

  // Register the Unicode-aware tokenizer for international text
  registry.register({
    id: 'faseeh-core-unicode',
    name: 'Unicode-Aware Tokenizer',
    description: 'Unicode-aware tokenizer that handles international characters and scripts',
    languageCodes: ['*'], // Universal fallback
    priority: 0, // Default priority - good general-purpose tokenizer
    tokenize: unicodeTokenizer
  })

  // ========================================
  // ESSENTIAL LANGUAGE-SPECIFIC TOKENIZERS (Priority: 100-300)
  // ========================================

  // English tokenizer with enhanced contraction and compound word handling
  registry.register({
    id: 'faseeh-core-english',
    name: 'English Tokenizer',
    description: 'Enhanced English tokenizer with improved handling of contractions, compound words, and abbreviations',
    languageCodes: ['en', 'en-US', 'en-GB', 'en-CA', 'en-AU'],
    priority: 200,
    tokenize: punctuationTokenizer
  })

  // French tokenizer with accent and diacritic support
  registry.register({
    id: 'faseeh-core-french',
    name: 'French Tokenizer',
    description: 'French tokenizer with proper accent and diacritic handling',
    languageCodes: ['fr', 'fr-FR', 'fr-CA', 'fr-BE', 'fr-CH'],
    priority: 200,
    tokenize: unicodeTokenizer
  })

  // Spanish tokenizer with accent support
  registry.register({
    id: 'faseeh-core-spanish',
    name: 'Spanish Tokenizer',
    description: 'Spanish tokenizer with proper accent and punctuation handling',
    languageCodes: ['es', 'es-ES', 'es-MX', 'es-AR', 'es-CO'],
    priority: 200,
    tokenize: unicodeTokenizer
  })

  // Arabic tokenizer with enhanced script handling
  registry.register({
    id: 'faseeh-core-arabic',
    name: 'Arabic Tokenizer',
    description: 'Enhanced tokenizer for Arabic script with better diacritic and ligature handling',
    languageCodes: ['ar', 'ar-SA', 'ar-EG', 'ar-AE', 'ar-MA'],
    priority: 300,
    tokenize: unicodeTokenizer
  })

  // Chinese tokenizer - basic character-level (plugins should provide word segmentation)
  registry.register({
    id: 'faseeh-core-chinese',
    name: 'Chinese Basic Tokenizer',
    description: 'Basic Chinese tokenizer for character-level segmentation. For production use, install a specialized Chinese word segmentation plugin (e.g., Jieba, THULAC)',
    languageCodes: ['zh', 'zh-CN', 'zh-TW', 'zh-HK', 'zh-SG'],
    priority: 150,
    tokenize: unicodeTokenizer
  })

  // ========================================
  // IMPORTANT NOTES FOR COMPLEX LANGUAGES
  // ========================================

  /*
   * SPECIALIZED TOKENIZATION REQUIREMENTS:
   *
   * The following languages require sophisticated word segmentation algorithms
   * and should use specialized plugins with priority 1000+ for production use:
   *
   * 1. CHINESE (zh, zh-CN, zh-TW, zh-HK, zh-SG):
   *    - Requires word segmentation libraries like Jieba, THULAC, or Stanford CoreNLP
   *    - No spaces between words, complex compound word formation
   *    - Traditional vs Simplified character considerations
   *    - The basic Chinese tokenizer above provides character-level fallback only
   *
   * 2. JAPANESE (ja, ja-JP):
   *    - Requires morphological analysis tools like MeCab, Kuromoji, or Sudachi
   *    - Mixed scripts: Hiragana, Katakana, Kanji, Latin
   *    - No spaces between words, complex grammatical particles
   *    - Plugins should provide proper word boundary detection
   *
   * 3. THAI (th, th-TH):
   *    - Requires word boundary detection algorithms
   *    - No spaces between words, complex script with tone marks
   *    - Libraries like ICU Boundary Analysis or specialized Thai NLP tools
   *
   * 4. KOREAN (ko, ko-KR):
   *    - Benefits from morphological analysis for agglutinative structure
   *    - Plugins can provide better handling of compound words and particles
   *
   * 5. OTHER COMPLEX SCRIPTS:
   *    - Khmer (km), Lao (lo), Myanmar (my), etc.
   *    - Require specialized word boundary detection
   *    - Complex scripts with no clear word separators
   *
   * The basic tokenizers provided here offer character-level or simple Unicode
   * tokenization as fallbacks, but production applications should install
   * appropriate language-specific plugins for these languages.
   */
}
