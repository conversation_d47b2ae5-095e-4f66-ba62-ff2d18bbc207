import { TokenizerRegistry } from './text-tokenizer-registry'
import {
  whitespaceTokenizer,
  punctuationTokenizer,
  unicodeTokenizer
} from '../tokenizers/whitespace-tokenizer'

/**
 * Initializes and registers the default/core tokenizers that come with Faseeh
 * These provide comprehensive tokenization functionality for major world languages
 *
 * Priority Levels:
 * - 1000+: Specialized plugin tokenizers (e.g., Jieba for Chinese, MeCab for Japanese)
 * - 100-500: Language-specific tokenizers
 * - 50: Language family tokenizers
 * - 0 to -100: Universal fallback tokenizers
 */
export function initializeDefaultTokenizers(registry: TokenizerRegistry): void {
  // ========================================
  // UNIVERSAL FALLBACK TOKENIZERS
  // ========================================

  // Register the basic whitespace tokenizer as the ultimate fallback
  registry.register({
    id: 'faseeh-core-whitespace',
    name: 'Basic Whitespace Tokenizer',
    description: 'Simple whitespace and punctuation-based tokenizer that works for any language',
    languageCodes: ['*'], // Universal fallback
    priority: -100, // Very low priority - only used when nothing else is available
    tokenize: whitespaceTokenizer
  })

  // Register the punctuation-aware tokenizer for better handling of structured text
  registry.register({
    id: 'faseeh-core-punctuation',
    name: 'Punctuation-Aware Tokenizer',
    description: 'Improved tokenizer that handles punctuation and contractions better',
    languageCodes: ['*'], // Universal fallback
    priority: -50, // Low priority but better than basic whitespace
    tokenize: punctuationTokenizer
  })

  // Register the Unicode-aware tokenizer for international text
  registry.register({
    id: 'faseeh-core-unicode',
    name: 'Unicode-Aware Tokenizer',
    description: 'Unicode-aware tokenizer that handles international characters and scripts',
    languageCodes: ['*'], // Universal fallback
    priority: 0, // Default priority - good general-purpose tokenizer
    tokenize: unicodeTokenizer
  })

  // ========================================
  // LANGUAGE-SPECIFIC TOKENIZERS (Priority: 100-500)
  // ========================================

  // Enhanced English tokenizer with better contraction and compound word handling
  registry.register({
    id: 'faseeh-core-english-enhanced',
    name: 'Enhanced English Tokenizer',
    description: 'Advanced English tokenizer with improved handling of contractions, compound words, and abbreviations',
    languageCodes: ['en', 'en-US', 'en-GB', 'en-CA', 'en-AU', 'en-NZ', 'en-ZA', 'en-IE'],
    priority: 200,
    tokenize: punctuationTokenizer
  })

  // Japanese tokenizer - basic Unicode handling (plugins should provide specialized segmentation)
  registry.register({
    id: 'faseeh-core-japanese',
    name: 'Japanese Basic Tokenizer',
    description: 'Basic Japanese tokenizer for hiragana, katakana, and kanji. For production use, install a specialized Japanese segmentation plugin (e.g., MeCab, Kuromoji)',
    languageCodes: ['ja', 'ja-JP'],
    priority: 150,
    tokenize: unicodeTokenizer
  })

  // Enhanced Arabic script tokenizer
  registry.register({
    id: 'faseeh-core-arabic-enhanced',
    name: 'Enhanced Arabic Script Tokenizer',
    description: 'Enhanced tokenizer for Arabic script languages with better diacritic and ligature handling',
    languageCodes: ['ar', 'ar-SA', 'ar-EG', 'ar-AE', 'ar-MA', 'ar-DZ', 'ar-TN', 'ar-LY', 'ar-SD', 'ar-SY', 'ar-IQ', 'ar-JO', 'ar-LB', 'ar-KW', 'ar-OM', 'ar-QA', 'ar-BH', 'ar-YE'],
    priority: 300,
    tokenize: unicodeTokenizer
  })

  // Persian/Farsi tokenizer
  registry.register({
    id: 'faseeh-core-persian',
    name: 'Persian/Farsi Tokenizer',
    description: 'Specialized tokenizer for Persian/Farsi with Arabic script adaptations',
    languageCodes: ['fa', 'fa-IR', 'fa-AF', 'prs'],
    priority: 250,
    tokenize: unicodeTokenizer
  })

  // Urdu tokenizer
  registry.register({
    id: 'faseeh-core-urdu',
    name: 'Urdu Tokenizer',
    description: 'Specialized tokenizer for Urdu with Arabic script and Latin borrowings',
    languageCodes: ['ur', 'ur-PK', 'ur-IN'],
    priority: 250,
    tokenize: unicodeTokenizer
  })

  // Hebrew tokenizer
  registry.register({
    id: 'faseeh-core-hebrew',
    name: 'Hebrew Tokenizer',
    description: 'Specialized tokenizer for Hebrew with right-to-left script support',
    languageCodes: ['he', 'he-IL', 'iw'],
    priority: 250,
    tokenize: unicodeTokenizer
  })

  // Chinese tokenizer - basic character-level (plugins should provide word segmentation)
  registry.register({
    id: 'faseeh-core-chinese',
    name: 'Chinese Basic Tokenizer',
    description: 'Basic Chinese tokenizer for character-level segmentation. For production use, install a specialized Chinese word segmentation plugin (e.g., Jieba, THULAC)',
    languageCodes: ['zh', 'zh-CN', 'zh-TW', 'zh-HK', 'zh-SG', 'cmn', 'yue'],
    priority: 150,
    tokenize: unicodeTokenizer
  })

  // Korean tokenizer - basic Unicode handling
  registry.register({
    id: 'faseeh-core-korean',
    name: 'Korean Basic Tokenizer',
    description: 'Basic Korean tokenizer for Hangul characters. For advanced morphological analysis, install a specialized Korean plugin',
    languageCodes: ['ko', 'ko-KR', 'ko-KP'],
    priority: 200,
    tokenize: unicodeTokenizer
  })

  // Thai tokenizer - basic Unicode handling (requires specialized word boundary detection)
  registry.register({
    id: 'faseeh-core-thai',
    name: 'Thai Basic Tokenizer',
    description: 'Basic Thai tokenizer. For proper word segmentation, install a specialized Thai word boundary detection plugin',
    languageCodes: ['th', 'th-TH'],
    priority: 150,
    tokenize: unicodeTokenizer
  })

  // Vietnamese tokenizer
  registry.register({
    id: 'faseeh-core-vietnamese',
    name: 'Vietnamese Tokenizer',
    description: 'Tokenizer for Vietnamese with Latin script and tone marks',
    languageCodes: ['vi', 'vi-VN'],
    priority: 200,
    tokenize: unicodeTokenizer
  })

  // Hindi tokenizer
  registry.register({
    id: 'faseeh-core-hindi',
    name: 'Hindi Tokenizer',
    description: 'Tokenizer for Hindi with Devanagari script support',
    languageCodes: ['hi', 'hi-IN'],
    priority: 200,
    tokenize: unicodeTokenizer
  })

  // Bengali tokenizer
  registry.register({
    id: 'faseeh-core-bengali',
    name: 'Bengali Tokenizer',
    description: 'Tokenizer for Bengali with Bengali script support',
    languageCodes: ['bn', 'bn-BD', 'bn-IN'],
    priority: 200,
    tokenize: unicodeTokenizer
  })

  // Tamil tokenizer
  registry.register({
    id: 'faseeh-core-tamil',
    name: 'Tamil Tokenizer',
    description: 'Tokenizer for Tamil with Tamil script support',
    languageCodes: ['ta', 'ta-IN', 'ta-LK', 'ta-SG'],
    priority: 200,
    tokenize: unicodeTokenizer
  })

  // Telugu tokenizer
  registry.register({
    id: 'faseeh-core-telugu',
    name: 'Telugu Tokenizer',
    description: 'Tokenizer for Telugu with Telugu script support',
    languageCodes: ['te', 'te-IN'],
    priority: 200,
    tokenize: unicodeTokenizer
  })

  // Gujarati tokenizer
  registry.register({
    id: 'faseeh-core-gujarati',
    name: 'Gujarati Tokenizer',
    description: 'Tokenizer for Gujarati with Gujarati script support',
    languageCodes: ['gu', 'gu-IN'],
    priority: 200,
    tokenize: unicodeTokenizer
  })

  // Punjabi tokenizer
  registry.register({
    id: 'faseeh-core-punjabi',
    name: 'Punjabi Tokenizer',
    description: 'Tokenizer for Punjabi with Gurmukhi script support',
    languageCodes: ['pa', 'pa-IN', 'pa-PK'],
    priority: 200,
    tokenize: unicodeTokenizer
  })

  // ========================================
  // LANGUAGE FAMILY TOKENIZERS (Priority: 50)
  // ========================================

  // Enhanced Romance languages tokenizer with expanded language support
  registry.register({
    id: 'faseeh-core-romance-enhanced',
    name: 'Enhanced Romance Languages Tokenizer',
    description: 'Comprehensive tokenizer for Romance languages with advanced accent and diacritic handling',
    languageCodes: [
      'es', 'es-ES', 'es-MX', 'es-AR', 'es-CO', 'es-PE', 'es-VE', 'es-CL', 'es-EC', 'es-GT', 'es-CU', 'es-BO', 'es-DO', 'es-HN', 'es-PY', 'es-SV', 'es-NI', 'es-CR', 'es-PA', 'es-UY', 'es-PR',
      'fr', 'fr-FR', 'fr-CA', 'fr-BE', 'fr-CH', 'fr-LU', 'fr-MC', 'fr-SN', 'fr-CI', 'fr-ML', 'fr-BF', 'fr-NE', 'fr-TG', 'fr-BJ', 'fr-MG', 'fr-CM', 'fr-CF', 'fr-TD', 'fr-CG', 'fr-GA', 'fr-GQ', 'fr-DJ', 'fr-KM', 'fr-VU', 'fr-NC', 'fr-PF',
      'it', 'it-IT', 'it-CH', 'it-SM', 'it-VA',
      'pt', 'pt-PT', 'pt-BR', 'pt-AO', 'pt-MZ', 'pt-GW', 'pt-CV', 'pt-ST', 'pt-TL', 'pt-MO',
      'ro', 'ro-RO', 'ro-MD',
      'ca', 'ca-ES', 'ca-AD', 'ca-FR', 'ca-IT'
    ],
    priority: 50,
    tokenize: unicodeTokenizer
  })

  // Enhanced Germanic languages tokenizer with expanded language support
  registry.register({
    id: 'faseeh-core-germanic-enhanced',
    name: 'Enhanced Germanic Languages Tokenizer',
    description: 'Comprehensive tokenizer for Germanic languages with compound word and umlaut support',
    languageCodes: [
      'de', 'de-DE', 'de-AT', 'de-CH', 'de-LU', 'de-LI', 'de-BE',
      'nl', 'nl-NL', 'nl-BE', 'nl-SR',
      'sv', 'sv-SE', 'sv-FI',
      'no', 'nb', 'nn', 'no-NO',
      'da', 'da-DK', 'da-GL',
      'is', 'is-IS',
      'fo', 'fo-FO',
      'af', 'af-ZA', 'af-NA',
      'fy', 'fy-NL'
    ],
    priority: 50,
    tokenize: unicodeTokenizer
  })

  // Enhanced Slavic languages tokenizer with expanded language support
  registry.register({
    id: 'faseeh-core-slavic-enhanced',
    name: 'Enhanced Slavic Languages Tokenizer',
    description: 'Comprehensive tokenizer for Slavic languages with Cyrillic and Latin script support',
    languageCodes: [
      'ru', 'ru-RU', 'ru-BY', 'ru-KZ', 'ru-KG', 'ru-MD', 'ru-UA',
      'uk', 'uk-UA',
      'be', 'be-BY',
      'bg', 'bg-BG',
      'mk', 'mk-MK',
      'sr', 'sr-RS', 'sr-ME', 'sr-BA', 'sr-Latn', 'sr-Cyrl',
      'hr', 'hr-HR', 'hr-BA',
      'bs', 'bs-BA',
      'sl', 'sl-SI',
      'cs', 'cs-CZ',
      'sk', 'sk-SK',
      'pl', 'pl-PL',
      'hsb', 'dsb' // Upper and Lower Sorbian
    ],
    priority: 50,
    tokenize: unicodeTokenizer
  })

  // Baltic languages tokenizer
  registry.register({
    id: 'faseeh-core-baltic',
    name: 'Baltic Languages Tokenizer',
    description: 'Tokenizer for Baltic languages with Latin script and diacritic support',
    languageCodes: ['lv', 'lv-LV', 'lt', 'lt-LT'],
    priority: 50,
    tokenize: unicodeTokenizer
  })

  // Celtic languages tokenizer
  registry.register({
    id: 'faseeh-core-celtic',
    name: 'Celtic Languages Tokenizer',
    description: 'Tokenizer for Celtic languages with Latin script variations',
    languageCodes: ['ga', 'ga-IE', 'gd', 'gd-GB', 'cy', 'cy-GB', 'br', 'br-FR', 'kw', 'gv'],
    priority: 50,
    tokenize: unicodeTokenizer
  })

  // Finno-Ugric languages tokenizer
  registry.register({
    id: 'faseeh-core-finno-ugric',
    name: 'Finno-Ugric Languages Tokenizer',
    description: 'Tokenizer for Finno-Ugric languages with agglutinative morphology support',
    languageCodes: [
      'fi', 'fi-FI',
      'et', 'et-EE',
      'hu', 'hu-HU',
      'sme', 'smj', 'sma', 'smn', 'sms' // Sami languages
    ],
    priority: 50,
    tokenize: unicodeTokenizer
  })

  // Turkic languages tokenizer
  registry.register({
    id: 'faseeh-core-turkic',
    name: 'Turkic Languages Tokenizer',
    description: 'Tokenizer for Turkic languages with Latin and Cyrillic script support',
    languageCodes: [
      'tr', 'tr-TR', 'tr-CY',
      'az', 'az-AZ',
      'kk', 'kk-KZ',
      'ky', 'ky-KG',
      'uz', 'uz-UZ',
      'tk', 'tk-TM',
      'tt', 'tt-RU',
      'ba', 'ba-RU',
      'cv', 'cv-RU'
    ],
    priority: 50,
    tokenize: unicodeTokenizer
  })

  // ========================================
  // IMPORTANT NOTES FOR COMPLEX LANGUAGES
  // ========================================

  /*
   * SPECIALIZED TOKENIZATION REQUIREMENTS:
   *
   * The following languages require sophisticated word segmentation algorithms
   * and should use specialized plugins with priority 1000+ for production use:
   *
   * 1. CHINESE (zh, zh-CN, zh-TW, zh-HK, zh-SG):
   *    - Requires word segmentation libraries like Jieba, THULAC, or Stanford CoreNLP
   *    - No spaces between words, complex compound word formation
   *    - Traditional vs Simplified character considerations
   *
   * 2. JAPANESE (ja, ja-JP):
   *    - Requires morphological analysis tools like MeCab, Kuromoji, or Sudachi
   *    - Mixed scripts: Hiragana, Katakana, Kanji, Latin
   *    - No spaces between words, complex grammatical particles
   *
   * 3. THAI (th, th-TH):
   *    - Requires word boundary detection algorithms
   *    - No spaces between words, complex script with tone marks
   *    - Libraries like ICU Boundary Analysis or specialized Thai NLP tools
   *
   * 4. KHMER (km, km-KH):
   *    - Similar to Thai, requires specialized word boundary detection
   *    - Complex script with no word separators
   *
   * 5. LAO (lo, lo-LA):
   *    - Similar to Thai, requires word boundary detection
   *    - No spaces between words
   *
   * 6. MYANMAR/BURMESE (my, my-MM):
   *    - Requires specialized segmentation for Myanmar script
   *    - Complex script with syllable-based writing
   *
   * The basic tokenizers provided here offer character-level or simple Unicode
   * tokenization as fallbacks, but production applications should install
   * appropriate language-specific plugins for these languages.
   */
}
