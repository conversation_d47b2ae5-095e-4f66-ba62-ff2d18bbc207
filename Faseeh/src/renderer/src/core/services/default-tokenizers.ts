import { TokenizerRegistry } from './text-tokenizer-registry'
import { 
  whitespaceTokenizer, 
  punctuationTokenizer, 
  unicodeTokenizer 
} from '../tokenizers/whitespace-tokenizer'

/**
 * Initializes and registers the default/core tokenizers that come with Faseeh
 * These provide basic tokenization functionality for any language
 */
export function initializeDefaultTokenizers(registry: TokenizerRegistry): void {
  // Register the basic whitespace tokenizer as the ultimate fallback
  registry.register({
    id: 'faseeh-core-whitespace',
    name: 'Basic Whitespace Tokenizer',
    description: 'Simple whitespace and punctuation-based tokenizer that works for any language',
    languageCodes: ['*'], // Universal fallback
    priority: -100, // Very low priority - only used when nothing else is available
    tokenize: whitespaceTokenizer
  })

  // Register the punctuation-aware tokenizer for better handling of structured text
  registry.register({
    id: 'faseeh-core-punctuation',
    name: 'Punctuation-Aware Tokenizer',
    description: 'Improved tokenizer that handles punctuation and contractions better',
    languageCodes: ['*'], // Universal fallback
    priority: -50, // Low priority but better than basic whitespace
    tokenize: punctuationTokenizer
  })

  // Register the Unicode-aware tokenizer for international text
  registry.register({
    id: 'faseeh-core-unicode',
    name: 'Unicode-Aware Tokenizer',
    description: 'Unicode-aware tokenizer that handles international characters and scripts',
    languageCodes: ['*'], // Universal fallback
    priority: 0, // Default priority - good general-purpose tokenizer
    tokenize: unicodeTokenizer
  })

  // Register language-specific variants of the Unicode tokenizer for common languages
  // These have higher priority for their specific languages

  // English - punctuation tokenizer works well
  registry.register({
    id: 'faseeh-core-english',
    name: 'English Tokenizer',
    description: 'Optimized tokenizer for English text',
    languageCodes: ['en', 'en-US', 'en-GB', 'en-CA', 'en-AU'],
    priority: 50,
    tokenize: punctuationTokenizer
  })

  // Romance languages - Unicode tokenizer handles accents well
  registry.register({
    id: 'faseeh-core-romance',
    name: 'Romance Languages Tokenizer',
    description: 'Tokenizer optimized for Romance languages with accent support',
    languageCodes: ['es', 'fr', 'it', 'pt', 'ro', 'ca'],
    priority: 50,
    tokenize: unicodeTokenizer
  })

  // Germanic languages - punctuation tokenizer with Unicode support
  registry.register({
    id: 'faseeh-core-germanic',
    name: 'Germanic Languages Tokenizer',
    description: 'Tokenizer optimized for Germanic languages',
    languageCodes: ['de', 'nl', 'sv', 'no', 'da'],
    priority: 50,
    tokenize: unicodeTokenizer
  })

  // Slavic languages - Unicode tokenizer for Cyrillic and Latin scripts
  registry.register({
    id: 'faseeh-core-slavic',
    name: 'Slavic Languages Tokenizer',
    description: 'Tokenizer optimized for Slavic languages with Cyrillic support',
    languageCodes: ['ru', 'uk', 'bg', 'sr', 'hr', 'cs', 'sk', 'pl'],
    priority: 50,
    tokenize: unicodeTokenizer
  })

  // Arabic script languages - Unicode tokenizer
  registry.register({
    id: 'faseeh-core-arabic',
    name: 'Arabic Script Tokenizer',
    description: 'Tokenizer for Arabic script languages',
    languageCodes: ['ar', 'fa', 'ur', 'he'],
    priority: 50,
    tokenize: unicodeTokenizer
  })

  // Note: For languages like Chinese, Japanese, Thai, etc. that don't use spaces
  // between words, plugins should provide specialized tokenizers with much higher
  // priority (e.g., 1000+) that use proper segmentation algorithms
}
