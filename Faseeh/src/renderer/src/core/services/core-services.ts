import { EventEmitterWrapper } from '@shared/utilities/event-system/event-emitter-wrapper'
import { 
  PluginEvents,
  VaultEvents,
  WorkspaceEvents
} from '@shared/types/event-types'
import { IStorageAPI } from '@shared/types/storage-api'
import { TokenizerRegistry } from './text-tokenizer-registry'
import { initializeDefaultTokenizers } from './default-tokenizers'
import { FaseehAppImpl } from '../faseeh-app'
import { BasePlugin } from '../plugins/plugin-types'

/**
 * Core services container that manages all the core application services
 * This is responsible for initializing and coordinating the core services
 */
export class CoreServices {
  public readonly tokenizerRegistry: TokenizerRegistry
  public readonly faseehApp: FaseehAppImpl

  constructor(
    storage: IStorageAPI,
    workspaceEvents: EventEmitterWrapper<WorkspaceEvents>,
    vaultEvents: EventEmitterWrapper<VaultEvents>,
    pluginEvents: EventEmitterWrapper<PluginEvents>
  ) {
    // Initialize the tokenizer registry
    this.tokenizerRegistry = new TokenizerRegistry()
    
    // Initialize default tokenizers
    initializeDefaultTokenizers(this.tokenizerRegistry)

    // Create a simple plugin manager interface for now
    const pluginManager = {
      getPlugin(id: string): BasePlugin | null {
        // TODO: Implement proper plugin manager integration
        console.warn(`Plugin manager not yet implemented. Requested plugin: ${id}`)
        return null
      }
    }

    // Get platform information
    const platform = this.detectPlatform()
    
    // Create the FaseehApp instance
    this.faseehApp = new FaseehAppImpl(
      {
        version: '0.1.0', // TODO: Get from package.json or build process
        platform
      },
      storage,
      this.tokenizerRegistry,
      pluginManager,
      workspaceEvents,
      vaultEvents,
      pluginEvents
    )
  }

  /**
   * Detect the current platform
   */
  private detectPlatform(): 'win' | 'mac' | 'linux' {
    const userAgent = navigator.userAgent.toLowerCase()
    
    if (userAgent.includes('win')) {
      return 'win'
    } else if (userAgent.includes('mac')) {
      return 'mac'
    } else {
      return 'linux'
    }
  }

  /**
   * Get the tokenizer registry instance
   */
  getTokenizerRegistry(): TokenizerRegistry {
    return this.tokenizerRegistry
  }

  /**
   * Get the FaseehApp instance for use by plugins
   */
  getFaseehApp(): FaseehAppImpl {
    return this.faseehApp
  }

  /**
   * Initialize all core services
   * This should be called during application startup
   */
  static async initialize(
    storage: IStorageAPI,
    workspaceEvents: EventEmitterWrapper<WorkspaceEvents>,
    vaultEvents: EventEmitterWrapper<VaultEvents>,
    pluginEvents: EventEmitterWrapper<PluginEvents>
  ): Promise<CoreServices> {
    const services = new CoreServices(storage, workspaceEvents, vaultEvents, pluginEvents)
    
    // Perform any async initialization here
    console.log('Core services initialized successfully')
    console.log(`Registered ${services.tokenizerRegistry.listRegisteredTokenizers().length} default tokenizers`)
    
    return services
  }

  /**
   * Cleanup all core services
   * This should be called during application shutdown
   */
  cleanup(): void {
    // Perform any necessary cleanup
    console.log('Core services cleaned up')
  }
}
