/**
 * Represents a single token identified in the text
 */
export interface Token {
  text: string;       // The actual token string (e.g., "Hello", ",", "世界")
  startIndex: number; // Start index in the original raw text (inclusive)
  endIndex: number;   // End index in the original raw text (inclusive)
  isWord: boolean;    // Heuristic: is this likely a word vs punctuation/whitespace?
}

/**
 * Metadata describing a Tokenizer's capabilities
 */
export interface TokenizerInfo {
  id: string;                  // Unique identifier (e.g., 'arabic-tokenizer')
  name: string;                // Human-readable name
  description?: string;         // Optional description
  languageCodes: string[];      // Array of supported language codes (e.g., ['ar', 'en'])
  priority?: number;           // Higher number = higher priority when multiple tokenizers match
}

/**
 * The registration object containing both metadata and the tokenize function
 */
export interface TokenizerRegistration extends TokenizerInfo {
  tokenize: TokenizerFunction;
}

/**
 * The core tokenizer function type that takes text and returns tokens
 * Can be either synchronous or asynchronous
 */
export type TokenizerFunction = (text: string) => Token[] | Promise<Token[]>;
