import { EventEmitterWrapper } from '@shared/utilities/event-system/event-emitter-wrapper'
import { 
  EventType,
  Handler,
  PluginEvents,
  VaultEvents,
  WorkspaceEvents
} from '@shared/types/event-types'
import { IStorageAPI } from '@shared/types/storage-api'
import { TokenizerRegistryFacade } from '@shared/types/text-tokenizer-types'
import { FaseehApp, BasePlugin } from './plugins/plugin-types'
import { TokenizerRegistry } from './services/text-tokenizer-registry'
import { TokenizerRegistryFacadeImpl } from './facades/tokenizer-registry-facade'

/**
 * Implementation of the FaseehApp interface
 * This is the main API object provided to plugins for interacting with the application
 */
export class FaseehAppImpl implements FaseehApp {
  public readonly appInfo: {
    readonly version: string
    readonly platform: 'win' | 'mac' | 'linux'
  }

  public readonly storage: IStorageAPI
  public readonly tokenizers: TokenizerRegistryFacade
  
  public readonly plugins: {
    getPlugin(id: string): BasePlugin | null
  }

  // Shared event emitters
  public readonly workspaceEvents: EventEmitterWrapper<WorkspaceEvents>
  public readonly vaultEvents: EventEmitterWrapper<VaultEvents>
  public readonly pluginEvents: EventEmitterWrapper<PluginEvents>

  constructor(
    appInfo: { version: string; platform: 'win' | 'mac' | 'linux' },
    storage: IStorageAPI,
    tokenizerRegistry: TokenizerRegistry,
    pluginManager: { getPlugin(id: string): BasePlugin | null },
    workspaceEvents: EventEmitterWrapper<WorkspaceEvents>,
    vaultEvents: EventEmitterWrapper<VaultEvents>,
    pluginEvents: EventEmitterWrapper<PluginEvents>
  ) {
    this.appInfo = appInfo
    this.storage = storage
    this.tokenizers = new TokenizerRegistryFacadeImpl(tokenizerRegistry)
    this.plugins = pluginManager
    this.workspaceEvents = workspaceEvents
    this.vaultEvents = vaultEvents
    this.pluginEvents = pluginEvents
  }
}
