import { 
  Token, 
  TokenizerInfo, 
  TokenizerRegistration, 
  TokenizerRegistryFacade 
} from '@shared/types/text-tokenizer-types'
import { TokenizerRegistry } from '../services/text-tokenizer-registry'

/**
 * Facade for the TokenizerRegistry that provides a controlled interface to plugins
 * This implements the TokenizerRegistryFacade interface and acts as a bridge
 * between the FaseehApp API and the internal TokenizerRegistry service
 */
export class TokenizerRegistryFacadeImpl implements TokenizerRegistryFacade {
  constructor(private registry: TokenizerRegistry) {}

  /**
   * Register a new tokenizer
   */
  register(registration: TokenizerRegistration): void {
    this.registry.register(registration)
  }

  /**
   * Unregister a tokenizer by ID
   */
  unregister(id: string): void {
    this.registry.unregister(id)
  }

  /**
   * Tokenize text using the best available tokenizer for the language
   * Uses '*' as default language if none specified
   */
  async tokenizeText(text: string, languageCode: string = '*'): Promise<Token[]> {
    return this.registry.tokenizeText(text, languageCode)
  }

  /**
   * List all registered tokenizers
   */
  listRegisteredTokenizers(): TokenizerInfo[] {
    return this.registry.listRegisteredTokenizers()
  }

  /**
   * Get a specific tokenizer by ID
   */
  getTokenizerById(id: string): TokenizerRegistration | null {
    return this.registry.getTokenizerById(id)
  }
}
