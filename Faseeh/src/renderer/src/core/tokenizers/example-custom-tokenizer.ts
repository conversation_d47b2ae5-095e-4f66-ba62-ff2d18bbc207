import { Token, TokenizerFunction } from '@shared/types/text-tokenizer-types'

/**
 * Example custom tokenizer for demonstration purposes
 * This shows how a plugin might implement a specialized tokenizer
 * 
 * This example tokenizer is designed for a hypothetical language that:
 * - Uses periods to separate words instead of spaces
 * - Uses exclamation marks for emphasis (not word separators)
 * - Has compound words connected by hyphens
 */
export const exampleCustomTokenizer: TokenizerFunction = (text: string): Token[] => {
  const tokens: Token[] = []
  
  if (!text || text.trim().length === 0) {
    return tokens
  }

  // Custom tokenization logic for our hypothetical language
  // Split on periods (word separators) but keep hyphens within words
  const segments = text.split('.')
  let currentIndex = 0
  
  for (let i = 0; i < segments.length; i++) {
    const segment = segments[i]
    
    if (segment.trim().length > 0) {
      // Process each segment to extract words and punctuation
      const segmentTokens = processSegment(segment, currentIndex)
      tokens.push(...segmentTokens)
    }
    
    // Add the period as a token if it's not the last segment
    if (i < segments.length - 1) {
      const periodIndex = currentIndex + segment.length
      tokens.push({
        text: '.',
        startIndex: periodIndex,
        endIndex: periodIndex,
        isWord: false
      })
      currentIndex = periodIndex + 1
    } else {
      currentIndex += segment.length
    }
  }
  
  return tokens
}

/**
 * Process a segment (text between periods) to extract tokens
 */
function processSegment(segment: string, baseIndex: number): Token[] {
  const tokens: Token[] = []
  
  // Regex to match words (including compound words with hyphens) and punctuation
  // In our hypothetical language:
  // - Words can contain letters, numbers, and hyphens
  // - Exclamation marks are kept with words for emphasis
  // - Other punctuation is separate
  const tokenRegex = /([a-zA-Z0-9]+(?:-[a-zA-Z0-9]+)*!*|[^\w\s-]|\s+)/g
  
  let match: RegExpExecArray | null
  
  while ((match = tokenRegex.exec(segment)) !== null) {
    const tokenText = match[0]
    const startIndex = baseIndex + match.index
    const endIndex = startIndex + tokenText.length - 1
    
    // Skip whitespace-only tokens
    if (/^\s+$/.test(tokenText)) {
      continue
    }
    
    // Determine if this is a word
    // In our language, words are alphanumeric with optional hyphens and exclamation marks
    const isWord = /^[a-zA-Z0-9]+(?:-[a-zA-Z0-9]+)*!*$/.test(tokenText)
    
    tokens.push({
      text: tokenText,
      startIndex,
      endIndex,
      isWord
    })
  }
  
  return tokens
}

/**
 * Example of how a plugin would register this tokenizer
 * This would typically be called in the plugin's onload() method
 */
export function registerExampleTokenizer(app: any): void {
  app.tokenizers.register({
    id: 'example-custom-tokenizer',
    name: 'Example Custom Language Tokenizer',
    description: 'Demonstration tokenizer for a hypothetical language with period-separated words',
    languageCodes: ['example', 'ex'], // Hypothetical language codes
    priority: 100, // Higher priority than default tokenizers for this language
    tokenize: exampleCustomTokenizer
  })
  
  console.log('Example custom tokenizer registered successfully')
}

/**
 * Example of how a plugin would unregister this tokenizer
 * This would typically be called in the plugin's onunload() method
 */
export function unregisterExampleTokenizer(app: any): void {
  app.tokenizers.unregister('example-custom-tokenizer')
  console.log('Example custom tokenizer unregistered')
}
