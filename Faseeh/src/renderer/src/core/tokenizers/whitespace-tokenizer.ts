import { Token, TokenizerFunction } from '@shared/types/text-tokenizer-types'

/**
 * Basic whitespace-based tokenizer that splits text on whitespace and punctuation
 * This serves as the fallback tokenizer for any language
 */
export const whitespaceTokenizer: TokenizerFunction = (text: string): Token[] => {
  const tokens: Token[] = []
  
  if (!text || text.trim().length === 0) {
    return tokens
  }

  // Regular expression to match words, punctuation, and whitespace
  // This captures:
  // - Word characters (letters, numbers, underscores, apostrophes within words)
  // - Individual punctuation marks
  // - Whitespace sequences
  const tokenRegex = /(\w+(?:'\w+)*|[^\w\s]|\s+)/g
  
  let match: RegExpExecArray | null
  
  while ((match = tokenRegex.exec(text)) !== null) {
    const tokenText = match[0]
    const startIndex = match.index
    const endIndex = startIndex + tokenText.length - 1
    
    // Skip empty matches
    if (tokenText.length === 0) {
      continue
    }
    
    // Determine if this is likely a word vs punctuation/whitespace
    const isWord = /^\w+(?:'\w+)*$/.test(tokenText)
    
    tokens.push({
      text: tokenText,
      startIndex,
      endIndex,
      isWord
    })
  }
  
  return tokens
}

/**
 * Simple punctuation-aware tokenizer that handles basic sentence structure
 * Better than whitespace-only for languages with clear word boundaries
 */
export const punctuationTokenizer: TokenizerFunction = (text: string): Token[] => {
  const tokens: Token[] = []
  
  if (!text || text.trim().length === 0) {
    return tokens
  }

  // More sophisticated regex that better handles punctuation
  // This captures:
  // - Words (including contractions and hyphenated words)
  // - Numbers (including decimals)
  // - Punctuation marks
  // - Whitespace (but we'll filter it out)
  const tokenRegex = /(\w+(?:[-']\w+)*|\d+(?:\.\d+)?|[^\w\s]|\s+)/g
  
  let match: RegExpExecArray | null
  
  while ((match = tokenRegex.exec(text)) !== null) {
    const tokenText = match[0]
    const startIndex = match.index
    const endIndex = startIndex + tokenText.length - 1
    
    // Skip whitespace-only tokens
    if (/^\s+$/.test(tokenText)) {
      continue
    }
    
    // Determine if this is likely a word
    const isWord = /^[\w][\w-']*[\w]$|^\w$/.test(tokenText) || /^\d+(?:\.\d+)?$/.test(tokenText)
    
    tokens.push({
      text: tokenText,
      startIndex,
      endIndex,
      isWord
    })
  }
  
  return tokens
}

/**
 * Unicode-aware tokenizer that handles international characters better
 * Good for languages with non-ASCII characters
 */
export const unicodeTokenizer: TokenizerFunction = (text: string): Token[] => {
  const tokens: Token[] = []
  
  if (!text || text.trim().length === 0) {
    return tokens
  }

  // Unicode-aware regex that handles international characters
  // \p{L} matches any Unicode letter
  // \p{N} matches any Unicode number
  // \p{M} matches any Unicode mark (accents, etc.)
  const tokenRegex = /([\p{L}\p{N}\p{M}]+(?:[-'][\p{L}\p{N}\p{M}]+)*|[\p{P}\p{S}]|\s+)/gu
  
  let match: RegExpExecArray | null
  
  while ((match = tokenRegex.exec(text)) !== null) {
    const tokenText = match[0]
    const startIndex = match.index
    const endIndex = startIndex + tokenText.length - 1
    
    // Skip whitespace-only tokens
    if (/^\s+$/.test(tokenText)) {
      continue
    }
    
    // Determine if this is likely a word (letters, numbers, marks)
    const isWord = /^[\p{L}\p{N}\p{M}][\p{L}\p{N}\p{M}-']*[\p{L}\p{N}\p{M}]$|^[\p{L}\p{N}\p{M}]$/u.test(tokenText)
    
    tokens.push({
      text: tokenText,
      startIndex,
      endIndex,
      isWord
    })
  }
  
  return tokens
}
