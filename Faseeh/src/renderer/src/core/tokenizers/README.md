# Faseeh Text Tokenizer System

This directory contains the text tokenization system for Faseeh, which provides language-aware text segmentation capabilities for the application.

## Overview

The text tokenizer system is responsible for breaking down raw text into individual tokens (words, punctuation, etc.) with positional information. This is crucial for features like:

- Interactive text display with clickable words
- Dictionary lookups on hover
- Vocabulary extraction
- Language learning analytics
- Spaced repetition system integration

## Architecture

The tokenizer system follows a plugin-based architecture with the following components:

### Core Components

1. **TokenizerRegistry** (`../services/text-tokenizer-registry.ts`)
   - Central registry for all tokenizer implementations
   - Handles tokenizer selection based on language and priority
   - Provides the main `tokenizeText()` API

2. **TokenizerRegistryFacade** (`../facades/tokenizer-registry-facade.ts`)
   - Controlled interface exposed to plugins through FaseehApp
   - Provides safe access to tokenizer functionality

3. **Default Tokenizers** (`../services/default-tokenizers.ts`)
   - Initializes core tokenizers that work for most languages
   - Provides fallback tokenization for any language

### Type Definitions

All tokenizer types are defined in `@shared/types/text-tokenizer-types.d.ts`:

- `Token` - Represents a single token with text and position
- `TokenizerFunction` - Function signature for tokenizer implementations
- `TokenizerInfo` - Metadata about a tokenizer
- `TokenizerRegistration` - Complete tokenizer registration object
- `ITokenizerRegistry` - Interface for the registry service
- `TokenizerRegistryFacade` - Interface exposed to plugins

## Built-in Tokenizers

### Core Tokenizers (`whitespace-tokenizer.ts`)

1. **Whitespace Tokenizer** (`whitespaceTokenizer`)
   - Basic tokenizer that splits on whitespace and punctuation
   - Universal fallback with very low priority (-100)
   - Works for any language but provides minimal sophistication

2. **Punctuation Tokenizer** (`punctuationTokenizer`)
   - Improved tokenizer that handles contractions and hyphenated words
   - Better sentence structure awareness
   - Low priority (-50) but better than basic whitespace

3. **Unicode Tokenizer** (`unicodeTokenizer`)
   - Unicode-aware tokenizer for international characters
   - Handles accents, diacritics, and non-ASCII scripts
   - Default priority (0) - good general-purpose tokenizer

### Language-Specific Tokenizers

The system automatically registers optimized tokenizers for common language families:

- **English** - Uses punctuation tokenizer (priority 50)
- **Romance Languages** (Spanish, French, Italian, Portuguese, Romanian, Catalan) - Uses Unicode tokenizer (priority 50)
- **Germanic Languages** (German, Dutch, Swedish, Norwegian, Danish) - Uses Unicode tokenizer (priority 50)
- **Slavic Languages** (Russian, Ukrainian, Bulgarian, Serbian, Croatian, Czech, Slovak, Polish) - Uses Unicode tokenizer (priority 50)
- **Arabic Script Languages** (Arabic, Persian, Urdu, Hebrew) - Uses Unicode tokenizer (priority 50)

## Plugin Integration

### Registering a Custom Tokenizer

Plugins can register custom tokenizers through the FaseehApp API:

```typescript
// In your plugin's onload() method
async onload() {
  this.app.tokenizers.register({
    id: 'my-chinese-tokenizer',
    name: 'Advanced Chinese Tokenizer',
    description: 'Specialized tokenizer for Chinese text using Jieba algorithm',
    languageCodes: ['zh', 'zh-CN', 'zh-TW'],
    priority: 1000, // High priority for Chinese text
    tokenize: this.chineseTokenizerFunction
  });
}

// Your tokenizer implementation
private chineseTokenizerFunction = (text: string): Token[] => {
  // Your custom tokenization logic here
  // Return array of Token objects
};
```

### Unregistering a Tokenizer

```typescript
// In your plugin's onunload() method
onunload() {
  this.app.tokenizers.unregister('my-chinese-tokenizer');
}
```

### Using the Tokenizer System

```typescript
// Tokenize text with automatic language detection
const tokens = await this.app.tokenizers.tokenizeText('Hello, world!');

// Tokenize text for a specific language
const tokens = await this.app.tokenizers.tokenizeText('你好世界', 'zh');

// List all registered tokenizers
const tokenizers = this.app.tokenizers.listRegisteredTokenizers();
```

## Priority System

Tokenizers are selected based on language support and priority:

1. **Exact Language Match** - Tokenizers that support the specific language code get +1000 bonus
2. **Wildcard Match** - Tokenizers that support '*' (any language) get +100 bonus
3. **Base Priority** - The tokenizer's configured priority value

The tokenizer with the highest total score is selected.

### Recommended Priority Ranges

- **Core/Fallback Tokenizers**: -100 to 0
- **General Language Family Tokenizers**: 1 to 100
- **Language-Specific Tokenizers**: 100 to 1000
- **Specialized/Advanced Tokenizers**: 1000+

## Token Structure

Each token contains:

```typescript
interface Token {
  text: string;       // The actual token text
  startIndex: number; // Start position in original text (inclusive)
  endIndex: number;   // End position in original text (inclusive)
  isWord: boolean;    // Whether this is likely a word vs punctuation
}
```

## Examples

See `example-custom-tokenizer.ts` for a complete example of implementing and registering a custom tokenizer.

## Language Support

For languages that don't use spaces between words (Chinese, Japanese, Thai, etc.), plugins should provide specialized tokenizers with high priority (1000+) that use proper segmentation algorithms like:

- Chinese: Jieba, THULAC, or similar
- Japanese: MeCab, Kuromoji, or similar  
- Thai: ICU boundary analysis or similar

The core tokenizers provide basic fallback functionality but are not suitable for production use with these languages.
