import '@renderer/common/assets/styles/main.css'
import { createApp, App as VueApp } from 'vue'
import { createPinia } from 'pinia'
import { createMemoryHistory, createRouter, Router } from 'vue-router'

import App from './App.vue'
import { routes } from '@renderer/common/router/routes'
import { workspaceEvents, vaultEvents, pluginEvents } from '@shared/constants/event-emitters'
import { CoreServices } from './core/services/core-services'

class RendererLifecycle {
  private app: VueApp | null = null
  private router: Router | null = null
  private coreServices: CoreServices | null = null

  async init(): Promise<void> {
    // Initialize core services first
    this.coreServices = await CoreServices.initialize(
      window.storageAPI,
      workspaceEvents,
      vaultEvents,
      pluginEvents
    )

    const pinia = createPinia()

    this.router = createRouter({
      history: createMemoryHistory(),
      routes
    })

    this.app = createApp(App)
    this.app.use(pinia)
    this.app.use(this.router)

    workspaceEvents.on('media:opened', (event) =>
      console.log('Media opened:', event.mediaId, 'from', event.source)
    )

    window.addEventListener('beforeunload', () => this.close())
  }

  run(): void {
    if (!this.app) {
      throw new Error('Application not initialized. Call init() first.')
    }
    this.app.mount('#app')
  }

  test(): void {
    if (import.meta.env.DEV) {
      // Test event system
      workspaceEvents.emit('media:opened', { mediaId: '12345', source: 'local' })

      // Test storage API
      this.testStorage()

      // Test tokenizer system
      this.testTokenizers()
    }
  }

  close(): void {
    workspaceEvents.clearAllHandlers()

    // Cleanup core services
    if (this.coreServices) {
      this.coreServices.cleanup()
    }
  }

  private async testStorage(): Promise<void> {
    try {
      console.log(await window.storageAPI.listPluginDirectories())

      // await window.storageAPI.setAppSetting({
      //   key: 'testKey',
      //   value: JSON.stringify([1, 2, 3, 4, 5])
      // })

      // const setting = await window.storageAPI.getAppSetting('testKey')
      // if (setting) {
      //   console.log('Value from storage:', JSON.parse(setting.value))
      // } else {
      //   console.log('Setting "testKey" not found.')
      // }
    } catch (error) {
      console.error('Storage service test failed:', error)
    }
  }

  private async testTokenizers(): Promise<void> {
    if (!this.coreServices) {
      console.error('Core services not initialized')
      return
    }

    try {
      console.log('Testing tokenizer system...')

      const faseehApp = this.coreServices.getFaseehApp()

      // Test basic tokenization
      const testTexts = [
        'Hello, world! This is a test.',
        'Bonjour le monde! Ceci est un test.',
        'Hola mundo! Esta es una prueba.',
        'Привет мир! Это тест.',
        'مرحبا بالعالم! هذا اختبار.'
      ]

      for (const text of testTexts) {
        console.log(`\nTokenizing: "${text}"`)
        const tokens = await faseehApp.tokenizers.tokenizeText(text)
        console.log('Tokens:', tokens.map(t => ({ text: t.text, isWord: t.isWord })))
      }

      // Test language-specific tokenization
      console.log('\nTesting language-specific tokenization:')
      const englishTokens = await faseehApp.tokenizers.tokenizeText('Hello, world!', 'en')
      console.log('English tokens:', englishTokens.map(t => t.text))

      const frenchTokens = await faseehApp.tokenizers.tokenizeText('Bonjour le monde!', 'fr')
      console.log('French tokens:', frenchTokens.map(t => t.text))

      // List registered tokenizers
      console.log('\nRegistered tokenizers:')
      const tokenizers = faseehApp.tokenizers.listRegisteredTokenizers()
      tokenizers.forEach(t => console.log(`- ${t.name} (${t.id}): ${t.languageCodes.join(', ')}`))

    } catch (error) {
      console.error('Tokenizer test failed:', error)
    }
  }
}

const renderer = new RendererLifecycle()

// Initialize and run the application
async function startApp() {
  try {
    await renderer.init()
    renderer.run()
    renderer.test()
  } catch (error) {
    console.error('Failed to start application:', error)
  }
}

startApp()
