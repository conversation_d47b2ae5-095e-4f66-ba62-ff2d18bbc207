// Simple test for the tokenizer system that can be run independently
import { TokenizerRegistry } from './core/services/text-tokenizer-registry'
import { initializeDefaultTokenizers } from './core/services/default-tokenizers'

// Simple test function
export async function testTokenizerSystem(): Promise<string> {
  try {
    console.log('Creating tokenizer registry...')
    const registry = new TokenizerRegistry()
    
    console.log('Initializing default tokenizers...')
    initializeDefaultTokenizers(registry)
    
    console.log('Testing tokenization...')
    
    // Test basic tokenization
    const testTexts = [
      'Hello, world! This is a test.',
      'Bonjour le monde! Ceci est un test.',
      'Hola mundo! Esta es una prueba.'
    ]
    
    const results = []
    
    for (const text of testTexts) {
      const tokens = await registry.tokenizeText(text)
      results.push(`"${text}" -> ${tokens.length} tokens`)
      console.log(`Tokenized "${text}":`, tokens.map(t => ({ text: t.text, isWord: t.isWord })))
    }
    
    // Test language-specific tokenization
    const englishTokens = await registry.tokenizeText('Hello, world!', 'en')
    const frenchTokens = await registry.tokenizeText('Bonjour le monde!', 'fr')
    
    results.push(`English tokenization: ${englishTokens.length} tokens`)
    results.push(`French tokenization: ${frenchTokens.length} tokens`)
    
    // List registered tokenizers
    const tokenizers = registry.listRegisteredTokenizers()
    results.push(`Registered tokenizers: ${tokenizers.length}`)
    
    console.log('Registered tokenizers:')
    tokenizers.forEach(t => console.log(`- ${t.name} (${t.id}): ${t.languageCodes.join(', ')}`))
    
    const summary = `✅ Tokenizer test completed successfully!\n\n${results.join('\n')}`
    console.log(summary)
    return summary
    
  } catch (error) {
    const errorMsg = `❌ Tokenizer test failed: ${error.message}`
    console.error(errorMsg, error)
    return errorMsg
  }
}

// Make it available globally for testing
if (typeof window !== 'undefined') {
  (window as any).testTokenizerSystem = testTokenizerSystem
}
