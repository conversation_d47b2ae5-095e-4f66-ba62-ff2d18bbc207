@import url('@renderer/common/assets/styles/faseeh-dark.css');
@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

[data-theme='dark'] {
  /* --- MAP SHADCN/UI VARIABLES TO FASEEH VARIABLES --- */
  --background: var(--faseeh-background-base);
  --foreground: var(--faseeh-text-primary);
  --card: var(--faseeh-background-secondary); /* Map Card to Secondary Bg */
  --card-foreground: var(--faseeh-text-primary);
  --popover: var(--faseeh-background-primary); /* Popover often matches main bg */
  --popover-foreground: var(--faseeh-text-primary);
  --primary: var(--faseeh-accent-primary);
  --primary-foreground: var(--faseeh-text-on-accent-primary);
  --secondary: var(--faseeh-accent-secondary); /* Map to your secondary accent */
  --secondary-foreground: var(--faseeh-text-on-accent-secondary);
  --muted: var(--faseeh-background-tertiary); /* Map Muted bg */
  --muted-foreground: var(--faseeh-text-muted);
  --accent: var(--faseeh-interactive-background-hover); /* Map Accent bg */
  --accent-foreground: var(--faseeh-text-secondary); /* Map Accent text */
  --destructive: var(--faseeh-color-destructive);
  --destructive-foreground: var(--faseeh-text-on-destructive);
  --border: var(--faseeh-border-primary);
  --input: var(--faseeh-border-primary); /* Input border often same as primary border */
  --ring: var(--faseeh-border-interactive); /* Focus ring color */
  --radius: var(--faseeh-radius-md); /* Base radius */

  /* Chart & Sidebar colors - map them to your semantic vars as needed */
  /* Example - you might need more specific --faseeh vars for these */
  --chart-1: var(--faseeh-color-cyan-mid);
  --chart-2: var(--faseeh-color-green-mid);
  /* ... map chart 3, 4, 5 ... */
  --sidebar: var(--faseeh-background-secondary);
  --sidebar-foreground: var(--faseeh-text-primary);
  --sidebar-primary: var(--faseeh-accent-primary); /* Or a different sidebar accent */
  --sidebar-primary-foreground: var(--faseeh-text-on-accent-primary);
  --sidebar-accent: var(--faseeh-interactive-background-hover);
  --sidebar-accent-foreground: var(--faseeh-text-primary);
  --sidebar-border: var(--faseeh-border-secondary);
  --sidebar-ring: var(--faseeh-border-interactive);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--reka-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--reka-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
