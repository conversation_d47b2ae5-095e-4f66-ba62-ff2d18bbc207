@import './base.css';
@import 'tailwindcss';
@plugin "@iconify/tailwind4";

/* -------------------------------------------------------------------------- */
/*                                   Button                                   */
/* -------------------------------------------------------------------------- */

@utility faseeh-button {
  @apply inline-flex items-center justify-center px-4 py-2 rounded-md;
  @apply text-sm font-medium transition-colors;
  @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2;
  background-color: var(--faseeh-interactive-background);
  color: var(--faseeh-interactive-text);
  border: 1px solid var(--faseeh-interactive-border);
  &:hover {
    background-color: var(--faseeh-interactive-background-hover);
  }
  &:focus-visible {
    border-color: var(--faseeh-border-interactive);
    box-shadow:
      0 0 0 2px var(--faseeh-background-primary),
      0 0 0 4px var(--faseeh-border-interactive);
  }
}

@utility faseeh-button-primary {
  @apply faseeh-button;
  @apply bg-primary text-primary-foreground border-transparent;
  &:hover {
    background-color: var(--faseeh-button-primary-background-hover);
  }
}

/* -------------------------------------------------------------------------- */
/*                                   Spacer                                   */
/* -------------------------------------------------------------------------- */

@utility faseeh-spacer {
  @apply flex-grow;
}

/* -------------------------------------------------------------------------- */
/*                                  TitleBar                                  */
/* -------------------------------------------------------------------------- */
@utility faseeh-titlebar {
  @apply flex items-center;
  @apply h-9 w-full;
  -webkit-app-region: drag;
  > *:not(.faseeh-titlebar__tabs) {
    @apply h-full;
    @apply border-b-1 border-input;
  }
}

@utility faseeh-titlebar__logo {
  @apply h-full px-1.5 py-1;
}

@utility faseeh-titlebar__window-controls {
  @apply flex items-center;
  @apply h-full w-fit;
  -webkit-app-region: no-drag;
}

@utility faseeh-titlebar__window-controls__button {
  @apply flex items-center justify-center;
  @apply h-full w-10;
  &:hover {
    background-color: var(--faseeh-interactive-background-hover);
  }

  > span {
    @apply h-5 w-5;
    @apply text-accent-foreground;
    @apply group-hover:text-primary-foreground;
  }

  &:hover > * {
    @apply text-primary-foreground;
  }
}

@utility faseeh-titlebar__window-controls__button--danger {
  @apply faseeh-titlebar__window-controls__button;
  &:hover {
    @apply bg-destructive;
  }
}

@utility faseeh-titlebar__tabs {
  @apply flex-grow flex;
}

@utility faseeh-titlebar__tabs__item {
  @apply flex-grow flex justify-between items-center;
  @apply px-3;
  @apply border-input;
  @apply last:border-b-1;
  @apply last:border-r-1;
  @apply last:nth-[2]:border-l-1;
  @apply not-first:not-last:border-x-1;
  -webkit-app-region: no-drag;
  &:hover {
    @apply bg-accent;
    @apply border-b-0;
  }
}

/* -------------------------------------------------------------------------- */
/*                                   SideBar                                  */
/* -------------------------------------------------------------------------- */

@utility faseeh-sidebar {
  @apply flex flex-col items-center space-y-4;
  @apply h-full w-12 px-1;
  border-right: var(--faseeh-border-width) solid var(--faseeh-border-primary);
}

@utility faseeh-sidebar__logo {
  @apply h-9 px-1.5 pt-1 mb-5;
}

@utility faseeh-sidebar__button {
  @apply flex flex-col items-center justify-center;
  @apply w-8 h-8;
  @apply rounded-lg;
  @apply text-sidebar-foreground;
  @apply relative;

  &:hover {
    @apply bg-sidebar-accent text-sidebar-accent-foreground;
  }

  &.active {
    @apply bg-sidebar-primary text-sidebar-primary-foreground;
  }

  > span {
    @apply h-5 w-5;
  }
}

@utility faseeh-divider--horizontal {
  @apply w-full h-px;
  @apply bg-sidebar-border;
}

@utility faseeh-divider--vertical {
  @apply w-px h-full;
  @apply bg-sidebar-border;
}

/* -------------------------------------------------------------------------- */
/*                                 FilterMenu                                 */
/* -------------------------------------------------------------------------- */
@utility faseeh-filter-menu__icon {
  @apply mr-2 size-4;
}

@utility faseeh-filter-menu__content {
  @apply w-auto;
}
