#!/usr/bin/env node

/**
 * Improved Arabic tokenizer that detects words properly
 */

// Mock Token class
class Token {
  constructor(text, startIndex, endIndex, isWord) {
    this.text = text;
    this.startIndex = startIndex;
    this.endIndex = endIndex;
    this.isWord = isWord;
  }
}

// Improved Arabic word tokenizer
function arabicWordTokenizer(text) {
  const tokens = [];
  
  if (!text || text.trim().length === 0) {
    return tokens;
  }

  // Arabic word tokenization regex
  // This regex matches:
  // - Arabic words (sequences of Arabic letters)
  // - Punctuation marks
  // - Numbers
  // - Non-Arabic words
  const tokenRegex = /([\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+|[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s]+|\S)/g;
  
  let match;
  
  while ((match = tokenRegex.exec(text)) !== null) {
    const tokenText = match[0];
    const startIndex = match.index;
    const endIndex = startIndex + tokenText.length - 1;
    
    // Determine if this is a word or punctuation
    const isArabicWord = /^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+$/.test(tokenText);
    const isLatinWord = /^[a-zA-Z]+$/.test(tokenText);
    const isNumber = /^\d+$/.test(tokenText);
    
    const isWord = isArabicWord || isLatinWord || isNumber;
    
    tokens.push(new Token(tokenText, startIndex, endIndex, isWord));
  }
  
  return tokens;
}

// Simple registry
class TokenizerRegistry {
  constructor() {
    this.tokenizers = new Map();
  }

  register(registration) {
    this.tokenizers.set(registration.id, registration);
  }

  async tokenizeText(text, languageCode = 'ar') {
    const tokenizer = this.tokenizers.get('faseeh-core-arabic-words');
    return tokenizer.tokenize(text);
  }
}

// Test function
async function testArabicWordTokens() {
  const registry = new TokenizerRegistry();
  
  // Register improved Arabic word tokenizer
  registry.register({
    id: 'faseeh-core-arabic-words',
    name: 'Arabic Word Tokenizer',
    languageCodes: ['ar'],
    priority: 300,
    tokenize: arabicWordTokenizer
  });

  // Arabic test text with clear word boundaries
  const arabicText = 'مرحبا بالعالم! هذا اختبار للنظام.';
  
  console.log('Arabic Text:', arabicText);
  console.log('Translation: Hello world! This is a test for the system.');
  console.log('\nDetected Tokens:');
  
  const tokens = await registry.tokenizeText(arabicText, 'ar');
  
  tokens.forEach((token, index) => {
    const type = token.isWord ? 'WORD' : 'PUNCT';
    console.log(`${index + 1}. "${token.text}" [${type}]`);
  });
  
  console.log('\nWords only:');
  const words = tokens.filter(t => t.isWord);
  words.forEach((word, index) => {
    console.log(`${index + 1}. "${word.text}"`);
  });
}

// Run test
testArabicWordTokens();
