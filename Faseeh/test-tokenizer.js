// Simple Node.js test for the tokenizer system
// This tests the core tokenizer functions without the full Electron environment

// Mock the Token interface
class Token {
  constructor(text, startIndex, endIndex, isWord) {
    this.text = text;
    this.startIndex = startIndex;
    this.endIndex = endIndex;
    this.isWord = isWord;
  }
}

// Basic whitespace tokenizer implementation
function whitespaceTokenizer(text) {
  const tokens = [];
  
  if (!text || text.trim().length === 0) {
    return tokens;
  }

  // Regular expression to match words, punctuation, and whitespace
  const tokenRegex = /(\w+(?:'\w+)*|[^\w\s]|\s+)/g;
  
  let match;
  
  while ((match = tokenRegex.exec(text)) !== null) {
    const tokenText = match[0];
    const startIndex = match.index;
    const endIndex = startIndex + tokenText.length - 1;
    
    // Skip empty matches
    if (tokenText.length === 0) {
      continue;
    }
    
    // Determine if this is likely a word vs punctuation/whitespace
    const isWord = /^\w+(?:'\w+)*$/.test(tokenText);
    
    tokens.push(new Token(tokenText, startIndex, endIndex, isWord));
  }
  
  return tokens;
}

// Test the tokenizer
console.log('Testing tokenizer system...');

const testTexts = [
  'Hello, world! This is a test.',
  'Bonjour le monde! Ceci est un test.',
  'Hola mundo! Esta es una prueba.',
];

for (const text of testTexts) {
  console.log(`\nTokenizing: "${text}"`);
  const tokens = whitespaceTokenizer(text);
  console.log('Tokens:', tokens.map(t => ({ text: t.text, isWord: t.isWord })));
}

console.log('\nTokenizer test completed successfully!');
