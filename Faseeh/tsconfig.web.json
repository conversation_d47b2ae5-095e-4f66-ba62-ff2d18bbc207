{"extends": "@electron-toolkit/tsconfig/tsconfig.web.json", "include": ["src/renderer/src/env.d.ts", "src/renderer/src/**/*", "src/renderer/src/**/*.vue", "src/preload/*.d.ts", "src/shared/**/*"], "compilerOptions": {"types": ["vite/client"], "composite": true, "baseUrl": ".", "paths": {"@root/*": ["*"], "@renderer/*": ["src/renderer/src/*"], "@main/*": ["src/main/*"], "@shared/*": ["src/shared/*"], "@preload/*": ["src/preload/*"]}}}