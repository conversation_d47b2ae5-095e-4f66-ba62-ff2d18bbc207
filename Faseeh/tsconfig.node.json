{
  "extends": "@electron-toolkit/tsconfig/tsconfig.node.json",
  "include": [
    "electron.vite.config.*",
    "src/main/**/*",
    "src/preload/**/*",
    "src/main/db/migrations",
    "src/shared/**/*",
  ],
  "compilerOptions": {
    "composite": true,
    "strict": true,
    "moduleResolution": "bundler",
    "types": ["electron-vite/node", "vite/client"],
    "baseUrl": ".",
    "paths": {
      "@root/*": ["*"],
      "@renderer/*": ["src/renderer/src/*"],
      "@main/*": ["src/main/*"],
      "@shared/*": ["src/shared/*"],
      "@preload/*": ["src/preload/*"]
    }
  }
}
